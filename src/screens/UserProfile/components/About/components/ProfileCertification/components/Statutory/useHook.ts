import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import {
  selectAboutProfileCertificationsStatutoryCount,
  selectAboutProfileStatutoryCertifications,
} from '@/src/redux/selectors/about';
import { BottomTabNavigationI } from '@/src/navigation/types';

export const useProfileStatutory = (profileId: string, isUserProfile: boolean) => {
  const certifications = useSelector(selectAboutProfileStatutoryCertifications);
  const count = useSelector(selectAboutProfileCertificationsStatutoryCount);
  const navigation = useNavigation<BottomTabNavigationI>();

  const onListCertifications = () => {
    navigation.navigate(isUserProfile ? 'ProfileStack' : 'HomeStack', {
      screen: 'EditCertificationList',
      params: {
        editable: false,
        tab: 'statutory',
        profileId: profileId,
      },
    });
  };

  return {
    certifications,
    count,
    onListCertifications,
  };
};
