/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { FlatList } from 'react-native';

export interface CommentsHeadProps {
  postId: string;
  type: 'USER_POST' | 'SCRAPBOOK_POST';
  onPostLoaded: () => void;
  portUnLocode?: string;
  parentScrollRef?: React.RefObject<FlatList | null>;
  postIndex?: number;
}
