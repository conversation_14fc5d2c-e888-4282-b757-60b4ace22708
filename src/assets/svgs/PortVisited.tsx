/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Circle, ClipPath, Defs, G, Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const PortVisited: React.FC<FilledIconPropsI> = ({ width = 5.456, height = 5.58, ...props }) => {
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 44 45"
      fill="none"
      {...props}
    >
      <Circle cx={22} cy={22.4707} r={21.5} fill="#F2F9E5" stroke="#DDEFC8" />
      <G clipPath="url(#clip0_638_2672)">
        <Path
          d="M32.394 19.688l-17.25-6a.75.75 0 00-.996.708v16.5a.75.75 0 001.5 0V26.93l16.746-5.825a.75.75 0 000-1.417zm-16.746 5.654v-9.89l14.217 4.944-14.217 4.946z"
          fill="#448600"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_638_2672">
          <Path fill="#fff" transform="translate(10 10.47)" d="M0 0H24V24H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
};

export default PortVisited;
