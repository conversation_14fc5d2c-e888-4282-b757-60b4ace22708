/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
export interface OptionSearchResultI {
  name: string;
  id: string;
  dataType: 'raw' | 'master';
}

export interface CountryOptionSearchResultI {
  iso2: string;
  name: string;
}

export interface OptionAddBodyI {
  name: string;
  type?: 'STATUTORY' | 'VALUE_ADDED' | 'EDUCATION' | 'COMPANY' | 'MARITIME' | 'OTHER';
  website?: string;
  category?: string;
  hasFuelType?: boolean;
}

export interface OptionAddResultI {
  name: string;
  id: string;
  dataType: 'raw' | 'master';
}

export type EntitySearchResponse = {
  data: OptionSearchResultI[] | CountryOptionSearchResultI[] | ShipEntitySearchResponse[];
  total: number;
};

export type OptionSearchBodyI = {
  search: string;
  page: string;
};

type ShipEntitySearchResponse = {
  dataType: string;
  imageUrl: string;
  imo: string;
  matchedName: string;
  name: string;
};
