import { Pressable, Text, View } from 'react-native';
import UserAvatar from '@/src/components/UserAvatar';
import { CommentPreviewProps } from './types';

const CommentPreview = ({ comment, handleClose }: CommentPreviewProps) => {
  return (
    <View className="bg-gray-50 border-b border-gray-200 p-3">
      <View className="flex-row justify-between items-start">
        <Text className="text-xs text-gray-600 font-medium mb-2">Replying to</Text>
        <Pressable onPress={handleClose}>
          <Text className="text-gray-400 text-lg">×</Text>
        </Pressable>
      </View>

      <View className="flex-row gap-2">
        <UserAvatar
          avatarUri={comment.Profile?.avatar}
          name={comment.Profile.name}
          height={24}
          width={24}
        />
        <View className="flex-1">
          <Text className="text-xs font-medium text-gray-700">{comment.Profile?.name}</Text>
          <Text className="text-xs text-gray-600 mt-1" numberOfLines={2}>
            {comment.text}
          </Text>
        </View>
      </View>
    </View>
  );
};

export default CommentPreview;
