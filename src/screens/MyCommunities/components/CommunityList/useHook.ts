import React, { useEffect, useState } from 'react';
import { showToast } from '@/src/utilities/toast';
import { fetchOwnCommunities } from '@/src/networks/community/community';
import { CommunityFetchForClientI, FetchCommunityClientI } from '@/src/networks/community/types';

const useMyCommunities = () => {
  const [communities, setCommunities] = useState<FetchCommunityClientI[]>([]);
  const [lastCursorId, setLastCursorId] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState<boolean>(true);

  const fetchCommunities = async (query: CommunityFetchForClientI) => {
    try {
      const response = await fetchOwnCommunities(query);
      setCommunities((prev) => (query.cursorId ? [...prev, ...response] : response));
      if (response.length > 0) {
        setLastCursorId(response[response.length - 1].cursorId);
        setHasMore(true);
      } else {
        setLastCursorId(null);
        setHasMore(false);
      }
    } catch (err) {
      showToast({
        type: 'error',
        message: 'Failed to fetch communities',
      });
    }
  };

  const loadMore = async () => {
    if (!hasMore || lastCursorId === null) {
      return;
    }
    await fetchCommunities({
      cursorId: parseInt(lastCursorId),
    });
  };

  useEffect(() => {
    fetchCommunities({
      cursorId: null,
    });
  }, []);

  return {
    communities,
    loadMore,
  };
};

export default useMyCommunities;
