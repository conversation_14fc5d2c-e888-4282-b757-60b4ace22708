/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
export const debounceAsync = <T extends (...args: unknown[]) => Promise<unknown>>(
  callback: T,
  delay: number,
): ((...args: Parameters<T>) => Promise<ReturnType<T>>) => {
  let timeoutId: ReturnType<typeof setTimeout> | null = null;

  return (...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    return new Promise<ReturnType<T>>((resolve) => {
      timeoutId = setTimeout(async () => {
        try {
          const result = await callback(...args);
          resolve(result as ReturnType<T> | PromiseLike<ReturnType<T>>);
        } catch (_error) {
          return;
        } finally {
          timeoutId = null;
        }
      }, delay);
    });
  };
};
