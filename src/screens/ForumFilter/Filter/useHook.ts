/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { selectForumQuestionsFilters } from '@/src/redux/selectors/question';
import { selectSelectionByKey, selectMultipleSelectionsByKey } from '@/src/redux/selectors/search';
import { clearSelection } from '@/src/redux/slices/entitysearch/searchSlice';
import type { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import {
  setForumQuestionsFilters,
  clearForumQuestions,
  fetchForumQuestions,
} from '@/src/redux/slices/question/questionSlice';
import type { ForumQuestionsFiltersI } from '@/src/redux/slices/question/types';
import type { AppDispatch } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import { UseForumFilterResultI } from './types';

const useForumFilter = (): UseForumFilterResultI => {
  const dispatch = useDispatch<AppDispatch>();
  const navigation = useNavigation();
  const filters = useSelector(selectForumQuestionsFilters);
  const departmentSelection = useSelector(selectSelectionByKey('filterDepartment'));
  const qnaTopicsSelection = useSelector(selectMultipleSelectionsByKey('filterTopic')) as
    | SearchResultI[]
    | null;
  const equipmentSelection = useSelector(selectSelectionByKey('filterEquipmentCategory'));
  const makeSelection = useSelector(selectSelectionByKey('filterEquipmentManufacturer'));
  const modelSelection = useSelector(selectSelectionByKey('filterEquipmentModel'));

  const updateFilter = (key: keyof ForumQuestionsFiltersI, value: any) => {
    dispatch(setForumQuestionsFilters({ [key]: value }));
  };

  const addTopic = (topic: SearchResultI) => {
    const existingTopics = filters.topics || [];
    if (existingTopics.length < 3 && !existingTopics.find((t) => t.id === topic.id)) {
      dispatch(
        setForumQuestionsFilters({
          topics: [...existingTopics, topic],
        }),
      );
    }
  };

  const removeTopic = (topicId: string) => {
    const updatedTopics = filters.topics.filter((topic) => topic.id !== topicId);
    dispatch(setForumQuestionsFilters({ topics: updatedTopics }));
  };

  const setEquipmentCategory = (equipment: SearchResultI | null) => {
    dispatch(setForumQuestionsFilters({ equipmentCategory: equipment }));
  };

  const setEquipmentManufacturer = (manufacturer: SearchResultI | null) => {
    dispatch(setForumQuestionsFilters({ equipmentManufacturer: manufacturer }));
  };

  const setEquipmentModel = (model: SearchResultI | null) => {
    dispatch(setForumQuestionsFilters({ equipmentModel: model }));
  };

  const applyFilters = async (pendingFilters: ForumQuestionsFiltersI) => {
    dispatch(setForumQuestionsFilters(pendingFilters));

    dispatch(clearForumQuestions());

    try {
      await dispatch(fetchForumQuestions({ refresh: true })).unwrap();
      showToast({ message: 'Filters applied successfully', type: 'success' });

      navigation.goBack();
    } catch (error) {
      showToast({ message: 'Failed to apply filters', type: 'error' });
    }
  };

  const clearAllFilters = () => {
    dispatch(
      setForumQuestionsFilters({
        type: 'ALL',
        isLive: false,
        myRecommended: false,
        myQuestion: false,
        myAnswered: false,
        myCommunity: false,
        department: null,
        topics: [],
        equipmentCategory: null,
        equipmentManufacturer: null,
        equipmentModel: null,
      }),
    );

    // Clear entity search selections
    dispatch(clearSelection('filterDepartment'));
    dispatch(clearSelection('filterTopics'));
    dispatch(clearSelection('filterEquipmentCategory'));
    dispatch(clearSelection('filterEquipmentManufacturer'));
    dispatch(clearSelection('filterEquipmentModel'));

    showToast({ message: 'Filters cleared successfully', type: 'success' });
  };

  return {
    filters,
    departmentSelection,
    qnaTopicsSelection,
    equipmentSelection,
    makeSelection,
    modelSelection,
    updateFilter,
    addTopic,
    removeTopic,
    setEquipmentCategory,
    setEquipmentManufacturer,
    setEquipmentModel,
    applyFilters,
    clearAllFilters,
  };
};

export default useForumFilter;
