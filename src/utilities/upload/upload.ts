import { MediaI } from '@/src/screens/CreateContent/components/CreatePostForm/types';
import { showToast } from '../toast';

export const uriToBlob = (uri: string): Promise<Blob> => {
  const adjustedUri = uri.startsWith('file:///') ? uri.replace('file:///', 'file:/') : uri;

  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();

    xhr.onload = function () {
      resolve(xhr.response);
    };

    xhr.onerror = function (e) {
      reject(new Error('uriToBlob failed'));
    };

    xhr.responseType = 'blob';
    xhr.open('GET', adjustedUri, true);
    xhr.send(null);
  });
};

export const uploadFileWithPresignedUrl = async (file: MediaI, uploadUrl: string) => {
  try {
    const blob = await uriToBlob(file.uri);
    const uploadResponse = await fetch(uploadUrl, {
      method: 'PUT',
      body: blob,
      headers: {
        'Content-Type': file.type || 'application/octet-stream',
        'x-amz-acl': 'public-read',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'PUT, POST, GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Cache-Control': 'no-cache',
        'Content-Length': blob.size.toString(),
      },
      mode: 'cors',
      credentials: 'same-origin',
    });

    if (!uploadResponse.ok) {
      let errorText = '';
      try {
        errorText = await uploadResponse.text();
      } catch (e) {
        errorText = 'Could not retrieve error details';
      }
      showToast({
        type: 'error',
        message: 'Upload Failed',
        description: `Status: ${uploadResponse.status}. Please try again or check your connection.`,
      });
      throw new Error(`Upload failed with status: ${uploadResponse.status}. Details: ${errorText}`);
    }

    return true;
  } catch (error) {
    showToast({
      type: 'error',
      message: 'Upload Error',
      description: error instanceof Error ? error.message : 'Unknown upload error occurred',
    });
    throw error;
  }
};
