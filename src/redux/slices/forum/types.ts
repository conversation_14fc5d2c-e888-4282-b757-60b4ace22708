import type { ForumAnswerWithProfileI, ForumAnswerDetailI } from '@/src/networks/answer/types';
import type { ProfileExternalI } from '@/src/networks/answerVote/types';
import type {
  QuestionCommentI,
  QuestionCommentReplyI,
  AnswerCommentI,
  AnswerCommentReplyI,
} from '@/src/networks/forum/types';
import type { ForumQuestionDetailWithAnswersI } from '@/src/networks/question/types';

export type ForumState = {
  question: ForumQuestionDetailWithAnswersI | null;
  loading: boolean;
  error: string | null;
  answers: ForumAnswerWithProfileI[];
  total: number;
  nextCursorId: number | null;
  canUpdateStatus: boolean;
  answerLoading: boolean;
  answerError: string | null;
  answerDetail: ForumAnswerDetailI | null;
  answerVotes: ProfileExternalI[];
  answerVotesTotal: number;
  answerVotesNextCursorId: number | null;
  answerVoteLoading: boolean;
  answerVoteError: string | null;
  comments: {
    [key: string]: {
      comments: QuestionCommentI[];
      total: number;
      nextCursorId: number | null;
    };
  };
  commentReplies: {
    [key: string]: {
      comments: QuestionCommentReplyI[];
      total: number;
      nextCursorId: number | null;
    };
  };
  answerComments: {
    [key: string]: {
      comments: AnswerCommentI[];
      total: number;
      nextCursorId: number | null;
    };
  };
  answerCommentReplies: {
    [key: string]: {
      comments: AnswerCommentReplyI[];
      total: number;
      nextCursorId: number | null;
    };
  };
};
