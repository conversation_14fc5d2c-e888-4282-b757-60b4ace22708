import { useState, useCallback } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import { showToast } from '@/src/utilities/toast';
import APIResError from '@/src/errors/networks/APIResError';
import { fetchNotificationData, fetchNotifications } from '@/src/networks/notifications/fetchMany';
import type {
  NotificationFetchManyQueryI,
  ProfileForNotificationI,
  PostForNotificationI,
  NotificationFetchManyItemI,
} from '@/src/networks/notifications/types';
import { updateNotificationRead } from '@/src/networks/notifications/updateRead';
import type { UseNotificationListReturn } from './types';

export const useNotificationList = (): UseNotificationListReturn => {
  const [notifications, setNotifications] = useState<NotificationFetchManyQueryI[]>([]);
  const [profiles, setProfiles] = useState<ProfileForNotificationI[]>([]);
  const [posts, setPosts] = useState<PostForNotificationI[]>([]);
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [nextCursor, setNextCursor] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const fetchAdditionalData = async (notificationList: NotificationFetchManyQueryI[]) => {
    const items: NotificationFetchManyItemI[] = notificationList.map((notification) => ({
      actorProfileId: notification.data.actorProfileId,
      postId: notification.data.postId,
    }));

    try {
      const result = await fetchNotificationData(items);
      if (result.profiles) {
        setProfiles((prev) => {
          const existingIds = new Set(prev.map((p) => p.id));
          const newProfiles = result.profiles!.filter((p) => !existingIds.has(p.id));
          return [...prev, ...newProfiles];
        });
      }
      if (result.posts) {
        setPosts((prev) => {
          const existingIds = new Set(prev.map((p) => p.id));
          const newPosts = result.posts!.filter((p) => !existingIds.has(p.id));
          return [...prev, ...newPosts];
        });
      }
    } catch (err) {
      if (notifications.length === 0) {
        const errorMessage = `Failed to fetch additional notification data: ${err instanceof APIResError ? err.message : 'Unknown error'}`;
        triggerErrorBoundary(new Error(errorMessage));
      } else {
        showToast({ message: 'Failed to update notification details', type: 'error' });
      }
    }
  };

  const loadNotifications = async (cursor: string | null = null, isRefresh = false) => {
    if (loading && !isRefresh) return;

    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }

      const result = await fetchNotifications({
        cursorId: cursor,
        pageSize: 10,
      });

      if (isRefresh || !cursor) {
        setNotifications(result.notifications);
        setProfiles([]);
        setPosts([]);
      } else {
        setNotifications((prev) => [...prev, ...result.notifications]);
      }

      await fetchAdditionalData(result.notifications);

      setNextCursor(result.nextCursor);
      setHasMore(!!result.nextCursor);
    } catch (err) {
      if (notifications.length === 0) {
        const errorMessage = `Failed to load notifications: ${err instanceof APIResError ? err.message : 'Unknown error'}`;
        triggerErrorBoundary(new Error(errorMessage));
      } else {
        showToast({ message: 'Failed to load more notifications', type: 'error' });
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
      setInitialLoading(false);
    }
  };

  const loadMore = () => {
    if (hasMore && !loading && nextCursor) {
      loadNotifications(nextCursor);
    }
  };

  const refresh = () => {
    loadNotifications(null, true);
  };

  const markAllAsRead = async () => {
    try {
      const unreadNotifications = notifications.filter((n) => !n.isRead);
      if (unreadNotifications.length === 0) return;
      const unreadIds = unreadNotifications.map((n) => n.id);
      await updateNotificationRead(unreadIds);
      setNotifications((prev) => prev.map((notification) => ({ ...notification, isRead: true })));
    } catch (err) {
      console.error('Failed to mark all notifications as read:', err);
      const errorMessage = `Failed to mark all notifications as read: ${
        err instanceof APIResError ? err.message : 'Unknown error'
      }`;
      triggerErrorBoundary(new Error(errorMessage));
    }
  };

  const markAsRead = async (notificationId: string) => {
    try {
      await updateNotificationRead([notificationId]);

      setNotifications((prev) =>
        prev.map((notification) =>
          notification.id === notificationId ? { ...notification, isRead: true } : notification,
        ),
      );
    } catch (err) {
      console.error('Failed to mark notification as read:', err);
      const errorMessage = `Failed to mark notification as read: ${
        err instanceof APIResError ? err.message : 'Unknown error'
      }`;
      triggerErrorBoundary(new Error(errorMessage));
    }
  };

  const getProfileById = (id: string) => {
    return profiles.find((profile) => profile.id === id);
  };

  const getPostById = (id: string) => {
    return posts.find((post) => post.id === id);
  };

  useFocusEffect(
    useCallback(() => {
      setInitialLoading(true);
      setError(null);
      loadNotifications(null, false);
    }, []),
  );

  return {
    notifications,
    profiles,
    posts,
    loading,
    initialLoading,
    hasMore,
    loadMore,
    refresh,
    refreshing,
    markAllAsRead,
    markAsRead,
    getProfileById,
    getPostById,
  };
};
