import { useEffect, useRef } from 'react';
import { Linking } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { URL } from 'react-native-url-polyfill';
import { useSelector } from 'react-redux';
import { BottomTabNavigationI } from '../navigation/types';
import { selectCurrentUser } from '../redux/selectors/user';
import { validateRoute } from './deeplink-validators';
import { Route, RouteConfig } from './types';

const routeConfig: RouteConfig = {
  post: { stack: 'HomeStack', screen: 'Comment', paramKeys: ['type', 'postId'] },
  profile: {
    stack: 'HomeStack',
    screen: 'OtherUserProfile',
    paramKeys: ['profileId', 'fromTabPress'],
  },
  forum: { stack: 'LearnCollabStack', screen: 'ForumAnswers', paramKeys: ['postId'] },
  ship: { stack: 'HomeStack', screen: 'ShipProfile', paramKeys: ['imo', 'dataType'] },
  port: { stack: 'HomeStack', screen: 'PortProfile', paramKeys: ['unLocode', 'dataType'] },
} as const;

export const useDeepLink = () => {
  const currentUser = useSelector(selectCurrentUser);
  const navigation = useNavigation<BottomTabNavigationI>();
  const pendingUrlRef = useRef<string | null>(null);
  const isNavigationReadyRef = useRef(false);

  const handleUrl = async (url: string | null) => {
    if (!url) return;
    if (!isNavigationReadyRef.current) {
      pendingUrlRef.current = url;
      return;
    }
    let path: string;
    if (url.startsWith('navicater://')) {
      path = url.replace(/.*?:\/\//g, '');
    } else {
      const urlObj = new URL(url);
      path = urlObj.pathname.startsWith('/') ? urlObj.pathname.substring(1) : urlObj.pathname;
    }

    const segments = path.split('/');
    const route = segments[0] as Route;
    const config = routeConfig[route];
    if (!config) {
      navigation.navigate('HomeStack', { screen: 'NotFound' });
      return;
    }
    const params = config.paramKeys.reduce(
      (acc, key, i) => {
        acc[key] = segments[i + 1] || '';
        return acc;
      },
      {} as Record<string, string>,
    );
    if (route === 'profile' && currentUser && params.profileId === currentUser.profileId) {
      navigation.navigate('ProfileStack', {
        screen: 'UserProfile',
        params: {
          fromTabPress: true,
          profileId: params.profileId,
        },
      });
      return;
    }
    const validation = await validateRoute(route, params);
    if (!validation.isValid) {
      navigation.navigate('HomeStack', { screen: 'NotFound' });
      return;
    }
    if (route === 'post') {
      navigation.navigate(config.stack as 'ProfileStack', {
        screen: config.screen as 'UserProfile',
        params: { ...params },
      });
    }
    if (route === 'forum') {
      navigation.navigate(config.stack as 'LearnCollabStack', {
        screen: config.screen as 'ForumAnswers',
        params: { postId: params.postId },
      });
    }
    if (route === 'ship') {
      navigation.navigate(config.stack as 'HomeStack', {
        screen: config.screen as 'ShipProfile',
        params: { imo: params.imo, dataType: params.dataType },
      });
    }
    if (route === 'port') {
      navigation.navigate(config.stack as 'HomeStack', {
        screen: config.screen as 'PortProfile',
        params: { unLocode: params.unLocode, dataType: params.dataType },
      });
    }
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      isNavigationReadyRef.current = true;

      if (pendingUrlRef.current) {
        const url = pendingUrlRef.current;
        pendingUrlRef.current = null;
        handleUrl(url);
      }
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    Linking.getInitialURL().then(handleUrl);
    const sub = Linking.addListener('url', ({ url }) => handleUrl(url));
    return () => sub.remove();
  }, []);
};
