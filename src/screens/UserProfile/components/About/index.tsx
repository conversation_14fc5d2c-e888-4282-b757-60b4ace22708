import { ScrollView, Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import { selectProfileDescription } from '@/src/redux/selectors/about';
import ProfileCertification from './components/ProfileCertification';
import ProfileDocumentation from './components/ProfileDocumentation';
import ProfileEducation from './components/ProfileEducation';
import ProfileSkill from './components/ProfileSkills';
import { AboutScreenPropsI } from './types';

const AboutScreen = ({ isUserProfile, profileId }: AboutScreenPropsI) => {
  const description = useSelector(selectProfileDescription);
  return (
    <ScrollView
      contentContainerStyle={{ flexGrow: 1, paddingBottom: 10 }}
      showsVerticalScrollIndicator={false}
      className="px-4"
    >
      {description && <Text className="pt-8 pb-4">{description}</Text>}
      {description && <View className="h-2 bg-gray-100 mx-[-16px] my-4" />}
      <ProfileEducation isUserProfile={isUserProfile} profileId={profileId} />
      <View className="h-2 bg-gray-100 mx-[-16px] my-4" />
      <ProfileCertification isUserProfile={isUserProfile} profileId={profileId} />
      <View className="h-2 bg-gray-100 mx-[-16px] my-4" />
      <ProfileDocumentation isUserProfile={isUserProfile} profileId={profileId} />
      <View className="h-2 bg-gray-100 mx-[-16px] my-4" />
      <ProfileSkill isUserProfile={isUserProfile} profileId={profileId} />
    </ScrollView>
  );
};

export default AboutScreen;
