import { Text, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import RadioButton from '@/src/components/RadioButton';
import ToggleSwitch from '@/src/components/Toggle';
import {
  selectQuestionType,
  selectQuestionIsAnonymous,
  selectQuestionIsLive,
} from '@/src/redux/selectors/question';
import {
  setQuestionType,
  setIsAnonymous,
  setIsLive,
} from '@/src/redux/slices/question/questionSlice';
import { AppDispatch } from '@/src/redux/store';
import QnaFields from '../QnaFields';
import TroubleshootingFields from '../TroubleshootingFields';

export const validateAskQuestionForm = (state: any): { isValid: boolean; message: string } => {
  const questionType = state.question.formData.type;
  const isGeneral = state.question.formData.isGeneral;
  const topicsSelection = state.search.multipleSelections.topic;
  const departmentSelection = state.search.selections.department;
  const equipmentCategorySelection = state.search.selections.equipmentCategory;
  const equipmentManufacturerSelection = state.search.selections.equipmentManufacturer;
  const equipmentModelSelection = state.search.selections.equipmentModel;

  if (questionType === 'NORMAL') {
    // QnA validation
    if (!topicsSelection || topicsSelection.length === 0) {
      return {
        isValid: false,
        message: 'Please select at least one topic for your question',
      };
    }

    if (!isGeneral && !departmentSelection) {
      return {
        isValid: false,
        message: 'Please select a department type',
      };
    }
  } else {
    // Troubleshooting validation
    if (!equipmentCategorySelection) {
      return {
        isValid: false,
        message: 'Please select an equipment category',
      };
    }

    if (!equipmentManufacturerSelection) {
      return {
        isValid: false,
        message: 'Please select an equipment manufacturer',
      };
    }

    if (!equipmentModelSelection) {
      return {
        isValid: false,
        message: 'Please select an equipment model',
      };
    }

    if (!isGeneral && !departmentSelection) {
      return {
        isValid: false,
        message: 'Please select a department type',
      };
    }
  }

  return { isValid: true, message: '' };
};

const AskQuestionForm = () => {
  const dispatch = useDispatch<AppDispatch>();
  const questionType = useSelector(selectQuestionType);
  const isAnonymous = useSelector(selectQuestionIsAnonymous);
  const isLive = useSelector(selectQuestionIsLive);

  const isQnA = questionType === 'NORMAL';

  const handleTypeChange = (type: 'qna' | 'troubleshoot') => {
    dispatch(setQuestionType(type === 'qna' ? 'NORMAL' : 'TROUBLESHOOT'));
  };

  const handleAnonymousToggle = () => {
    dispatch(setIsAnonymous(!isAnonymous));
  };

  const handleLiveToggle = () => {
    dispatch(setIsLive(!isLive));
  };

  return (
    <View className="mt-6 flex-1 gap-3 px-2">
      <Text className="text-xl font-medium">Ask Question</Text>
      <View>
        <Text className="text-sm leading-4 my-3">Post type</Text>
        <View className="flex-row justify-between">
          <View className="flex-1">
            <RadioButton
              selected={questionType === 'NORMAL'}
              onPress={() => handleTypeChange('qna')}
              label="QnA"
            />
          </View>
          <View className="flex-1">
            <RadioButton
              selected={questionType === 'TROUBLESHOOT'}
              onPress={() => handleTypeChange('troubleshoot')}
              label="Troubleshooting"
            />
          </View>
        </View>
      </View>
      {isQnA ? <QnaFields /> : <TroubleshootingFields />}
      <View className="gap-5">
        <ToggleSwitch
          label="Make anonymous"
          enabled={isAnonymous}
          onToggle={handleAnonymousToggle}
        />
        <ToggleSwitch label="Live Mode" enabled={isLive} onToggle={handleLiveToggle} />
      </View>
    </View>
  );
};

export default AskQuestionForm;
