import { useSelector } from 'react-redux';
import {
  selectAboutProfileDocumentsIdentityCount,
  selectAboutProfileIdentityDocuments,
} from '@/src/redux/selectors/about';

export const useProfileIdentity = (profileId: string) => {
  const documents = useSelector(selectAboutProfileIdentityDocuments);
  const count = useSelector(selectAboutProfileDocumentsIdentityCount);

  return {
    documents,
    count,
  };
};
