/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Circle, Path } from 'react-native-svg';
import { OutlinedIconPropsI } from './types';

const Announcements: React.FC<OutlinedIconPropsI> = ({
  width = 3.5,
  height = 4.5,
  stroke = '#000',
  color,
  strokeWidth = 1,
  accessibilityLabel = 'Announcements',
  disabled,
  ...props
}) => {
  const strokeColor = color || stroke;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 31 39"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        d="M24.892 24.082c-4.155-.593-5.395-.08-7.123.594-2.635 1.028-4.153 4.154-3.56 5.34.594 1.188 14.243 1.188 15.43.594 1.187-.593-.593-5.934-4.747-6.528zM9.462 12.212v6.529M4.713 19.334S5.9 25.27 7.087 25.863c1.187.593 2.374.296 2.967 0 .594-.297-2.374-7.122-2.374-7.122"
        stroke={strokeColor}
        strokeWidth={strokeWidth}
      />
      <Path
        d="M21.33 13.4s.594-4.749-.592-5.342c-1.187-.594-7.716 3.56-11.277 4.154-3.561.594-3.56-1.187-6.528 0-2.967 1.187-2.374 5.935 0 6.529 2.374.593 5.342-.594 7.122 0 1.78.593 7.715 4.154 7.715 4.154"
        stroke={strokeColor}
        strokeWidth={strokeWidth}
      />
      <Circle
        cx={21.9239}
        cy={18.7407}
        r={3.65437}
        stroke={strokeColor}
        strokeWidth={strokeWidth}
      />
    </Svg>
  );
};

export default Announcements;
