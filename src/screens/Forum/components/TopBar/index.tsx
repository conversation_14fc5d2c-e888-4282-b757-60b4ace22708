import React from 'react';
import { Pressable, Text, View, Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import BackButton from '@/src/components/BackButton';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
// import UserAvatar from '@/src/components/UserAvatar';
// import AiBot from '@/src/assets/images/others/aibot.png';
import AddItem from '@/src/assets/svgs/AddItem';

const TopBar = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const handleAdd = () => {
    navigation.navigate('CommunityQuestion', { id: '' });
  };
  return (
    <View className="flex-row justify-between items-center px-4 py-2 bg-white">
      <View className="flex-row items-center">
        <BackButton onBack={() => navigation.goBack()} label="" />
        <Text className="text-2xl font-semibold text-black">Forum</Text>
      </View>
      <View className="flex-row items-center gap-2">
        {/* <Pressable className="items-center justify-center pr-2">
        <UserAvatar
          avatarUri={Image.resolveAssetSource(AiBot).uri}
          width={37}
          height={37}
          name="Navicater AI"
        />
      </Pressable> */}
        <Pressable onPress={handleAdd} className="items-center justify-center pr-2">
          <AddItem height={5} width={5} />
          {/* <Text className="text-xs text-black">Create</Text> */}
        </Pressable>
      </View>
    </View>
  );
};

export default TopBar;
