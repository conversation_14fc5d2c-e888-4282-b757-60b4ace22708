import { KeyboardAvoidingView, Platform } from 'react-native';
import SafeArea from '@/src/components/SafeArea';
import PeopleList from './components/PeopleList';

const PeopleScreen = () => {
  return (
    <SafeArea>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <PeopleList />
      </KeyboardAvoidingView>
    </SafeArea>
  );
};

export default PeopleScreen;
