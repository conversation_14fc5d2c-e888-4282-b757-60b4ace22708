import { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  Pressable,
  Platform,
  ActivityIndicator,
  Image,
  Animated,
  Keyboard,
  PermissionsAndroid,
  Alert,
  FlatList,
  RefreshControl,
  KeyboardEvent,
  Dimensions,
} from 'react-native';
import { type RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import AudioRecorderPlayer, {
  AudioEncoderAndroidType,
  AudioSet,
  AudioSourceAndroidType,
} from 'react-native-audio-recorder-player';
import { Audio } from 'react-native-compressor';
import ImagePicker from 'react-native-image-crop-picker';
import { RFPercentage } from 'react-native-responsive-fontsize';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { pick, types } from '@react-native-documents/picker';
import { viewDocument } from '@react-native-documents/viewer';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import BottomSheet from '@/src/components/Bottomsheet/index';
import ImageViewer from '@/src/components/ImageViewer';
import CustomModal from '@/src/components/Modal';
import { OptionItem, OptionsMenu } from '@/src/components/OptionsMenu';
import SafeArea from '@/src/components/SafeArea';
import UserAvatar from '@/src/components/UserAvatar';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { compressToTargetSize } from '@/src/utilities/upload/compress';
import type { HomeStackParamListI } from '@/src/navigation/types';
import Close from '@/src/assets/svgs/Close';
import HorizontalEllipsis from '@/src/assets/svgs/HorizontalEllipsis';
import PauseIcon from '@/src/assets/svgs/Pause';
import PlayIcon from '@/src/assets/svgs/Play';
import Trash from '@/src/assets/svgs/TrashBin';
import ChatInput from './components/ChatInput';
import LinkPreview from './components/LinkPreview';
import SwipeableMessage from './components/SwipeableMessage';
import type { MediaPreviewItem, MessageI } from './types';
import { useChatScreenHook } from './useHook';

const FILE_SIZE_LIMITS = {
  'application/pdf': 4 * 1024 * 1024,
  'audio/mpeg': 10 * 1024 * 1024,
  'audio/mp3': 10 * 1024 * 1024,
  'audio/m4a': 10 * 1024 * 1024,
  'video/mp4': 50 * 1024 * 1024,
};

const { height: screenHeight } = Dimensions.get('window');

const ChatScreen = () => {
  const navigation = useNavigation();
  const route = useRoute<RouteProp<HomeStackParamListI, 'Chat'>>();
  const profileId = route.params.id;
  const currentUser = useSelector(selectCurrentUser);
  const insets = useSafeAreaInsets();

  const [editingMessageId, setEditingMessageId] = useState<string | null>(null);
  const [mediaOptionsVisible, setMediaOptionsVisible] = useState(false);
  const [messageOptionsVisible, setMessageOptionsVisible] = useState(false);
  const [pendingAction, setPendingAction] = useState<'gallery' | 'file' | null>(null);
  const [mediaPreview, setMediaPreview] = useState<MediaPreviewItem[]>([]);
  const [imageViewerVisible, setImageViewerVisible] = useState(false);
  const [selectedImagePost, setSelectedImagePost] = useState<any>(null);
  const [recordingTime, setRecordingTime] = useState('00:00');
  const [isAudioPlaying, setIsAudioPlaying] = useState<{ [key: string]: boolean }>({});
  const [currentPlayingAudio, setCurrentPlayingAudio] = useState<string | null>(null);
  const [audioRecorderPlayer] = useState(() => new AudioRecorderPlayer());
  const [recordedAudioFile, setRecordedAudioFile] = useState<any>(null);
  const [isPreviewPlaying, setIsPreviewPlaying] = useState(false);
  const [previewPlaybackPosition, setPreviewPlaybackPosition] = useState(0);
  const [previewDuration, setPreviewDuration] = useState(0);
  const [isAudioUploading, setIsAudioUploading] = useState(false);
  const [audioUploadProgress, setAudioUploadProgress] = useState(0);
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [, setKeyboardHeight] = useState(0);

  const textInputRef = useRef<TextInput>(null);
  const progressBarWidth = useRef(new Animated.Value(0)).current;
  const uploadProgressAnim = useRef(new Animated.Value(0)).current;
  const inputTranslateY = useRef(new Animated.Value(0)).current;

  const {
    messages,
    profile,
    loading,
    loadingMore,
    refreshing,
    replyPreview,
    selectedMessage,
    messageText,
    sending,
    flatListRef,
    isUserOnline,
    lastSeen,
    selectedMessages,
    isSelectionMode,
    deleteOptionsVisible,
    clearChatVisible,
    audioDurations,
    otherUserTyping,
    setMessageText,
    setDeleteOptionsVisible,
    setClearChatVisible,
    sendMessage,
    sendMediaMessage,
    uploadMedia,
    editMessage,
    handleLoadMore,
    refreshMessages,
    handleMessageLongPress,
    handleMessagePress,
    handleReply,
    handleSwipeReply,
    handleCloseReply,
    setSelectedMessage,

    exitSelectionMode,
    handleDeleteSelected,
    handleDeleteSelectedForMe,
    handleDeleteSelectedForEveryone,
    handleConfirmClearChat,
    canDeleteSelectedForEveryone,
    setClearChatOptionsVisible,
    clearChatOptions,
    formatAudioTime,
    loadAudioDuration,
    setMessages,
    setReplyPreview,
  } = useChatScreenHook(profileId);

  const keyboardWillShow = (e: KeyboardEvent) => {
    const height = e.endCoordinates.height;
    setKeyboardHeight(height);

    Animated.timing(inputTranslateY, {
      toValue: -height,
      duration: e.duration || 250,
      useNativeDriver: true,
    }).start();
  };

  const keyboardWillHide = (e: KeyboardEvent) => {
    setKeyboardHeight(0);

    Animated.timing(inputTranslateY, {
      toValue: 0,
      duration: e.duration || 250,
      useNativeDriver: true,
    }).start();
  };

  useEffect(() => {
    const showEvent = Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow';
    const hideEvent = Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide';

    const keyboardShowListener = Keyboard.addListener(showEvent, keyboardWillShow);
    const keyboardHideListener = Keyboard.addListener(hideEvent, keyboardWillHide);

    return () => {
      keyboardShowListener?.remove();
      keyboardHideListener?.remove();
    };
  }, []);

  useEffect(() => {
    return () => {
      if (audioRecorderPlayer) {
        audioRecorderPlayer.stopRecorder().catch(() => {});
        audioRecorderPlayer.stopPlayer().catch(() => {});
        audioRecorderPlayer.removeRecordBackListener();
        audioRecorderPlayer.removePlayBackListener();
      }
    };
  }, [audioRecorderPlayer]);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const validateFileSize = (file: any): boolean => {
    const mimeType = file.mime || file.type;
    const fileSize = file.size;
    const maxSize = FILE_SIZE_LIMITS[mimeType as keyof typeof FILE_SIZE_LIMITS];

    if (!maxSize) {
      return true;
    }

    return fileSize <= maxSize;
  };

  const showFileSizeAlert = (invalidFiles: any[]) => {
    if (invalidFiles.length === 0) return;

    const fileTypeMap = {
      'application/pdf': 'PDF',
      'audio/mpeg': 'Audio',
      'audio/mp3': 'Audio',
      'audio/m4a': 'Audio',
      'video/mp4': 'Video',
    };

    const firstInvalidFile = invalidFiles[0];
    const mimeType = firstInvalidFile.mime || firstInvalidFile.type;
    const maxSize = FILE_SIZE_LIMITS[mimeType as keyof typeof FILE_SIZE_LIMITS];
    const fileTypeName = fileTypeMap[mimeType as keyof typeof fileTypeMap] || 'File';

    if (invalidFiles.length === 1) {
      Alert.alert(
        'File Too Large',
        `${fileTypeName} size (${formatFileSize(firstInvalidFile.size)}) exceeds the maximum allowed size of ${formatFileSize(maxSize)}.`,
      );
    } else {
      Alert.alert(
        'Files Too Large',
        `${invalidFiles.length} files exceed the maximum allowed size. Please select smaller files.`,
      );
    }
  };

  const requestAudioPermissions = async (): Promise<boolean> => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.requestMultiple([
          PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
        ]);

        return (
          granted[PermissionsAndroid.PERMISSIONS.RECORD_AUDIO] ===
          PermissionsAndroid.RESULTS.GRANTED
        );
      } catch (err) {
        return false;
      }
    }
    return true;
  };

  const onBack = () => {
    if (isSelectionMode) {
      exitSelectionMode();
    } else {
      Keyboard.dismiss();
      setTimeout(() => {
        navigation.goBack();
      }, 100);
    }
  };

  const handleAttachment = () => {
    setMediaOptionsVisible(true);
  };

  const handleGalleryPress = () => {
    setPendingAction('gallery');
    setMediaOptionsVisible(false);
  };

  const handleFilePress = () => {
    setPendingAction('file');
    setMediaOptionsVisible(false);
  };

  const handleModalHide = () => {
    if (!pendingAction) return;

    setTimeout(() => {
      if (pendingAction === 'gallery') {
        openGallery();
      } else if (pendingAction === 'file') {
        openFilePicker();
      }
      setPendingAction(null);
    }, 100);
  };

  const openGallery = () => {
    ImagePicker.openPicker({
      multiple: true,
      mediaType: 'any',
      includeBase64: false,
      maxFiles: 5,
    })
      .then(async (media) => {
        const processedMedia = await Promise.all(
          (Array.isArray(media) ? media : [media]).map(async (item) => {
            if (item.mime.startsWith('image/')) {
              try {
                const compressedPath = await compressToTargetSize(item.path, 500);
                return { ...item, path: compressedPath, mime: 'image/jpeg' };
              } catch (error) {
                return item;
              }
            }
            return item;
          }),
        );
        handleMediaSelection(processedMedia);
      })
      .catch(() => {});
  };

  const openFilePicker = async () => {
    try {
      const files = await pick({
        allowMultiSelection: true,
        type: [types.pdf],
      });

      const validFiles = [];
      const invalidFiles = [];

      for (const file of files) {
        const formattedFile = {
          path: file.uri,
          mime: file.type || 'application/pdf',
          filename: file.name,
          size: file.size,
        };

        if (validateFileSize(formattedFile)) {
          validFiles.push(formattedFile);
        } else {
          invalidFiles.push(formattedFile);
        }
      }

      if (invalidFiles.length > 0) {
        showFileSizeAlert(invalidFiles);
      }

      if (validFiles.length > 0) {
        handleMediaSelection(validFiles);
      }
    } catch (error: any) {}
  };

  const handleMediaSelection = async (selectedMedia: any[]) => {
    const tempMediaItems: MediaPreviewItem[] = selectedMedia.map((item, index) => ({
      url: item.path,
      mimeType: item.mime,
      name: item.filename || `media_${index}.${item.mime.split('/')[1]}`,
      isUploading: true,
    }));

    setMediaPreview(tempMediaItems);

    try {
      const uploadedMedia = await uploadMedia(selectedMedia);
      setMediaPreview(uploadedMedia);
    } catch (error) {
      setMediaPreview([]);
    }
  };

  const handleSendMessage = () => {
    if (editingMessageId) {
      handleSaveEdit();
      return;
    }

    if (mediaPreview.length > 0) {
      if (mediaPreview.some((item) => item.isUploading)) {
        return;
      }
      sendMediaMessage(mediaPreview, messageText.trim());
      setMessageText('');
      setMediaPreview([]);
    } else if (recordedAudioFile) {
      handleSendAudioMessage();
    } else if (messageText.trim()) {
      sendMessage();
      setMessageText('');
    }
  };

  const handleSendAudioMessage = async () => {
    if (isAudioUploading) return;

    try {
      setIsAudioUploading(true);
      setAudioUploadProgress(0);

      Animated.timing(uploadProgressAnim, {
        toValue: 1,
        duration: 2000,
        useNativeDriver: false,
      }).start();

      const simulateProgress = () => {
        const interval = setInterval(() => {
          setAudioUploadProgress((prev) => {
            if (prev >= 90) {
              clearInterval(interval);
              return prev;
            }
            return prev + Math.random() * 15;
          });
        }, 200);
        return interval;
      };

      const progressInterval = simulateProgress();

      const mediaItems = await uploadMedia([recordedAudioFile]);

      clearInterval(progressInterval);
      setAudioUploadProgress(100);

      const currentMessageText = messageText.trim();
      await sendMediaMessage(mediaItems, currentMessageText);

      setMessageText('');
      setRecordedAudioFile(null);
      setIsPreviewPlaying(false);
      setPreviewPlaybackPosition(0);
      setPreviewDuration(0);
      setIsAudioUploading(false);
      setAudioUploadProgress(0);
      uploadProgressAnim.setValue(0);
    } catch (error) {
      setIsAudioUploading(false);
      setAudioUploadProgress(0);
      uploadProgressAnim.setValue(0);
    }
  };

  const handleCancelMediaPreview = () => {
    setMediaPreview([]);
  };

  const handleCancelAudioPreview = () => {
    if (isPreviewPlaying) {
      audioRecorderPlayer.stopPlayer().catch(() => {});
      audioRecorderPlayer.removePlayBackListener();
      setIsPreviewPlaying(false);
    }
    if (isAudioUploading) {
      setIsAudioUploading(false);
      setAudioUploadProgress(0);
      uploadProgressAnim.setValue(0);
    }
    setRecordedAudioFile(null);
    setPreviewPlaybackPosition(0);
    setPreviewDuration(0);
  };

  const handleImagePress = (message: MessageI) => {
    if (message.content.media && message.content.media.length > 0) {
      const imagePost = {
        createdAt: message.createdAt,
        Profile: {
          avatar:
            message.senderId === currentUser.profileId ? currentUser.avatar : profile?.avatar || '',
          name:
            message.senderId === currentUser.profileId ? currentUser.fullName : profile?.name || '',
          designation:
            message.senderId === currentUser.profileId
              ? currentUser.designation
              : profile?.designation,
          entity:
            message.senderId === currentUser.profileId ? currentUser.organisation : profile?.entity,
        },
        Media: message.content.media.map((media) => ({
          fileUrl: media.url,
          caption: '',
        })),
      };
      setSelectedImagePost(imagePost);
      setImageViewerVisible(true);
    }
  };

  const handleDocumentPress = async (media: any) => {
    try {
      await viewDocument({
        uri: media.url,
        mimeType: media.mimeType,
      });
    } catch (error) {}
  };

  const handleMicrophonePress = async () => {
    if (isRecording) {
      try {
        const result = await audioRecorderPlayer.stopRecorder();
        audioRecorderPlayer.removeRecordBackListener();
        setRecordingTime('00:00');

        let compressedAudioPath = result;
        try {
          compressedAudioPath = await Audio.compress(result, {
            quality: 'medium',
          });
        } catch (compressionError) {
          compressedAudioPath = result;
        }

        const audioFile = {
          path: compressedAudioPath,
          mime: Platform.OS === 'ios' ? 'audio/m4a' : 'audio/mp3',
          filename: `audio_${Date.now()}.${Platform.OS === 'ios' ? 'm4a' : 'mp3'}`,
          size: 0,
        };

        try {
          const fileInfo = await fetch(compressedAudioPath);
          const blob = await fileInfo.blob();
          audioFile.size = blob.size;
        } catch (error) {
          audioFile.size = 0;
        }

        setRecordedAudioFile(audioFile);
        setIsRecording(false);
      } catch (_error) {
        setIsRecording(false);
      }
    } else {
      try {
        const hasAudioPermission = await requestAudioPermissions();

        if (!hasAudioPermission) {
          return;
        }

        const path = Platform.OS === 'ios' ? 'audio.m4a' : undefined;

        const audioSet: AudioSet = {
          AudioEncoderAndroid: AudioEncoderAndroidType.AAC,
          AudioSourceAndroid: AudioSourceAndroidType.MIC,
          AVNumberOfChannelsKeyIOS: Platform.OS === 'ios' ? 2 : undefined,
        };

        await audioRecorderPlayer.startRecorder(path, audioSet);

        audioRecorderPlayer.addRecordBackListener((e) => {
          const seconds = Math.floor(e.currentPosition / 1000);
          const minutes = Math.floor(seconds / 60);
          const remainingSeconds = seconds % 60;
          setRecordingTime(
            `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`,
          );
        });

        setIsRecording(true);
      } catch (_error) {
        // r
      }
    }
  };

  const handlePlayPreviewAudio = async () => {
    if (!recordedAudioFile || isAudioUploading) return;

    try {
      if (isPreviewPlaying) {
        await audioRecorderPlayer.stopPlayer();
        audioRecorderPlayer.removePlayBackListener();
        setIsPreviewPlaying(false);
      } else {
        await audioRecorderPlayer.startPlayer(recordedAudioFile.path);
        setIsPreviewPlaying(true);

        audioRecorderPlayer.addPlayBackListener((e) => {
          setPreviewPlaybackPosition(e.currentPosition);
          setPreviewDuration(e.duration);
          progressBarWidth.setValue(e.currentPosition / e.duration);

          if (e.currentPosition === e.duration) {
            setIsPreviewPlaying(false);
            setPreviewPlaybackPosition(0);
            progressBarWidth.setValue(0);
            audioRecorderPlayer.removePlayBackListener();
          }
        });
      }
    } catch (error) {
      setIsPreviewPlaying(false);
    }
  };

  const handlePlayAudio = async (mediaUrl: string, messageId: string) => {
    try {
      if (isAudioPlaying[messageId]) {
        await audioRecorderPlayer.stopPlayer();
        audioRecorderPlayer.removePlayBackListener();
        setIsAudioPlaying((prev) => ({ ...prev, [messageId]: false }));
        setCurrentPlayingAudio(null);
      } else {
        if (currentPlayingAudio) {
          await audioRecorderPlayer.stopPlayer();
          setIsAudioPlaying((prev) => ({ ...prev, [currentPlayingAudio]: false }));
        }

        await audioRecorderPlayer.startPlayer(mediaUrl);
        setIsAudioPlaying((prev) => ({ ...prev, [messageId]: true }));
        setCurrentPlayingAudio(messageId);

        audioRecorderPlayer.addPlayBackListener((e) => {
          if (e.currentPosition === e.duration) {
            setIsAudioPlaying((prev) => ({ ...prev, [messageId]: false }));
            setCurrentPlayingAudio(null);
            audioRecorderPlayer.removePlayBackListener();
          }
        });
      }
    } catch (error) {
      setIsAudioPlaying((prev) => ({ ...prev, [messageId]: false }));
    }
  };

  const getDateLabel = (timestamp: Date | string): string => {
    const messageDate = typeof timestamp === 'string' ? new Date(timestamp) : new Date(timestamp);

    if (isNaN(messageDate.getTime())) {
      return 'Invalid Date';
    }

    const now = new Date();

    const messageDateOnly = new Date(
      messageDate.getFullYear(),
      messageDate.getMonth(),
      messageDate.getDate(),
    );

    const todayOnly = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    const diffTime = todayOnly.getTime() - messageDateOnly.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return 'Today';
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays > 1 && diffDays <= 6) {
      return messageDate.toLocaleDateString('en-IN', {
        weekday: 'long',
      });
    } else if (diffDays < 0) {
      return messageDate.toLocaleDateString('en-IN', {
        month: 'short',
        day: 'numeric',
        year: messageDate.getFullYear() !== now.getFullYear() ? 'numeric' : undefined,
      });
    } else {
      return messageDate.toLocaleDateString('en-IN', {
        month: 'short',
        day: 'numeric',
        year: messageDate.getFullYear() !== now.getFullYear() ? 'numeric' : undefined,
      });
    }
  };

  const canEditMessage = (message: MessageI): boolean => {
    if (message.senderId !== currentUser.profileId || message.deletedForAll) return false;
    const editTimeLimit = new Date(message.createdAt);
    editTimeLimit.setMinutes(editTimeLimit.getMinutes() + 15);
    return new Date() <= editTimeLimit;
  };

  const handleMessageTap = (message: MessageI, fromMessageBody = false) => {
    if (isSelectionMode) {
      handleMessagePress(message);
    } else if (fromMessageBody) {
      setSelectedMessage(message);
      setMessageOptionsVisible(true);
    }
  };

  const handleEdit = () => {
    if (selectedMessage) {
      setReplyPreview({
        ...selectedMessage,
        isEditing: true,
      });

      setEditingMessageId(selectedMessage.id);
      setMessageText(selectedMessage.content.text || '');
      setTimeout(() => {
        textInputRef.current?.focus();
      }, 100);
    }
    setMessageOptionsVisible(false);
    setSelectedMessage(null);
  };

  const handleReplyToMessage = () => {
    if (selectedMessage && !selectedMessage.deletedForAll) {
      handleReply();
    }
    setMessageOptionsVisible(false);
    setSelectedMessage(null);
  };

  const handleSaveEdit = () => {
    if (editingMessageId && messageText.trim()) {
      editMessage(editingMessageId, messageText.trim());

      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === editingMessageId
            ? {
                ...msg,
                content: { ...msg.content, text: messageText.trim() },
                editedAt: new Date(),
              }
            : msg,
        ),
      );

      setEditingMessageId(null);
      setMessageText('');
      setReplyPreview(null);
    }
  };

  const handleTextInputChange = (text: string) => {
    setMessageText(text);
  };

  const extractUrls = (text: string): string[] => {
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    return text.match(urlRegex) || [];
  };

  const renderMediaDisplay = (mediaItems: any[], isMyMessage: boolean, message: MessageI) => {
    if (!mediaItems || mediaItems.length === 0) return null;

    const firstMedia = mediaItems[0];
    const mediaCount = mediaItems.length;

    const isImage = firstMedia.mimeType.startsWith('image/') || firstMedia.mimeType === 'JPEG';
    const isPdf = firstMedia.mimeType === 'application/pdf' || firstMedia.mimeType === 'PDF';
    const isAudio =
      firstMedia.mimeType === 'audio/mpeg' ||
      firstMedia.mimeType === 'audio/m4a' ||
      firstMedia.mimeType.includes('mp3') ||
      firstMedia.mimeType.includes('m4a') ||
      firstMedia.mimeType === 'MP3' ||
      firstMedia.mimeType === 'M4A';

    return (
      <Pressable
        onPress={() => {
          if (isSelectionMode) {
            handleMessagePress(message);
          } else if (isImage) {
            handleImagePress(message);
          } else if (isPdf) {
            handleDocumentPress(firstMedia);
          }
        }}
        className="rounded-2xl overflow-hidden mb-2 relative"
        style={{ minWidth: 200, maxWidth: 280 }}
      >
        {isImage ? (
          <Image
            source={{ uri: firstMedia.url }}
            style={{ width: '100%', height: 200, minWidth: 200 }}
            resizeMode="cover"
          />
        ) : isAudio ? (
          <View className="w-full p-3" style={{ minWidth: 200, maxWidth: 280 }}>
            <Pressable
              className="flex-row items-center gap-3"
              onPress={() => {
                if (!audioDurations[message.id]) {
                  loadAudioDuration(message.id, firstMedia.url);
                }
                handlePlayAudio(firstMedia.url, message.id);
              }}
            >
              <View className="w-8 h-8 rounded-full bg-gray-300 items-center justify-center">
                {isAudioPlaying[message.id] ? (
                  <PauseIcon width={2} height={2} color="#666666" />
                ) : (
                  <PlayIcon width={2} height={2} color="#666666" />
                )}
              </View>
              <View className="flex-1">
                <Text className="text-xs text-gray-500">
                  {audioDurations[message.id]
                    ? formatAudioTime(audioDurations[message.id])
                    : 'Audio'}
                </Text>
              </View>
            </Pressable>
          </View>
        ) : (
          <View className="w-full p-4 bg-gray-200 rounded-2xl" style={{ minHeight: 120 }}>
            <View className="items-center justify-center flex-1">
              <Text className="text-4xl mb-2">📄</Text>
              <Text
                className={`${isMyMessage ? 'text-gray-700' : 'text-gray-600'} text-center font-medium`}
              >
                PDF Document
              </Text>
              <Text
                className={`${isMyMessage ? 'text-gray-600' : 'text-gray-500'} text-xs text-center mt-1`}
              >
                Tap to view
              </Text>
            </View>
          </View>
        )}
        {mediaCount > 1 && (
          <View className="absolute top-2 right-2 bg-black/70 rounded-full px-2 py-1">
            <Text className="text-white text-xs font-medium">1/{mediaCount}</Text>
          </View>
        )}
        {isSelectionMode && selectedMessages.has(message.id) && (
          <View className="absolute top-2 left-2 bg-green-800 rounded-full w-6 h-6 items-center justify-center">
            <Text className="text-white text-xs font-bold">✓</Text>
          </View>
        )}
      </Pressable>
    );
  };

  const renderMediaPreview = () => {
    if (mediaPreview.length === 0) return null;

    const firstMedia = mediaPreview[0];
    const mediaCount = mediaPreview.length;
    const isImage = firstMedia.mimeType.startsWith('image/') || firstMedia.mimeType === 'JPEG';
    const isPdf = firstMedia.mimeType === 'application/pdf' || firstMedia.mimeType === 'PDF';
    const isText = firstMedia.mimeType === 'text/plain' || firstMedia.mimeType === 'TXT';
    const isAudio =
      firstMedia.mimeType === 'audio/mpeg' ||
      firstMedia.mimeType === 'audio/m4a' ||
      firstMedia.mimeType.includes('mp3') ||
      firstMedia.mimeType.includes('m4a') ||
      firstMedia.mimeType === 'MP3' ||
      firstMedia.mimeType === 'M4A';

    return (
      <View className="rounded-2xl overflow-hidden mb-2 relative">
        {isImage ? (
          <Image source={{ uri: firstMedia.url }} className="w-full h-48" resizeMode="cover" />
        ) : isAudio ? (
          <View className="w-full p-4 bg-gray-100 rounded-2xl">
            <View className="flex-row items-center gap-3">
              <View className="w-10 h-10 rounded-full bg-gray-300 items-center justify-center">
                <PlayIcon width={1.5} height={1.5} color="#666666" />
              </View>
              <View className="flex-1">
                <Text className="text-gray-700 font-medium">Audio</Text>
                <Text className="text-gray-500 text-xs mt-1">Ready to send</Text>
                {messageText.trim() && (
                  <Text className="text-xs text-gray-700 mt-1" numberOfLines={1}>
                    Caption: {messageText}
                  </Text>
                )}
              </View>
            </View>
            {firstMedia.isUploading && (
              <View className="absolute inset-0 bg-gray-100/90 justify-center items-center rounded-2xl">
                <ActivityIndicator size="small" color="#666666" />
                <Text className="text-gray-600 mt-2 text-sm">Uploading audio...</Text>
              </View>
            )}
          </View>
        ) : (
          <View className="w-full p-3 bg-gray-200 rounded-2xl h-48 justify-center items-center">
            <Text className="text-4xl mb-2">{isPdf ? '📄' : isText ? '📝' : '📎'}</Text>
            <Text className="text-gray-600 text-center font-medium">
              {isPdf ? 'PDF Document' : isText ? 'Text File' : 'File'}
            </Text>
            {messageText.trim() && (
              <Text className="text-xs text-gray-700 mt-3" numberOfLines={2}>
                Caption: {messageText}
              </Text>
            )}
          </View>
        )}
        {firstMedia.isUploading && (isImage || !isAudio) && (
          <View className="absolute inset-0 bg-black/50 justify-center items-center">
            <ActivityIndicator size="large" color="#ffffff" />
            <Text className="text-white mt-2">Uploading...</Text>
          </View>
        )}
        {mediaCount > 1 && (
          <View className="absolute top-2 left-2 bg-black/70 rounded-full px-2 py-1">
            <Text className="text-white text-xs font-medium">1/{mediaCount}</Text>
          </View>
        )}
        <Pressable
          className="absolute top-2 right-2 bg-black/50 rounded-full p-1"
          onPress={handleCancelMediaPreview}
        >
          <Close color="#ffffff" width={2} height={2} />
        </Pressable>
      </View>
    );
  };

  const renderAudioPreview = () => {
    if (!recordedAudioFile) return null;

    return (
      <View className="px-4 mb-3">
        <View className="bg-gray-100 rounded-xl p-4 relative">
          {isAudioUploading && (
            <View className="absolute inset-0 flex items-center justify-center bg-gray-100/95 rounded-xl z-10">
              <View className="w-full px-6">
                <ActivityIndicator size="small" color="#666666" />
                <Text className="text-gray-600 text-sm font-medium text-center mt-2">
                  Uploading audio... {Math.round(audioUploadProgress)}%
                </Text>
                <View className="w-full h-1.5 bg-gray-200 rounded-full mt-2 overflow-hidden">
                  <Animated.View
                    className="h-full bg-green-600 rounded-full"
                    style={{
                      width: uploadProgressAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: ['0%', '100%'],
                      }),
                    }}
                  />
                </View>
              </View>
            </View>
          )}

          <View className="flex-row items-center gap-3">
            <Pressable
              onPress={handlePlayPreviewAudio}
              disabled={Boolean(isAudioUploading)}
              className={`w-10 h-10 rounded-full items-center justify-center ${
                isAudioUploading ? 'bg-gray-200' : 'bg-gray-300'
              }`}
            >
              {isPreviewPlaying ? (
                <PauseIcon
                  width={1.5}
                  height={1.5}
                  color={isAudioUploading ? '#999999' : '#666666'}
                />
              ) : (
                <PlayIcon
                  width={1.5}
                  height={1.5}
                  color={isAudioUploading ? '#999999' : '#666666'}
                />
              )}
            </Pressable>
            <View className="flex-1">
              <Text className={`text-xs ${isAudioUploading ? 'text-gray-400' : 'text-gray-500'}`}>
                {isAudioUploading
                  ? 'Preparing...'
                  : `${formatAudioTime(previewPlaybackPosition)} / ${formatAudioTime(previewDuration || 0)}`}
              </Text>
              {messageText.trim() && (
                <Text className="text-xs text-gray-700 mt-1" numberOfLines={1}>
                  Caption: {messageText}
                </Text>
              )}
            </View>
            <Pressable
              onPress={handleCancelAudioPreview}
              disabled={Boolean(isAudioUploading)}
              className={`w-8 h-8 rounded-full items-center justify-center ${
                isAudioUploading ? 'bg-gray-100' : 'bg-gray-200'
              }`}
            >
              <Close color={isAudioUploading ? '#999999' : '#666666'} width={2} height={2} />
            </Pressable>
          </View>
        </View>
      </View>
    );
  };

  const renderMessage = (msg: MessageI, index: number) => {
    const isMyMessage = msg.senderId === currentUser.profileId;
    const replyToMessage = msg.replyTo ? messages.find((m) => m.id === msg.replyTo) : null;
    const hasMedia = msg.content.media && msg.content.media.length > 0;
    const hasText = msg.content.text && msg.content.text.trim();
    const isSelected = selectedMessages.has(msg.id);
    const urls = hasText ? extractUrls(msg.content.text!) : [];
    const hasLinks = urls.length > 0;

    const prevMessage = index > 0 ? messages[index - 1] : null;

    const showDateLabel =
      !prevMessage || getDateLabel(msg.createdAt) !== getDateLabel(prevMessage.createdAt);

    const renderText = (text: string) => {
      if (!text) return null;

      const urlRegex = /(https?:\/\/[^\s]+)/g;
      const parts = text.split(urlRegex);
      const matches = text.match(urlRegex) || [];

      return (
        <Text className="text-sm text-gray-900">
          {parts.map((part, i) => {
            const isUrl = matches.includes(part as never);
            return isUrl ? (
              <Text key={i} className="text-green-800">
                {part}
              </Text>
            ) : (
              <Text key={i}>{part}</Text>
            );
          })}
          {msg.editedAt && <Text className="text-xs text-gray-400 ml-1"> (edited)</Text>}
        </Text>
      );
    };

    return (
      <View key={msg.id}>
        {showDateLabel && (
          <Text className="text-center text-gray-500 font-semibold my-3">
            {getDateLabel(msg.createdAt)}
          </Text>
        )}
        <View className="mb-2 px-4">
          <SwipeableMessage
            message={msg}
            onReply={handleSwipeReply}
            onLongPress={handleMessageLongPress}
          >
            <Pressable
              onPress={() => handleMessageTap(msg, true)}
              className={`max-w-[80%] p-3 rounded-xl ${
                isMyMessage
                  ? 'bg-violet-100 border border-violet-100 self-end'
                  : 'bg-white border border-gray-100 self-start'
              } ${msg.deletedForAll ? 'opacity-60' : ''} ${isSelected ? 'bg-green-50 border-green-200' : ''}`}
            >
              {isSelectionMode && (
                <View className="absolute -top-2 -left-2 bg-green-500 rounded-full w-6 h-6 items-center justify-center z-10">
                  <Text className="text-white text-xs font-bold">{isSelected ? '✓' : ''}</Text>
                </View>
              )}

              {replyToMessage && (
                <View className="mb-2 p-2 bg-[#F5F3FF] border-l-2 border-[#D2C9FF]">
                  <Text className="text-xs font-medium text-gray-600">
                    {replyToMessage.senderId === currentUser.profileId
                      ? 'You'
                      : profile?.name || 'User'}
                  </Text>
                  <Text className="text-xs text-gray-700" numberOfLines={1}>
                    {replyToMessage.deletedForAll
                      ? 'This message has been deleted'
                      : replyToMessage.content.text || 'Media message'}
                  </Text>
                </View>
              )}

              <>
                {hasMedia && renderMediaDisplay(msg.content.media as any, isMyMessage, msg)}

                {hasText &&
                  (msg.deletedForAll ? (
                    <Text className="text-sm text-gray-900">This message has been deleted</Text>
                  ) : (
                    renderText(msg.content.text!)
                  ))}

                {hasLinks && !msg.deletedForAll && <LinkPreview url={urls[0]} />}
              </>
              <Text className="text-xs text-gray-400 mt-1 text-right">
                {new Date(msg.createdAt).toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </Text>
            </Pressable>
          </SwipeableMessage>
        </View>
      </View>
    );
  };

  const renderItem = ({ item, index }: { item: MessageI; index: number }) => {
    return renderMessage(item, index);
  };

  const getBottomSheetHeight = (type: 'media' | 'delete' | 'chat' | 'message') => {
    const baseHeight = RFPercentage(7);
    const itemHeight = RFPercentage(4);
    const padding = RFPercentage(4);

    switch (type) {
      case 'media':
        return baseHeight + itemHeight * 2 + padding;
      case 'delete':
        const deleteItems = canDeleteSelectedForEveryone() ? 2 : 1;
        return baseHeight + itemHeight * deleteItems + padding;
      case 'chat':
        return baseHeight + itemHeight + padding;
      case 'message':
        let messageOptions = 2;
        if (selectedMessage && canEditMessage(selectedMessage)) messageOptions++;
        if (selectedMessage && selectedMessage.senderId === currentUser.profileId) messageOptions++;
        return baseHeight + itemHeight * messageOptions;
      default:
        return RFPercentage(25);
    }
  };

  if (loading) {
    return (
      <SafeArea>
        <View className="flex-1 justify-center items-center bg-white">
          <ActivityIndicator size="small" color="#448600" />
          <Text className="text-gray-600 mt-3 text-base">Loading chat...</Text>
        </View>
      </SafeArea>
    );
  }

  const headerHeight = 60;
  const availableHeight = screenHeight - headerHeight - insets.top;

  const isTextInputDisabled = Boolean(sending || isRecording || isAudioUploading);
  const isSendButtonDisabled = Boolean(
    sending ||
      isRecording ||
      isAudioUploading ||
      (!messageText.trim() && !mediaPreview.length && !recordedAudioFile && !editingMessageId) ||
      mediaPreview.some((item) => item.isUploading),
  );
  const isAttachmentDisabled = Boolean(
    sending ||
      mediaPreview.length > 0 ||
      isRecording ||
      recordedAudioFile ||
      isAudioUploading ||
      editingMessageId !== null,
  );
  const isMicrophoneDisabled = Boolean(
    sending || mediaPreview.length > 0 || isAudioUploading || editingMessageId !== null,
  );
  const isDeleteButtonDisabled = Boolean(selectedMessages.size === 0);

  return (
    <SafeArea>
      <View className="flex-1 bg-white">
        <View
          className="flex-row items-center gap-2 px-2 border-b border-gray-200 bg-white z-10"
          style={{ height: headerHeight }}
        >
          <BackButton label="" onBack={onBack} />
          {isSelectionMode ? (
            <View className="flex-1 flex-row items-center justify-between">
              <Text className="text-base font-medium">{selectedMessages.size} selected</Text>
              <Pressable
                onPress={handleDeleteSelected}
                className="p-2 rounded-full"
                disabled={isDeleteButtonDisabled}
              >
                <Trash color="#ef4444" width={2} height={2} />
              </Pressable>
            </View>
          ) : (
            <View className="flex-1 flex-row items-center justify-between">
              <View className="flex-row items-center gap-2">
                <UserAvatar
                  avatarUri={profile?.avatar || ''}
                  name={profile?.name || ''}
                  width={37}
                  height={37}
                />
                <View>
                  <Text className="text-base font-medium">{profile?.name || 'User'}</Text>
                  <Text className="text-xs text-gray-500">
                    {otherUserTyping
                      ? 'Typing...'
                      : isUserOnline
                        ? 'Online'
                        : lastSeen
                          ? `Last seen ${lastSeen}`
                          : 'Offline'}
                  </Text>
                </View>
              </View>
              <Pressable onPress={() => setClearChatOptionsVisible(true)} className="p-2">
                <HorizontalEllipsis color="#666666" width={4} height={4} />
              </Pressable>
            </View>
          )}
        </View>

        <View className="flex-1" style={{ height: availableHeight }}>
          <FlatList
            ref={flatListRef}
            data={messages}
            renderItem={renderItem}
            keyExtractor={(item) => item.id}
            windowSize={50}
            disableVirtualization={true}
            contentContainerStyle={{
              paddingBottom: 70 + insets.bottom,
            }}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={refreshMessages}
                tintColor="#448600"
              />
            }
            onEndReached={() => {
              if (!loadingMore && messages.length > 0) {
                handleLoadMore();
              }
            }}
            onEndReachedThreshold={0.1}
            bounces={true}
            removeClippedSubviews={false}
            nestedScrollEnabled={false}
            ListHeaderComponent={
              loadingMore ? (
                <View className="py-4">
                  <ActivityIndicator size="small" color="#448600" />
                </View>
              ) : null
            }
          />
        </View>

        <ChatInput
          isSelectionMode={isSelectionMode}
          inputTranslateY={inputTranslateY}
          replyPreview={replyPreview}
          handleCloseReply={handleCloseReply}
          mediaPreview={mediaPreview}
          renderMediaPreview={renderMediaPreview}
          isRecording={isRecording}
          recordingTime={recordingTime}
          handleMicrophonePress={handleMicrophonePress}
          recordedAudioFile={recordedAudioFile}
          renderAudioPreview={renderAudioPreview}
          textInputRef={textInputRef}
          messageText={messageText}
          handleTextInputChange={handleTextInputChange}
          isTextInputDisabled={isTextInputDisabled}
          handleSendMessage={handleSendMessage}
          isSendButtonDisabled={isSendButtonDisabled}
          isAudioUploading={isAudioUploading}
          handleAttachment={handleAttachment}
          isAttachmentDisabled={isAttachmentDisabled}
          isMicrophoneDisabled={isMicrophoneDisabled}
        />

        <BottomSheet
          onModalHide={handleModalHide}
          height={getBottomSheetHeight('media')}
          visible={mediaOptionsVisible}
          onClose={() => {
            setMediaOptionsVisible(false);
            setPendingAction(null);
          }}
        >
          <OptionsMenu>
            <OptionItem label="PDF Document" onPress={handleFilePress} />
            <View className="h-[1px] bg-gray-200" />
            <OptionItem label="Gallery" onPress={handleGalleryPress} />
          </OptionsMenu>
        </BottomSheet>

        {!selectedMessage?.deletedForAll && (
          <BottomSheet
            onModalHide={() => {}}
            height={getBottomSheetHeight('message')}
            visible={messageOptionsVisible}
            onClose={() => setMessageOptionsVisible(false)}
          >
            <OptionsMenu>
              <OptionItem label="Reply" onPress={handleReplyToMessage} />
              <View className="h-[1px] bg-gray-200" />
              {selectedMessage && canEditMessage(selectedMessage) && (
                <>
                  <OptionItem label="Edit" onPress={handleEdit} />
                  <View className="h-[1px] bg-gray-200" />
                </>
              )}
            </OptionsMenu>
          </BottomSheet>
        )}

        <BottomSheet
          onModalHide={() => {}}
          height={getBottomSheetHeight('delete')}
          visible={deleteOptionsVisible}
          onClose={() => setDeleteOptionsVisible(false)}
        >
          <OptionsMenu>
            <OptionItem label="Delete for me" onPress={handleDeleteSelectedForMe} />
            <View className="h-[1px] bg-gray-200" />
            {canDeleteSelectedForEveryone() && (
              <>
                <OptionItem label="Delete for everyone" onPress={handleDeleteSelectedForEveryone} />
                <View className="h-[1px] bg-gray-200" />
              </>
            )}
          </OptionsMenu>
        </BottomSheet>

        <BottomSheet
          onModalHide={() => {}}
          height={getBottomSheetHeight('chat')}
          visible={clearChatOptions}
          onClose={() => setClearChatOptionsVisible(false)}
        >
          <OptionsMenu>
            <OptionItem
              label="Clear chat"
              onPress={() => {
                setClearChatOptionsVisible(false);
                setTimeout(() => {
                  setClearChatVisible(true);
                }, 300);
              }}
            />
          </OptionsMenu>
        </BottomSheet>

        <CustomModal
          isVisible={clearChatVisible}
          onCancel={() => {
            setClearChatVisible(false);
          }}
          onConfirm={() => {
            handleConfirmClearChat();
          }}
          title="Clear Chat"
          description="Are you sure you want to clear this chat? This action cannot be undone."
          confirmButtonVariant="danger"
          cancelText="Cancel"
          confirmText="Clear"
        />

        {selectedImagePost && (
          <ImageViewer
            isVisible={imageViewerVisible}
            onClose={() => {
              setImageViewerVisible(false);
              setSelectedImagePost(null);
            }}
            post={selectedImagePost}
          />
        )}
      </View>
    </SafeArea>
  );
};

export default ChatScreen;
