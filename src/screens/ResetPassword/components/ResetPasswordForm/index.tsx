/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { Controller } from 'react-hook-form';
import Button from '@/src/components/Button';
import TextInput from '@/src/components/TextInput';
import TextView from '@/src/components/TextView';
import { ResetPasswordFormProps } from './types';
import { useResetPassword } from './useHook';

const ResetPasswordForm = ({ email, onSuccess }: ResetPasswordFormProps) => {
  const { methods, isSubmitting, handleSubmit } = useResetPassword(email, onSuccess);

  const {
    control,
    formState: { isValid, errors },
    watch,
  } = methods;

  const password = watch('password');

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <View className="px-5">
          <View className="my-8">
            <TextView
              title="Create new password"
              subtitle="Please enter your new password here"
              titleClassName="font-bold"
            />
          </View>
          <View>
            <Controller
              control={control}
              name="password"
              rules={{
                required: 'Password is required',
                minLength: {
                  value: 8,
                  message: 'Password must be at least 8 characters long',
                },
                maxLength: {
                  value: 32,
                  message: 'Password can be at most 32 characters long',
                },
                validate: (value) => {
                  if (!/[A-Z]/.test(value)) {
                    return 'Password must contain at least one uppercase letter';
                  }
                  if (!/[a-z]/.test(value)) {
                    return 'Password must contain at least one lowercase letter';
                  }
                  if (!/[0-9]/.test(value)) {
                    return 'Password must contain at least one number';
                  }
                  if (!/[@$!%*?&^#]/.test(value)) {
                    return 'Password must contain at least one special character (@, $, !, %, *, ?, &, ^, #)';
                  }
                  return true;
                },
              }}
              render={({ field: { onChange, value }, fieldState: { error } }) => (
                <TextInput
                  label="New password"
                  value={value}
                  placeholder="New password"
                  onChangeText={onChange}
                  type="password"
                  error={error?.message}
                />
              )}
            />
          </View>
          <View className="mt-6">
            <Controller
              control={control}
              name="confirmPassword"
              rules={{
                required: 'Please confirm your password',
                validate: (value) => value === password || 'Passwords do not match',
              }}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  label="Confirm password"
                  value={value}
                  placeholder="Confirm new password"
                  onChangeText={onChange}
                  type="password"
                  error={errors.confirmPassword?.message}
                />
              )}
            />
          </View>
          <View className="mt-8">
            <Button
              label="Create password"
              onPress={methods.handleSubmit(handleSubmit)}
              variant={isValid ? 'primary' : 'tertiary'}
              disabled={!isValid || isSubmitting}
              loading={isSubmitting}
            />
          </View>
        </View>
      </KeyboardAvoidingView>
    </TouchableWithoutFeedback>
  );
};

export default ResetPasswordForm;
