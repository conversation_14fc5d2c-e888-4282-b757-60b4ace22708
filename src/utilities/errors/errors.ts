/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { resetUserState } from '@/src/redux/slices/user/userSlice';
import { store } from '@/src/redux/store';
import { VoidFnI } from '@/src/types/common/function';
import APIResError from '@/src/errors/networks/APIResError';
import AppError from '@/src/errors/networks/AppError';
import useStorage from '../../hooks/storage';
import { capitalizeFirstLetter } from '../data/string';
import { showToast } from '../toast';

const { clearAllStorage } = useStorage();
export const handleError = (
  error: APIResError | AppError | Error | unknown,
  {
    handle4xxError,
    handle5xxError,
    silent = false,
  }: {
    handle4xxError?: VoidFnI;
    handle5xxError?: VoidFnI;
    silent?: boolean;
  } = {},
) => {
  let message = 'Something went wrong!';
  let status: number | undefined;

  if (error instanceof APIResError) {
    status = error.status;
    message = capitalizeFirstLetter(error.message) || message;

    if (status === 401) {
      store.dispatch(resetUserState());
      clearAllStorage().then(() => {
        if (!silent) {
          showToast({ message: 'Session expired. Please login again.', type: 'error' });
        }
      });
      return;
    }

    if (status >= 400 && status < 500) {
      handle4xxError?.();
      if (!silent) {
        showToast({ message, type: 'error' });
      }
      return;
    }

    if (status >= 500) {
      handle5xxError?.();
      if (!silent) {
        showToast({ message: 'Server error. Please try again later.', type: 'error' });
      }
      return;
    }

    if (!silent) {
      showToast({ message, type: 'error' });
    }
    return;
  }

  if (error instanceof AppError) {
    message = capitalizeFirstLetter(error.message) || message;
  } else if (error instanceof Error) {
    message = capitalizeFirstLetter(error.message) || message;
  } else if (typeof error === 'string') {
    message = capitalizeFirstLetter(error) || message;
  } else {
  }

  if (!silent) {
    showToast({ message, type: 'error' });
  }
};
