import { useState, useEffect } from 'react';
import { RouteProp, useRoute } from '@react-navigation/native';
import { showToast } from '@/src/utilities/toast';
import { HomeStackParamListI } from '@/src/navigation/types';
import { fetchReactionsAPI } from '@/src/networks/content/reaction';
import { ReactionExternalClientI } from '@/src/networks/content/types';
import { UseLikesListResult } from './types';

const PAGE_SIZE = 10;

const useLikesList = (postId: string): UseLikesListResult => {
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [reactions, setReactions] = useState<ReactionExternalClientI>({
    reactions: [],
    totalCount: 0,
  });

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const route = useRoute<RouteProp<HomeStackParamListI, 'Likes'>>();

  const type = route.params.type;

  useEffect(() => {
    const loadInitialReactions = async () => {
      if (!postId) return;
      setLoading(true);
      try {
        const result = await fetchReactionsAPI({ postId, page, pageSize: PAGE_SIZE });
        const resultReactions = result.reactions || [];
        const resultTotal = result.totalCount || 0;
        setReactions({ reactions: resultReactions, totalCount: resultTotal });
        setHasMore(resultReactions.length > 0 && resultReactions.length < resultTotal);
        setPage(0);
      } catch (error) {
        triggerErrorBoundary(
          new Error(
            `Failed to load reactions: ${error instanceof Error ? error.message : 'Unknown error'}`,
          ),
        );
      } finally {
        setLoading(false);
      }
    };

    loadInitialReactions();
  }, [postId, type]);

  const handleRefresh = async () => {
    if (refreshing || !postId) return;
    setRefreshing(true);
    try {
      const result = await fetchReactionsAPI({ postId, page, pageSize: PAGE_SIZE });
      const resultReactions = result.reactions || [];
      const resultTotal = result.totalCount || 0;
      setReactions({ reactions: resultReactions, totalCount: resultTotal });
      setPage(0);
      setHasMore(resultReactions.length === PAGE_SIZE && resultReactions.length < resultTotal);
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Error',
        description: 'Failed to refresh reactions',
      });
    } finally {
      setRefreshing(false);
    }
  };

  const handleLoadMore = async () => {
    if (loading || refreshing || !hasMore || !postId) return;
    const nextPage = page + 1;
    setLoading(true);
    try {
      const result = await fetchReactionsAPI({ postId, page: nextPage, pageSize: PAGE_SIZE });
      const resultReactions = result.reactions || [];
      const resultTotal = result.totalCount || 0;
      setReactions((prev) => ({
        reactions: [...prev.reactions, ...resultReactions],
        totalCount: resultTotal,
      }));
      setHasMore(reactions.reactions.length < resultTotal);
      setPage(nextPage);
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Error',
        description: 'Failed to load more reactions',
      });
    } finally {
      setLoading(false);
    }
  };

  return {
    reactions,
    loading,
    refreshing,
    handleRefresh,
    handleLoadMore,
  };
};

export default useLikesList;
