import type React from 'react';
import { Pressable, View } from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  runOnJS,
  interpolate,
  clamp,
} from 'react-native-reanimated';
import Send from '@/src/assets/svgs/Send';
import type { SwipeableMessagePropsI } from './types';

const SwipeableMessage: React.FC<SwipeableMessagePropsI> = ({
  message,
  onReply,
  onLongPress,
  children,
}) => {
  const translateX = useSharedValue(0);
  const opacity = useSharedValue(0);
  const SWIPE_THRESHOLD = 60;

  const panGesture = Gesture.Pan()
    .activeOffsetX(15)
    .failOffsetY([-25, 25])
    .minDistance(10)
    .onUpdate((event) => {
      const isHorizontalSwipe = Math.abs(event.translationX) > Math.abs(event.translationY);

      if (event.translationX > 0 && isHorizontalSwipe && !message.deletedForAll) {
        const clampedTranslateX = clamp(event.translationX, 0, SWIPE_THRESHOLD * 1.5);
        translateX.value = clampedTranslateX;
        opacity.value = interpolate(clampedTranslateX, [0, SWIPE_THRESHOLD], [0, 1], 'clamp');
      }
    })
    .onEnd((event) => {
      if (
        event.translationX > SWIPE_THRESHOLD &&
        Math.abs(event.translationX) > Math.abs(event.translationY) &&
        !message.deletedForAll
      ) {
        runOnJS(onReply)(message);
      }

      translateX.value = withSpring(0);
      opacity.value = withSpring(0);
    });

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }],
  }));

  const replyIconStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [
      {
        scale: interpolate(opacity.value, [0, 1], [0.5, 1], 'clamp'),
      },
    ],
  }));

  return (
    <View className="relative">
      <Animated.View
        style={[replyIconStyle]}
        className="absolute left-2 top-1/2 -translate-y-1/2 z-10"
      >
        <View className="w-8 h-8 bg-gray-300 rounded-full items-center justify-center">
          <Send color="#000000" width={2} height={2} />
        </View>
      </Animated.View>
      <GestureDetector gesture={panGesture}>
        <Animated.View style={animatedStyle}>
          <Pressable onLongPress={() => onLongPress(message)} disabled={message.deletedForAll}>
            {children}
          </Pressable>
        </Animated.View>
      </GestureDetector>
    </View>
  );
};

export default SwipeableMessage;
