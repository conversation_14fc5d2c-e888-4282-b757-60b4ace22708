import { VariantsType } from './types';

export const VARIANTS: VariantsType = {
  primary: {
    container: 'hover:bg-green-800 text-white rounded px-3 py-3 gap-2',
    containerStyle: { backgroundColor: '#448600' },
    label: 'text-white',
    spinnerColor: '#FFFFFF',
  },
  secondary: {
    container:
      'bg-white border border-gray-400 hover:bg-gray-100 text-black rounded px-3 py-3 gap-2',
    label: 'text-black',
    spinnerColor: '#0A0A0A',
  },
  tertiary: {
    container: 'bg-gray-300 hover:bg-gray-400 text-gray-700 rounded px-3 py-3 gap-2',
    label: 'text-gray-700',
    spinnerColor: '#525252',
  },
  outline: {
    container:
      'bg-transparent border border-gray-400 hover:bg-gray-100 text-black rounded-full px-3 py-3 gap-2',
    label: 'text-black',
    spinnerColor: '#000000',
  },
  primaryOutline: {
    container:
      'bg-transparent border hover:bg-green-50 text-green-700 rounded-full px-3 py-3 gap-2',
    containerStyle: { borderColor: '#448600' },
    label: { color: '#448600' },
    spinnerColor: '#448600',
  },
  oauth: {
    container:
      'bg-white border border-gray-400 hover:bg-gray-100 text-black rounded px-3 py-3 gap-2',
    disabledContainer:
      'bg-white border border-gray-400 text-black rounded px-3 py-3 gap-2 opacity-50',
    label: 'text-black',
    spinnerColor: '#0A0A0A',
  },
};
