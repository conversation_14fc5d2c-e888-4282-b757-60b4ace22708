/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { View } from 'react-native';
import { ProgressBarProps } from './types';

const ProgressBar = ({
  progress,
  barColor = '#448600',
  backgroundColor = '#E5E5E5',
  height = 16,
  rounded = true,
}: ProgressBarProps) => {
  const clamped = Math.min(Math.max(progress, 0), 100);
  const radius = rounded ? height / 2 : 0;

  return (
    <View
      className="w-1/2"
      style={{
        height,
        backgroundColor,
        borderRadius: radius,
        overflow: 'hidden',
      }}
    >
      <View
        style={{
          width: `${clamped}%`,
          height: '100%',
          backgroundColor: barColor,
          borderRadius: radius,
        }}
      />
    </View>
  );
};

export default ProgressBar;
