/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { FlatList } from 'react-native';
import { PostExternalClientI, PostMediaI } from '@/src/networks/content/types';

export interface Match {
  type: 'url' | 'hashtag' | 'highlight';
  text: string;
  index?: number;
  length: number;
}

export interface MediaRendererProps {
  item: PostMediaI;
  isMultiple: boolean;
  onPress?: () => void;
  index?: number;
}

export interface UserPostPropsI {
  post: PostExternalClientI;
  type?: 'USER_POST' | 'SCRAPBOOK_POST';
  onLikePress?: () => void;
  onCommentPress: () => void;
  onDeletePress?: () => void;
  onEditPress?: () => void;
  onLikeCountPress: () => void;
  onReportPress?: () => void;
  onSharePress?: () => void;
  isOwnPost?: boolean;
  isCommentEnabled?: boolean;
  parentScrollRef?: React.RefObject<FlatList | null>;
  postIndex?: number;
  highlightText?: string;
}
