/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const Pending: React.FC<FilledIconPropsI> = ({
  width = 2.1,
  height = 2,
  fill = '#CA8A04',
  color,
  disabled,
  accessibilityLabel = 'Pending',
  ...props
}) => {
  const fillColor = color || fill;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 17 16"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path d="M15.167 8A6.667 6.667 0 111.834 8a6.667 6.667 0 0113.333 0z" fill={fillColor} />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.5 4.833a.5.5 0 01.5.5v2.46l1.52 1.52a.5.5 0 11-.707.707L8.146 8.353A.5.5 0 018 8V5.333a.5.5 0 01.5-.5z"
        fill="#fff"
      />
    </Svg>
  );
};

export default Pending;
