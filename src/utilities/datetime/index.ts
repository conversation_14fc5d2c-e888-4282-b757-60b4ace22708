/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { DateFormat, TimeFormat, RelativeTimeOptions, DateRange, DateTimeConfig } from './types';

const defaultConfig: DateTimeConfig = {
  shortFormat: 'MMM D, YYYY',
  longFormat: 'MMMM D, YYYY',
  timeFormat: 'h:mm A',
  dateTimeFormat: 'MMM D, YYYY [at] h:mm A',
  calendar: {
    sameDay: '[Today at] h:mm A',
    nextDay: '[Tomorrow at] h:mm A',
    nextWeek: 'dddd [at] h:mm A',
    lastDay: '[Yesterday at] h:mm A',
    lastWeek: '[Last] dddd [at] h:mm A',
    sameElse: 'MMM D, YYYY [at] h:mm A',
  },
};

let config = { ...defaultConfig };

export const configureDateUtils = (newConfig: Partial<DateTimeConfig>) => {
  config = { ...config, ...newConfig };
};

const pad = (n: number) => (n < 10 ? `0${n}` : `${n}`);

const getMonthShort = (d: Date) => d.toLocaleString('default', { month: 'short' });
const getMonthLong = (d: Date) => d.toLocaleString('default', { month: 'long' });
const getWeekday = (d: Date) => d.toLocaleString('default', { weekday: 'long' }).replace(',', '');

const formatDateInternal = (date: Date, format: string) => {
  const h = date.getHours() % 12 || 12;
  const map: { [key: string]: string | number } = {
    YYYY: date.getFullYear(),
    MMMM: getMonthLong(date),
    MMM: getMonthShort(date),
    MM: pad(date.getMonth() + 1),
    DD: pad(date.getDate()),
    D: date.getDate(),
    dddd: getWeekday(date),
    hh: pad(h),
    h: h,
    H: pad(date.getHours()),
    mm: pad(date.getMinutes()),
    ss: pad(date.getSeconds()),
    A: date.getHours() >= 12 ? 'PM' : 'AM',
  };

  return format.replace(/YYYY|MMMM|MMM|MM|DD|D|dddd|hh|h|H|mm|ss|A/g, (token: string) =>
    String(map[token]),
  );
};

export const formatDate = (date: Date | string | number, format?: DateFormat): string => {
  if (!date) return '';
  const d = new Date(date);
  if (format === 'short') {
    return formatDateInternal(d, config.shortFormat);
  } else if (format === 'long') {
    return formatDateInternal(d, config.longFormat);
  } else if (typeof format === 'string') {
    return formatDateInternal(d, format);
  }
  return formatDateInternal(d, config.shortFormat);
};

export const formatTime = (date: Date | string | number, format?: TimeFormat): string => {
  if (!date) return '';
  const d = new Date(date);
  if (format === 'short') {
    return formatDateInternal(d, 'h:mm A');
  } else if (format === 'long') {
    return formatDateInternal(d, 'h:mm:ss A');
  } else if (typeof format === 'string') {
    return formatDateInternal(d, format);
  }
  return formatDateInternal(d, config.timeFormat);
};

export const formatDateTime = (date: Date | string | number, format?: string): string => {
  if (!date) return '';
  const d = new Date(date);
  return formatDateInternal(d, format || config.dateTimeFormat);
};

export const getRelativeTime = (
  date: Date | string | number,
  options?: RelativeTimeOptions,
): string => {
  if (!date) return '';
  const target = new Date(date).getTime();
  const now = Date.now();
  const diff = Math.floor((now - target) / 1000);

  if (options?.threshold && diff <= options.threshold) return 'Just now';

  const absDiff = Math.abs(diff);
  if (absDiff < 60) return `${absDiff}s ago`;
  if (absDiff < 3600) return `${Math.floor(absDiff / 60)}m ago`;
  if (absDiff < 86400) return `${Math.floor(absDiff / 3600)}h ago`;
  if (absDiff < 604800) return `${Math.floor(absDiff / 86400)}d ago`;

  return formatDateInternal(new Date(date), config.shortFormat);
};

export const formatSocialTime = (date: Date | string | number): string => {
  if (!date) return '';
  const d = new Date(date);
  const now = new Date();
  const diffMs = now.getTime() - d.getTime();
  const diffMin = Math.floor(diffMs / 60000);
  const diffHr = Math.floor(diffMs / 3600000);
  const diffDay = Math.floor(diffMs / 86400000);

  if (diffMin < 1) return 'Just now';
  if (diffMin < 60) return `${diffMin}m ago`;
  if (diffHr < 24) return `${diffHr}h ago`;
  if (diffDay < 7) {
    const today = new Date();
    const yesterday = new Date();
    yesterday.setDate(today.getDate() - 1);
    if (isSameDay(d, today)) return `Today at ${formatTime(d)}`;
    if (isSameDay(d, yesterday)) return `Yesterday at ${formatTime(d)}`;
    return `${getWeekday(d)} at ${formatTime(d)}`;
  }

  return formatDateInternal(d, 'MMM D, YYYY');
};

export const isToday = (date: Date | string | number): boolean => {
  const d = new Date(date);
  const now = new Date();
  return d.toDateString() === now.toDateString();
};

export const isYesterday = (date: Date | string | number): boolean => {
  const d = new Date(date);
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return d.toDateString() === yesterday.toDateString();
};

export const getDateRange = (range: DateRange): { start: Date; end: Date } => {
  const now = new Date();
  const end = new Date(now.setHours(23, 59, 59, 999));
  const start = new Date();

  switch (range) {
    case 'today':
      start.setHours(0, 0, 0, 0);
      break;
    case 'yesterday':
      start.setDate(start.getDate() - 1);
      start.setHours(0, 0, 0, 0);
      break;
    case 'week':
      start.setDate(start.getDate() - 7);
      start.setHours(0, 0, 0, 0);
      break;
    case 'month':
      start.setDate(start.getDate() - 30);
      start.setHours(0, 0, 0, 0);
      break;
    case 'year':
      start.setFullYear(start.getFullYear() - 1);
      start.setHours(0, 0, 0, 0);
      break;
    default:
      start.setHours(0, 0, 0, 0);
  }

  return { start, end };
};

export const formatDuration = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  const hrs = Math.floor(mins / 60);
  const mm = pad(mins % 60);
  const ss = pad(secs);
  if (hrs > 0) {
    return `${hrs}:${mm}:${ss}`;
  }
  return `${mins}:${ss}`;
};

export const getTimeRemaining = (date: Date | string | number): string => {
  const target = new Date(date).getTime();
  const now = Date.now();

  if (target < now) return 'Expired';

  const diffMs = target - now;
  const mins = Math.floor(diffMs / 60000);
  const hrs = Math.floor(mins / 60);
  const days = Math.floor(hrs / 24);

  if (days > 0) return `${days}d ${hrs % 24}h remaining`;
  if (hrs > 0) return `${hrs}h ${mins % 60}m remaining`;
  return `${mins}m remaining`;
};

const isSameDay = (a: Date, b: Date): boolean => {
  return a.toDateString() === b.toDateString();
};

export const getYearsMonths = (years: number, months: number) => {
  const parts = [];
  if (years > 0) parts.push(`${years} Year${years > 1 ? 's' : ''}`);
  if (months > 0) parts.push(`${months} Month${months > 1 ? 's' : ''}`);
  return parts.join(' ');
};
