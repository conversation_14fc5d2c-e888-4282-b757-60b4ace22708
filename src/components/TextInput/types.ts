/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { TextInputProps as RNTextInputProps } from 'react-native';

export interface TextInputProps extends Omit<RNTextInputProps, 'ref'> {
  type?: 'text' | 'password' | 'textarea';
  label?: string;
  error?: string;
  className?: string;
  inputClassName?: string;
  labelClassName?: string;
  errorClassName?: string;
  numberOfLines?: number;
}
