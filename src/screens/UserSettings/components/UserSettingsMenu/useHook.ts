import { useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useDispatch } from 'react-redux';
import {
  deleteAccountAsync,
  signOutAsync,
  deactivateAccountAsync,
} from '@/src/redux/slices/user/userSlice';
import type { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import useStorage from '@/src/hooks/storage';
import type { SettingsState, UseSettingsHookReturn } from './types';

const defaultSettings: SettingsState = {};

export const useSettings = (userId: string): UseSettingsHookReturn => {
  const [settings, setSettings] = useState<SettingsState>(defaultSettings);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isSignOutModalVisible, setIsSignOutModalVisible] = useState<boolean>(false);
  const [isDeleteAccountModalVisible, setIsDeleteAccountModalVisible] = useState<boolean>(false);
  const [isDeactivateAccountModalVisible, setIsDeactivateAccountModalVisible] =
    useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const { clearAllStorage } = useStorage();
  const [isDeleting, setIsDeleting] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);

  const handleTermsAccepted = (value: boolean) => {
    setTermsAccepted(value);
  };

  const dispatch = useDispatch<AppDispatch>();

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const loadSettings = async () => {
    try {
      const storedSettings = await AsyncStorage.getItem(`@user_settings_${userId}`);
      if (storedSettings) {
        const parsedSettings = JSON.parse(storedSettings);
        setSettings({ ...defaultSettings, ...parsedSettings });
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
      setSettings(defaultSettings);
    }
  };

  useEffect(() => {
    const initializeSettings = async () => {
      try {
        setIsLoading(true);
        setError(null);
        await loadSettings();
      } catch (error) {
        const errorMessage = `Failed to initialize settings: ${error instanceof Error ? error.message : 'Unknown error'}`;
        triggerErrorBoundary(new Error(errorMessage));
      } finally {
        setIsLoading(false);
      }
    };

    initializeSettings();
  }, [userId]);

  const signOut = async (): Promise<void> => {
    return new Promise((resolve) => {
      setIsSignOutModalVisible(true);
      resolve();
    });
  };

  const deleteAccount = async (): Promise<void> => {
    return new Promise((resolve) => {
      setIsDeleteAccountModalVisible(true);
      resolve();
    });
  };

  const deactivateAccount = async (): Promise<void> => {
    return new Promise((resolve) => {
      setIsDeactivateAccountModalVisible(true);
      resolve();
    });
  };

  const handleConfirmSignOut = async (): Promise<void> => {
    try {
      setIsSignOutModalVisible(false);
      await new Promise((resolve) => setTimeout(resolve, 500));
      await dispatch(signOutAsync()).unwrap();
      await clearAllStorage();
    } catch (error) {
      const errorMessage = `Failed to sign out: ${error instanceof Error ? error.message : 'Unknown error'}`;
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Sign Out Failed',
            description: 'Unable to sign out. Please try again.',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later.',
            type: 'error',
          });
        },
      });

      triggerErrorBoundary(new Error(errorMessage));
    }
  };

  const handleCancelSignOut = (): void => {
    setIsSignOutModalVisible(false);
  };

  const handleConfirmDeleteAccount = async (): Promise<void> => {
    try {
      setIsDeleting(true);
      await dispatch(deleteAccountAsync()).unwrap();
      await clearAllStorage();
      await AsyncStorage.removeItem(`@user_settings_${userId}`);

      showToast({
        message: 'Account Deleted',
        description: 'Your account will be deleted in 30 days',
        type: 'success',
      });
    } catch (error) {
      const errorMessage = `Failed to delete account: ${error instanceof Error ? error.message : 'Unknown error'}`;
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Delete Account Failed',
            description: 'Unable to delete account. Please try again.',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later.',
            type: 'error',
          });
        },
      });

      triggerErrorBoundary(new Error(errorMessage));
    } finally {
      setIsDeleting(false);
      setIsDeleteAccountModalVisible(false);
      setTermsAccepted(false);
    }
  };

  const handleCancelDeleteAccount = (): void => {
    setIsDeleteAccountModalVisible(false);
    setTermsAccepted(false);
  };

  const handleConfirmDeactivateAccount = async (): Promise<void> => {
    try {
      setIsDeleting(true);
      await dispatch(deactivateAccountAsync()).unwrap();
      await clearAllStorage();
      await AsyncStorage.removeItem(`@user_settings_${userId}`);

      showToast({
        message: 'Account Deactivated',
        description: 'Your account has been deactivated',
        type: 'success',
      });
    } catch (error) {
      const errorMessage = `Failed to deactivate account: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`;
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Deactivate Account Failed',
            description: 'Unable to deactivate account. Please try again.',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later.',
            type: 'error',
          });
        },
      });

      triggerErrorBoundary(new Error(errorMessage));
    } finally {
      setIsDeleting(false);
      setIsDeactivateAccountModalVisible(false);
      setTermsAccepted(false);
    }
  };

  const handleCancelDeactivateAccount = (): void => {
    setIsDeactivateAccountModalVisible(false);
    setTermsAccepted(false);
  };

  return {
    settings,
    isLoading,
    signOut,
    deleteAccount,
    deactivateAccount,
    termsAccepted,
    isSignOutModalVisible,
    isDeleteAccountModalVisible,
    isDeactivateAccountModalVisible,
    handleConfirmSignOut,
    handleCancelSignOut,
    handleConfirmDeleteAccount,
    handleCancelDeleteAccount,
    handleConfirmDeactivateAccount,
    handleCancelDeactivateAccount,
    handleTermsAccepted,
    isDeleting,
  };
};
