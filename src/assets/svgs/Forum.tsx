/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path, Rect } from 'react-native-svg';
import { OutlinedIconPropsI } from './types';

const Forum: React.FC<Omit<OutlinedIconPropsI, 'onPress'>> = ({
  width = 3.96,
  height = 3.96,
  color = '#448600',
  fill,
  stroke = '#D4D4D4',
  strokeWidth = 1,
  accessibilityLabel = 'Explore',
  disabled,
  ...props
}) => {
  const fillColor = fill || color;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 32 32"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Rect
        x="0.5"
        y="0.5"
        width={RFPercentage(3.8)}
        height={RFPercentage(3.8)}
        rx="7.5"
        stroke={stroke}
        strokeWidth={strokeWidth}
      />
      <Path
        d="M3.616 10.924v-7.3H1.691C.76 3.624 0 4.478 0 5.52v8.87c0 1.043.76 1.897 1.69 1.897h2.422v1.954c0 .13.072.248.178.297.107.05.233.021.314-.07l1.947-2.181h4.16c.93 0 1.69-.854 1.69-1.896v-.003H6.73c-1.717 0-3.113-1.554-3.113-3.464z"
        fill={fillColor}
        transform="translate(6,7)"
      />
      <Path
        d="M18.076 0H6.73C5.67 0 4.805.97 4.805 2.157v8.767c0 1.187.866 2.157 1.925 2.157h6.432l2.943 3.267a.302.302 0 00.357.08.37.37 0 00.203-.34v-3.007h1.41c1.06 0 1.926-.97 1.926-2.157V2.157C20 .97 19.135 0 18.076 0zM14.42 10.317H7.654V9.273h6.767v1.044zm2.73-3.265H7.655V6.008h9.498v1.044zm0-3.265H7.655V2.743h9.498v1.044z"
        fill={fillColor}
        transform="translate(6,7)"
      />
    </Svg>
  );
};

export default Forum;
