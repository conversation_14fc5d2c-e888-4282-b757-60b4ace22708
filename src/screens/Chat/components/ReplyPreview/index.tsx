import { Pressable, Text, View } from 'react-native';
import Close from '@/src/assets/svgs/Close';
import { MessageI } from './types';

const ReplyPreview = ({ message, onClose }: { message: MessageI; onClose?: () => void }) => (
  <View className="mx-3 mt-1 mb-2">
    <View className="p-3 rounded-lg flex-row" style={{ backgroundColor: '#F5F3FF' }}>
      <View className="w-1 mr-3 rounded-full" style={{ backgroundColor: '#D2C9FF' }} />
      <View className="flex-1">
        <Text className="text-sm font-medium text-gray-800 mb-1">
          {message.user?.name || 'Unknown'}
        </Text>
        <Text className="text-sm text-gray-700" numberOfLines={2}>
          {message.deletedForAll
            ? 'This message was deleted'
            : message.content.text || 'Media message'}
        </Text>
      </View>
      {onClose && (
        <Pressable onPress={onClose} className="p-1 ml-2">
          <Close width={2} height={2} color="#6B7280" />
        </Pressable>
      )}
    </View>
  </View>
);

export default ReplyPreview;
