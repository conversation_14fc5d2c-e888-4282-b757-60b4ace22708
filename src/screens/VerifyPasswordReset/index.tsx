/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import SafeArea from '@/src/components/SafeArea';
import { AppStackParamListI } from '@/src/navigation/types';
import { VerifyPasswordResetForm } from './components/VerifyPasswordResetForm';

type VerifyPasswordResetRouteProp = RouteProp<AppStackParamListI, 'VerifyPasswordReset'>;

export function VerifyPasswordResetScreen() {
  const navigation = useNavigation();
  const route = useRoute<VerifyPasswordResetRouteProp>();
  const { email } = route.params;

  const handleBack = () => {
    navigation.goBack();
  };

  return (
    <SafeArea>
      <VerifyPasswordResetForm email={email} onBack={handleBack} />
    </SafeArea>
  );
}

export default VerifyPasswordResetScreen;
