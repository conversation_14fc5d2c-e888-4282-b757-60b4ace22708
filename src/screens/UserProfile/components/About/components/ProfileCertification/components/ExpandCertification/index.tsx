import { Text, View } from 'react-native';
import WarningBadgeIcon from '@/src/assets/svgs/WarningBadge';
import { ExpandCertificationPropsI } from './types';

export const CertificationExpand = ({
  certification,
  expiresIn,
}: {
  certification: ExpandCertificationPropsI;
  expiresIn: string | (string | number)[];
}) => {
  const isExpired = Array.isArray(expiresIn) && expiresIn.length === 0;
  const isUnlimited =
    Array.isArray(expiresIn) && expiresIn.length > 0 && expiresIn[0] === 'Unlimited';
  const [timeLeft, unit] = Array.isArray(expiresIn) ? expiresIn : [null, null];

  return (
    <View className="flex-row items-center">
      {expiresIn === 'unlimited' ? (
        <Text className="text-sm text-black">Unlimited Validity</Text>
      ) : (
        <>
          <Text className={`text-sm ${isExpired ? 'text-gray-400' : 'text-black'}`}>
            {isExpired
              ? 'Expired'
              : isUnlimited
                ? 'Unlimited validity'
                : unit === 'D'
                  ? `Expires in ${timeLeft} days`
                  : (timeLeft as number) > 6
                    ? `Valid for ${timeLeft} months`
                    : `Expires in ${timeLeft} months`}
          </Text>
          {!isExpired && !isUnlimited && (timeLeft as number) < 6 ? (
            <View className="p-1">
              <WarningBadgeIcon />
            </View>
          ) : null}
        </>
      )}
    </View>
  );
};
