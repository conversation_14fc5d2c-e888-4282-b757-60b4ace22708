import { Pressable, Text, View } from 'react-native';
import { formatSocialTime } from '@/src/utilities/datetime';
import { CommentBodyProps } from './types';

const CommentBody = ({
  item,
  isReply = false,
  onUserPress,
}: CommentBodyProps & { onUserPress?: () => void }) => {
  const name = item.Profile?.name;
  const designation = item.Profile?.designation?.name;
  const entity = item.Profile?.entity?.name;
  const timestamp = formatSocialTime(item.createdAt);

  return (
    <View className={`bg-backgroundGray p-3 rounded-lg ${isReply ? 'bg-gray-50' : ''}`}>
      <View className="flex-row justify-between items-start">
        <View className="flex-1 gap-1">
          <Pressable onPress={onUserPress}>
            <Text className="font-semibold text-sm text-gray-900">{name}</Text>
          </Pressable>
          {(designation || entity) && (
            <Pressable onPress={onUserPress}>
              <Text className="text-gray-500 text-xs" numberOfLines={1} ellipsizeMode="tail">
                {designation}
                {designation && entity ? ' at ' : ''}
                {entity}
              </Text>
            </Pressable>
          )}
        </View>
        <Text className="text-labelGray text-xs shrink-0">{timestamp}</Text>
      </View>
      <Text className="mt-3 text-sm leading-5 text-gray-800">{item.text}</Text>
    </View>
  );
};

export default CommentBody;
