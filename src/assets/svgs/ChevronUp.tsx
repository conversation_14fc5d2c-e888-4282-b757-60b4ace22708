/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const ChevronUp: React.FC<FilledIconPropsI> = ({
  width = 2.976,
  height = 2.976,
  fill = '#000',
  color,
  disabled,
  accessibilityLabel = 'Chevron Up',
  ...props
}) => {
  const fillColor = color || fill;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 24 24"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        d="M4.102 15.073l7.5-7.5a.562.562 0 01.795 0l7.5 7.5a.563.563 0 01-.795.795L12 8.767l-7.103 7.101a.563.563 0 01-.795-.795z"
        fill={fillColor}
      />
    </Svg>
  );
};

export default ChevronUp;
