/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { View } from 'react-native';
import Chip from '@/src/components/Chip';
import TextView from '@/src/components/TextView';
import { SkillsI } from './types';

const SkillsSection = ({ skills }: { skills: SkillsI }) => {
  return (
    <View className="mt-6">
      <TextView title="Skills acquired:" titleClassName="text-sm font-medium text-subLabelGray" />
      <View className="mt-2 flex-row flex-wrap gap-2">
        {skills.map((skill, index) => (
          <Chip label={skill} key={index} />
        ))}
      </View>
    </View>
  );
};
export default SkillsSection;
