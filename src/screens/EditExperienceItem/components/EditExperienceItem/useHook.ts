import type React from 'react';
import { useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, useFieldArray } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { selectAllDesignations } from '@/src/redux/selectors/experience';
import { selectAllSelections, selectSelectionByKey } from '@/src/redux/selectors/search';
import { clearSelectionAsync, setSelection } from '@/src/redux/slices/entitysearch/searchSlice';
import type { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import {
  addExperienceAsync,
  addShipExperience,
} from '@/src/redux/slices/experience/experienceSlice';
import { type AppDispatch, store } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { navigate } from '@/src/utilities/navigation';
import { showToast } from '@/src/utilities/toast';
import { apiCall } from '@/src/services/api';
import { generateExperiencePayload } from '../utils';
import { editExperienceSchema } from './schema';
import type {
  ApiResponseTypeI,
  DesignationsI,
  DesignationWithDateI,
  FieldTypeI,
  ExperienceFormDataI,
  UseEditExperienceItemReturn,
} from './types';

const useEditExperienceItem = (experienceId?: string): UseEditExperienceItemReturn => {
  const [loading, setLoading] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [isAddingDesignation, setIsAddingDesignation] = useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [localEntity, setLocalEntity] = useState<SearchResultI | undefined>();
  const [initialEntity, setInitialEntity] = useState<SearchResultI | undefined>();
  const [initialDesignations, setInitialDesignations] = useState<DesignationWithDateI[]>([]);
  const [error, setError] = useState<Error | null>(null);
  const [designationSelections, setDesignationSelections] = useState<(SearchResultI | undefined)[]>(
    [],
  );
  const [isInitialized, setIsInitialized] = useState<boolean>(false);

  const dispatch = useDispatch<AppDispatch>();
  const entity: SearchResultI = useSelector(selectSelectionByKey('entity'));
  const designationsFromRedux: DesignationsI[] = useSelector(selectAllDesignations);
  const allSelectionsFromRedux = useSelector(selectAllSelections);
  const navigation = useNavigation();

  const form = useForm<ExperienceFormDataI>({
    resolver: zodResolver(editExperienceSchema),
    mode: 'onChange',
    defaultValues: {
      designations: [],
    },
  });

  const { append } = useFieldArray({
    control: form.control,
    name: 'designations',
  });

  const watchedDesignations = form.watch('designations');

  const entityChanged =
    localEntity?.id !== initialEntity?.id ||
    localEntity?.name !== initialEntity?.name ||
    localEntity?.dataType !== initialEntity?.dataType;

  const designationsChanged =
    watchedDesignations.length !== initialDesignations.length ||
    watchedDesignations.some((designation, index) => {
      const initial = initialDesignations[index];
      if (!initial) return true;
      return (
        designation.designation?.id !== initial.designation?.id ||
        designation.fromDate !== initial.fromDate ||
        designation.toDate !== initial.toDate ||
        designation.isPresent !== initial.isPresent ||
        (designation.ships?.length || 0) !== (initial.ships?.length || 0) ||
        designation.ships?.some((ship, shipIndex) => {
          const initialShip = initial.ships?.[shipIndex];
          return !initialShip || ship.id !== initialShip.id;
        })
      );
    });

  const hasChanges = form.formState.isDirty || entityChanged || designationsChanged;

  const mappedDesignations: DesignationWithDateI[] = watchedDesignations
    ? watchedDesignations.map((item: any) => ({
        ...item,
        ships: item.ships || [],
        designation: item.designation || undefined,
        fromDate: item.fromDate || '',
        toDate: item.toDate || undefined,
      }))
    : [];

  const triggerErrorBoundary = (error: Error): void => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  useEffect(() => {
    if (!isInitialized) return;

    const currentDesignationsInForm = form.getValues('designations');
    const newDesignationSelections: (SearchResultI | undefined)[] = [];

    for (let i = 0; i < currentDesignationsInForm.length; i++) {
      const selection = allSelectionsFromRedux[`designation-${i}`];
      newDesignationSelections.push(selection);

      const currentDesignationInForm = currentDesignationsInForm[i].designation;
      if (
        selection &&
        (!currentDesignationInForm || currentDesignationInForm.id !== selection.id)
      ) {
        form.setValue(`designations.${i}.designation`, selection, { shouldDirty: true });
        form.trigger(`designations.${i}`);
      } else if (!selection && currentDesignationInForm) {
        form.setValue(`designations.${i}.designation`, undefined, { shouldDirty: true });
        form.trigger(`designations.${i}`);
      }
    }

    const hasSelectionChanges =
      newDesignationSelections.length !== designationSelections.length ||
      newDesignationSelections.some((sel, idx) => sel?.id !== designationSelections[idx]?.id);

    if (hasSelectionChanges) {
      setDesignationSelections(newDesignationSelections);
    }
  }, [allSelectionsFromRedux, isInitialized, form, designationSelections]);

  const clearAndReindexSelections = async (
    oldDesignations: any[],
    newDesignations: any[],
  ): Promise<void> => {
    const state = store.getState();
    const selections: any = selectAllSelections(state);
    const savedSelections: { [key: string]: SearchResultI } = {};

    oldDesignations.forEach((designation, index) => {
      const selectionKey = `designation-${index}`;
      const selection = selections[selectionKey];
      if (selection) {
        const stableId = designation.id || designation.experienceDesignationId || `temp-${index}`;
        savedSelections[stableId] = selection;
      }
    });

    const clearPromises = Object.keys(selections)
      .filter((key) => key.startsWith('designation-'))
      .map((key) => dispatch(clearSelectionAsync(key)));
    await Promise.all(clearPromises);
    await new Promise((resolve) => setTimeout(resolve, 50));

    const setPromises = newDesignations.map(async (designation, newIndex) => {
      const stableId = designation.id || designation.experienceDesignationId || `temp-${newIndex}`;
      const savedSelection = savedSelections[stableId];
      if (savedSelection) {
        dispatch(
          setSelection({
            key: `designation-${newIndex}`,
            value: savedSelection,
          }),
        );
      }
    });
    await Promise.all(setPromises);
    await new Promise((resolve) => setTimeout(resolve, 50));
  };

  const getFieldError = (fieldName: string, index: number): string | undefined => {
    const fieldErrors = form.formState.errors.designations?.[index];
    if (fieldErrors && typeof fieldErrors === 'object' && fieldName in fieldErrors) {
      const error = fieldErrors[fieldName as keyof typeof fieldErrors];
      if (error && typeof error === 'object' && 'message' in error) {
        return String(error.message);
      }
      return String(error);
    }
    return undefined;
  };

  const handleAddDesignation = (): void => {
    setIsAddingDesignation(true);
    const newDesignation: DesignationWithDateI = {
      id: undefined,
      designation: undefined,
      name: undefined,
      experienceDesignationId: undefined,
      fromDate: '',
      toDate: undefined,
      isPresent: false,
      ships: [],
    };
    append(newDesignation);
    setTimeout(() => {
      setIsAddingDesignation(false);
    }, 300);
  };

  const handleDesignationChange = (index: number, selectedDesignation: SearchResultI): void => {
    form.setValue(`designations.${index}.designation`, selectedDesignation);
    form.trigger(`designations.${index}`);
  };

  const handlePresentCheckbox = (index: number, isPresent: boolean): void => {
    form.setValue(`designations.${index}.isPresent`, isPresent);
    if (isPresent) {
      form.setValue(`designations.${index}.toDate`, undefined);
      form.clearErrors(`designations.${index}.toDate`);
    }
    form.trigger(`designations.${index}`);
  };

  const getDateChangeHandler = (index: number, field: 'fromDate' | 'toDate') => {
    return (dateValue: React.SetStateAction<Date>): void => {
      let date: Date;
      if (typeof dateValue === 'function') {
        const currentValue = form.getValues(`designations.${index}.${field}`);
        const currentDate = currentValue ? new Date(currentValue) : new Date();
        date = dateValue(currentDate);
      } else {
        date = dateValue;
      }
      if (date instanceof Date && !isNaN(date.getTime())) {
        const dateString: string = date.toISOString().split('T')[0];
        form.setValue(`designations.${index}.${field}`, dateString);
        form.clearErrors(`designations.${index}.${field}`);
        form.trigger(`designations.${index}`);
      }
    };
  };

  const fetchExperience = async (experienceId: string): Promise<void> => {
    try {
      setLoading(true);
      const response: ApiResponseTypeI = await apiCall(
        `/backend/api/v1/career/profile-experience/${experienceId}`,
        'GET',
        { isAuth: true },
      );
      const processedDesignations = response.designations.map((designation: any) => ({
        ...designation,
        fromDate: designation.fromDate || '',
        toDate: designation.toDate || undefined,
        isPresent: !designation.toDate || designation.toDate === null,
        ships: designation.ships || [],
      }));
      setLocalEntity(response.entity);
      setInitialEntity(response.entity);
      setInitialDesignations(processedDesignations);
      form.reset({ designations: processedDesignations });
      const initialSelections = processedDesignations.map(
        (designation: any) => designation.designation || undefined,
      );
      setDesignationSelections(initialSelections);
      processedDesignations.forEach((designation: any, index: number) => {
        if (designation.designation) {
          dispatch(
            setSelection({
              key: `designation-${index}`,
              value: designation.designation,
            }),
          );
        }
      });
      setIsInitialized(true);
    } catch (error: any) {
      triggerErrorBoundary(
        new Error(
          'Failed to load experience details: ' +
            (error instanceof Error ? error.message : 'Unknown error'),
        ),
      );
    } finally {
      setLoading(false);
    }
  };

  const refetch = (): void => {
    if (experienceId) {
      fetchExperience(experienceId);
    }
  };

  const clearFields = async (): Promise<void> => {
    await dispatch(clearSelectionAsync('entity'));
    const state = store.getState();
    const selections: any = selectAllSelections(state);
    const clearPromises = Object.keys(selections)
      .filter((key) => key.startsWith('designation') || key.startsWith('skill'))
      .map((key) => dispatch(clearSelectionAsync(key)));
    await Promise.all(clearPromises);
  };

  const clearSkillsSelections = async (): Promise<void> => {
    const state = store.getState();
    const selections: any = selectAllSelections(state);
    const clearPromises = Object.keys(selections)
      .filter((key) => key.startsWith('skill'))
      .map((key) => dispatch(clearSelectionAsync(key)));
    await Promise.all(clearPromises);
  };

  const handleSubmit = async (): Promise<void> => {
    const isValid = await form.trigger();
    if (!isValid) {
      return showToast({
        message: 'Validation Error',
        description: 'Please fix the errors in the form',
        type: 'error',
      });
    }
    const formData = form.getValues();
    if (formData.designations.length <= 0) {
      return showToast({
        message: 'Validation',
        description: 'At least one designation is needed',
        type: 'error',
      });
    }
    setIsSubmitting(true);
    try {
      const payload: any = generateExperiencePayload(
        localEntity!,
        initialEntity!,
        formData.designations,
        initialDesignations,
        experienceId,
      );

      await dispatch(addExperienceAsync({ payload })).unwrap();
      showToast({
        message: 'Success',
        description: experienceId
          ? 'Experience updated successfully'
          : 'Experience added successfully',
        type: 'success',
      });
      await clearFields();
      navigation.goBack();
    } catch (error: any) {
      showToast({
        message: 'Error',
        description: 'Failed to update experience',
        type: 'error',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAddEditShip = (field: FieldTypeI, shipId?: string): void => {
    const formData = form.getValues();
    const data: any = generateExperiencePayload(
      localEntity!,
      initialEntity!,
      formData.designations,
      initialDesignations,
      experienceId,
      field,
    );
    const entityId: string | undefined = localEntity?.id;
    navigate('EditShipItem', {
      data,
      field,
      shipId,
      entityId,
      refetch: async () => {
        await clearSkillsSelections();
        refetch();
      },
    });
  };

  const handleDeleteShip = async (field: FieldTypeI, shipId: string): Promise<void> => {
    try {
      setIsDeleting(true);
      const payload: any = [
        {
          id: experienceId,
          opr: 'NESTED_OPR' as 'UPDATE' | 'DELETE' | 'CREATE' | 'NESTED_OPR',
          designations: [
            {
              id: field.id,
              opr: 'NESTED_OPR',
              ships: [
                {
                  id: shipId,
                  opr: 'DELETE' as 'UPDATE' | 'DELETE' | 'CREATE' | 'NESTED_OPR',
                },
              ],
            },
          ],
        },
      ];
      await dispatch(addShipExperience({ payload })).unwrap();
      const currentDesignations = form.getValues('designations');
      const updatedDesignations = currentDesignations.map((designation: any) => {
        if (designation.id === field.id) {
          const updatedShips: any[] =
            designation.ships?.filter((ship: any) => ship.id !== shipId) || [];
          return { ...designation, ships: updatedShips };
        }
        return designation;
      });
      form.setValue('designations', updatedDesignations, { shouldDirty: true });
      form.trigger('designations');
      await clearSkillsSelections();
    } catch (error: any) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Failed to delete ship',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const setLocalDesignations = async (newDesignations: DesignationWithDateI[]): Promise<void> => {
    form.setValue('designations', newDesignations as any, { shouldDirty: true });
    form.trigger('designations');
  };

  useEffect(() => {
    if (experienceId) {
      fetchExperience(experienceId);
    }
  }, [experienceId]);

  useEffect(() => {
    setLocalEntity(entity);
  }, [entity]);

  useEffect(() => {
    designationsFromRedux.forEach((designation: DesignationsI) => {
      const index = Number(designation.indexKey);
      const currentDesignations = form.getValues('designations');
      if (currentDesignations[index] && !currentDesignations[index].designation) {
        handleDesignationChange(index, {
          id: designation.id,
          name: designation.name,
          dataType: designation.dataType,
        });
      }
    });
  }, [designationsFromRedux, form]);

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', async () => {});
    return unsubscribe;
  }, [navigation]);

  return {
    designations: designationsFromRedux,
    isSubmitting,
    loading,
    isAddingDesignation,
    handleSubmit,
    handleAddDesignation,
    handleAddEditShip,
    handleDeleteShip,
    isDeleting,
    localDesignations: mappedDesignations,
    getDateChangeHandler,
    localEntity,
    setLocalDesignations,
    getFieldError,
    handlePresentCheckbox,
    hasChanges,
    clearAndReindexSelections,
    designationSelections,
    clearFields,
  };
};

export default useEditExperienceItem;
