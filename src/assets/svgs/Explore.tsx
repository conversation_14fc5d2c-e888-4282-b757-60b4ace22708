/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path, Rect } from 'react-native-svg';
import { OutlinedIconPropsI } from './types';

const Explore: React.FC<Omit<OutlinedIconPropsI, 'onPress'>> = ({
  width = 3.96,
  height = 3.96,
  color = '#448600',
  fill,
  stroke = '#D4D4D4',
  strokeWidth = 1,
  accessibilityLabel = 'Explore',
  disabled,
  ...props
}) => {
  const fillColor = fill || color;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 32 32"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Rect
        x="0.5"
        y="0.5"
        width={RFPercentage(3.8)}
        height={RFPercentage(3.8)}
        rx="7.5"
        stroke={stroke}
        strokeWidth={strokeWidth}
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10 0c5.523 0 10 4.477 10 10s-4.477 10-10 10S0 15.523 0 10 4.477 0 10 0zm5.984 5.39c.215-.745-.487-1.446-1.233-1.232l-.102.035L7.58 7.02l-.106.05a1 1 0 00-.39.382l-.062.125-2.828 7.071-.035.102c-.214.746.487 1.448 1.233 1.233l.102-.035 7.07-2.828.106-.05a1 1 0 00.391-.381l.06-.126 2.83-7.071.034-.102zM10 8a2 2 0 110 4 2 2 0 010-4z"
        fill={fillColor}
        transform="translate(6,6)"
      />
    </Svg>
  );
};

export default Explore;
