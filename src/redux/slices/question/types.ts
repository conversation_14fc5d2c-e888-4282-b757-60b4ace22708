/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import type {
  ForumQuestionFetchManyPayloadI as NetworkForumQuestionFetchManyPayloadI,
  ForumQuestionI as NetworkForumQuestionI,
} from '@/src/networks/question/types';
import { SearchResultI } from '../entitysearch/types';

export type QuestionTypeE = 'NORMAL' | 'TROUBLESHOOT';

// Re-export network types for convenience
export type ForumQuestionFetchManyPayloadI = NetworkForumQuestionFetchManyPayloadI;
export type ForumQuestionI = NetworkForumQuestionI;

export type QuestionMediaCreateItemI = {
  fileUrl: string;
  fileExtension: string;
};

export type IdTypeI = {
  id: string;
  dataType: 'master' | 'raw';
};

export type ImoTypeI = {
  imo: string;
  dataType: 'master' | 'raw';
};

export type QuestionFormDataI = {
  title: string;
  description: string;
  type: QuestionTypeE;
  communityId: string;
  isAnonymous: boolean;
  isLive: boolean;
  topics: SearchResultI[];
  equipmentCategory: SearchResultI | null;
  equipmentModel: SearchResultI | null;
  equipmentManufacturer: SearchResultI | null;
  department: SearchResultI | null;
  files: QuestionMediaCreateItemI[];
  imo: SearchResultI | null;
  isGeneral: boolean;
};

export type QuestionCreatePayloadI = {
  title: string;
  description: string;
  type: QuestionTypeE;
  communityId: string;
  isLive: boolean;
  equipmentCategory?: IdTypeI;
  equipmentModel?: IdTypeI;
  equipmentManufacturer?: IdTypeI;
  topics?: IdTypeI[];
  department?: IdTypeI | null;
  isAnonymous?: boolean;
  files?: QuestionMediaCreateItemI[];
  ship?: ImoTypeI;
};

export type QuestionCreateResponseI = {
  id: string;
  title: string;
  description: string;
  type: QuestionTypeE;
};

export type ForumQuestionsFiltersI = {
  type: 'ALL' | 'NORMAL' | 'TROUBLESHOOT';
  isLive: boolean;
  myRecommended: boolean;
  myQuestion: boolean;
  myAnswered: boolean;
  myCommunity: boolean;
  department: SearchResultI | null;
  // For QnA questions
  topics: SearchResultI[];
  // For Troubleshooting questions
  equipmentCategory: SearchResultI | null;
  equipmentManufacturer: SearchResultI | null;
  equipmentModel: SearchResultI | null;
};

export type QuestionStateI = {
  formData: QuestionFormDataI;
  loading: boolean;
  error: string | null;
  // Forum questions list
  forumQuestions: ForumQuestionI[];
  forumQuestionsLoading: boolean;
  forumQuestionsRefreshing: boolean;
  forumQuestionsPagination: {
    nextCursorDate: string | null;
    hasMore: boolean;
    total: number;
  };
  forumQuestionsFilters: ForumQuestionsFiltersI;
};

export type UpdateQuestionLiveStatusPayloadI = {
  questionId: string;
  isLive: boolean;
};

export type UpdateQuestionLiveStatusResponseI = {
  isLive: boolean;
  liveStartedAt: string | null;
};
