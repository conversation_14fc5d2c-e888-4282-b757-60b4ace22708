/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
export type AuthTypeI = 'APPLE' | 'GOOGLE' | 'EMAIL_PASSWORD';
export type AuthLoginBodyI = {
  email?: string;
  password?: string;
  type: AuthTypeI;
  externalToken?: string;
  deviceToken?: string;
};

export type AuthLoginResultI = {
  name: string;
  username: string;
  email: string;
  profileId: string;
  avatar: string | null;
  isUsernameSaved: boolean;
  isEmailVerified: boolean;
  isPersonalDetailsSaved: boolean;
  isWorkDetailsSaved: boolean;
  isPrivacyPolicyAccepted: boolean;
  token: string;
  jwtToken: string;
  previousStatus: 'ACTIVE' | 'INACTIVE' | 'SCHEDULED_FOR_DELETION' | 'BLOCKED' | 'DELETED';
};

export interface AuthRegisterBodyI {
  type: 'GOOGLE' | 'EMAIL_PASSWORD';
  email?: string;
  password?: string;
  confirmPassword?: string;
  isPPAndTNCAccepted: boolean;
}

export interface AuthRegisterResultI {
  profileId: string;
  token: string;
}

export interface SendOTPForEmailVerificationBodyI {
  profileId: string;
}

export interface VerifyOTPForEmailVerificationBodyI {
  otp: string;
  profileId: string;
}

export interface SendOTPForPasswordResetBodyI {
  email: string;
}

export interface VerifyOTPForPasswordResetBodyI {
  email: string;
  otp: string;
}

export interface ResetPasswordBodyI {
  email: string;
  newPassword: string;
}
