import { Image, Pressable, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { formatSocialTime } from '@/src/utilities/datetime';
import type { BottomTabNavigationI } from '@/src/navigation/types';
import AddConnection from '@/src/assets/svgs/AddConnection';
import Comment from '@/src/assets/svgs/Comment';
import Like from '@/src/assets/svgs/Like';
import type { NotificationType } from '@/src/networks/notifications/types';
import UserAvatar from '../UserAvatar';
import type { NotificationItemProps } from './types';

const NotificationItem = ({ notification, profile, post, onMarkAsRead }: NotificationItemProps) => {
  const navigation = useNavigation<BottomTabNavigationI>();
  const currentUser = useSelector(selectCurrentUser);

  const getNotificationIcon = (type: NotificationType) => {
    switch (type) {
      case 'LIKE':
        return <Like isLiked width={1.8} height={1.8} color="#EF4444" />;
      case 'COMMENT':
      case 'REPLY':
        return <Comment width={1.8} height={1.8} color="#4CAF50" />;
      case 'FOLLOWER':
      case 'REQUEST_RECEIVED':
      case 'REQUEST_ACCEPTED':
        return <AddConnection width={1.8} height={1.8} color="#4CAF50" />;
      default:
        return <Text className="text-xs">🔔</Text>;
    }
  };

  const notificationConfig: Record<NotificationType, { message: string; bgColor: string }> = {
    LIKE: { message: 'liked your post', bgColor: '#FEF2F2' },
    COMMENT: { message: 'commented on your post', bgColor: '#F0FDF4' },
    REPLY: { message: 'replied to your comment', bgColor: '#F0FDF4' },
    FOLLOWER: { message: 'started following you', bgColor: '#F0FDF4' },
    REQUEST_RECEIVED: { message: 'sent you a connection request', bgColor: '#FFFBEB' },
    REQUEST_ACCEPTED: { message: 'accepted your connection request', bgColor: '#F0FDF4' },
  };

  const config = notificationConfig[notification.type as NotificationType] || {
    message: '',
    bgColor: '#F9FAFB',
  };

  const getNotificationMessage = () => {
    if (post?.caption && post.caption.length > 50) {
      return `${config.message}: "${post.caption.substring(0, 50)}..."`;
    }
    return post?.caption ? `${config.message}: "${post.caption}..."` : config.message;
  };

  const handlePress = async () => {
    if (!notification.isRead && onMarkAsRead) {
      try {
        await onMarkAsRead(notification.id);
      } catch (error) {
        console.error('Failed to mark notification as read:', error);
      }
    }

    const { data } = notification;

    try {
      if (data.commentId || data.parentCommentId) {
        navigation.navigate('HomeStack', {
          screen: 'Comment',
          params: {
            postId: data.postId!,
            type: 'USER_POST',
          },
        });
      } else if (data.postId) {
        navigation.navigate('HomeStack', {
          screen: 'Comment',
          params: {
            postId: data.postId!,
            type: 'USER_POST',
          },
        });
      } else if (data.actorProfileId) {
        if (currentUser.profileId === data.actorProfileId) {
          navigation.navigate('ProfileStack', {
            screen: 'UserProfile',
            params: {
              profileId: data.actorProfileId,
              fromTabPress: true,
            },
          });
        } else {
          navigation.navigate('HomeStack', {
            screen: 'OtherUserProfile',
            params: {
              profileId: data.actorProfileId,
              fromTabPress: false,
            },
          });
        }
      }
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };

  return (
    <Pressable
      className={`mx-3 mb-2 rounded-xl overflow-hidden ${
        notification.isRead ? 'bg-white' : 'bg-green-50 border border-green-100'
      }`}
      onPress={handlePress}
    >
      <View className="p-4">
        <View className="flex-row items-start">
          <View className="relative mr-3">
            <UserAvatar
              avatarUri={profile?.avatar ?? null}
              name={profile?.name}
              width={48}
              height={48}
            />

            <View
              className="absolute -bottom-1 -right-1 w-6 h-6 rounded-full items-center justify-center border-2 border-white"
              style={{ backgroundColor: config.bgColor }}
            >
              {getNotificationIcon(notification.type as NotificationType)}
            </View>

            {!notification.isRead && (
              <View className="absolute -top-1 -left-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white" />
            )}
          </View>

          <View className="flex-1 min-w-0">
            <View className="flex-row items-start justify-between mb-1">
              <Text
                className={`font-semibold text-base ${notification.isRead ? 'text-gray-900' : 'text-gray-900'}`}
                numberOfLines={1}
              >
                {profile?.name || 'Unknown User'}
              </Text>
              <Text className="text-gray-400 text-xs ml-2 mt-0.5">
                {formatSocialTime(notification.createdAt)}
              </Text>
            </View>

            <Text
              className={`text-sm leading-5 ${notification.isRead ? 'text-gray-600' : 'text-gray-700'}`}
              numberOfLines={3}
            >
              {getNotificationMessage()}
            </Text>

            {post?.image && (
              <View className="mt-3 rounded-lg overflow-hidden bg-gray-100">
                <Image source={{ uri: post.image }} className="w-full h-32" resizeMode="cover" />
              </View>
            )}
          </View>
        </View>
      </View>
    </Pressable>
  );
};

export default NotificationItem;
