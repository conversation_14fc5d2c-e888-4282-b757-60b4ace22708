/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { GlobalSearchParams, GlobalSearchResponse } from '@/src/utilities/search/types';
import { apiCall } from '@/src/services/api';

export const searchPeopleGlobalAPI = async (
  query: GlobalSearchParams,
): Promise<GlobalSearchResponse> => {
  const result = await apiCall<GlobalSearchParams, GlobalSearchResponse>(
    '/backend/api/v1/network/connection/global-search',
    'GET',
    {
      isAuth: true,
      query,
    },
  );

  return result;
};
