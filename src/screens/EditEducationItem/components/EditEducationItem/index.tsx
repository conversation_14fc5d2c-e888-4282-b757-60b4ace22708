import { useEffect, useRef, useState } from 'react';
import { ActivityIndicator, Pressable, ScrollView, Text, View } from 'react-native';
import { useNavigation, type NavigationAction } from '@react-navigation/native';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import Checkbox from '@/src/components/Checkbox';
import ChipInput from '@/src/components/ChipInput';
import DatePicker from '@/src/components/DatePicker';
import EntitySearch from '@/src/components/EntitySearch';
import CustomModal from '@/src/components/Modal';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { validateDate } from '../utils';
import type { EditEducationItemPropsI } from './types';
import { useEditEducationItem } from './useHook';

const EditEducationItem = ({ onBack, profileId, educationId }: EditEducationItemPropsI) => {
  const {
    methods,
    isSubmitting,
    onSubmit,
    localSkills,
    setLocalSkills,
    loading,
    isPresent,
    handlePresentCheckbox,
    clearFields,
    isSubmitted,
    setIsSubmitted,
    hasChanges,
    isSkillLimitReached,
    MAX_SKILLS,
  } = useEditEducationItem(profileId, educationId);

  const { control, handleSubmit, watch } = methods;
  const instituteSelection = useSelector(selectSelectionByKey('entity'));
  const degreeSelection = useSelector(selectSelectionByKey('degree'));
  const navigation = useNavigation();
  const [showDiscardModal, setShowDiscardModal] = useState(false);
  const exitActionRef = useRef<NavigationAction | null>(null);

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', (e) => {
      if (hasChanges && !isSubmitting) {
        e.preventDefault();
        exitActionRef.current = e.data.action;
        setShowDiscardModal(true);
      }
    });
    return unsubscribe;
  }, [navigation, hasChanges, isSubmitting]);

  const handleDiscardConfirm = () => {
    setShowDiscardModal(false);
    setTimeout(() => {
      if (exitActionRef.current) {
        navigation.dispatch(exitActionRef.current);
      }
    }, 300);
  };

  const handleDiscardCancel = () => {
    setShowDiscardModal(false);
  };

  useEffect(() => {
    if (instituteSelection) {
      methods.setValue('institution', instituteSelection, { shouldDirty: true });
    }
    if (degreeSelection) {
      methods.setValue('degree', degreeSelection, { shouldDirty: true });
    }
    return () => {
      clearFields();
    };
  }, [degreeSelection, instituteSelection, methods, clearFields]);

  const FromDate = watch('fromDate');
  const ToDate = watch('toDate');

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#448600" />
      </View>
    );
  }

  return (
    <ScrollView className="flex-1 bg-white" showsVerticalScrollIndicator={false}>
      <View className="px-4">
        <View className="flex-row items-center justify-between py-4">
          <BackButton onBack={onBack} label={educationId ? 'Edit Education' : 'Create Education'} />
          <Pressable
            onPress={() => {
              setIsSubmitted(true);
              handleSubmit(onSubmit)();
            }}
            disabled={isSubmitting || !hasChanges}
          >
            <Text
              className={`text-lg font-medium ${isSubmitting || !hasChanges ? 'text-gray-400' : 'text-[#448600]'}`}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </Text>
          </Pressable>
        </View>
        <Controller
          control={control}
          name="institution"
          rules={{ required: 'Institution is required' }}
          render={({ fieldState: { error } }) => (
            <EntitySearch
              title="Institution"
              placeholder="Enter Institution ..."
              selectionKey="entity/institution"
              data={
                instituteSelection
                  ? instituteSelection.name
                  : methods.watch('institution')?.name || ''
              }
              error={
                isSubmitted && !instituteSelection && !methods.watch('institution')?.id
                  ? `Institution is required`
                  : error?.message
              }
            />
          )}
        />
        <Controller
          control={control}
          name="degree"
          rules={{ required: 'Degree is required' }}
          render={({ fieldState: { error } }) => (
            <EntitySearch
              title="Degree"
              placeholder="Enter Degree ..."
              selectionKey="degree"
              data={degreeSelection ? degreeSelection.name : methods.watch('degree')?.name || ''}
              error={
                isSubmitted && !degreeSelection && !methods.watch('degree')?.id
                  ? `Degree is required`
                  : error?.message
              }
            />
          )}
        />
        <View className="flex-row mb-6">
          <View className="flex-1 mr-2">
            <Controller
              control={control}
              name="fromDate"
              rules={{
                required: 'From date is required',
                validate: (value) => validateDate(value, ToDate),
              }}
              render={({ field: { onChange }, fieldState: { error } }) => (
                <>
                  <DatePicker
                    title="From"
                    selectedDate={FromDate}
                    onDateChange={(date) => {
                      if (date instanceof Date) {
                        onChange(date.toISOString().split('T')[0]);
                      }
                    }}
                    showMonthYear={true}
                    className={error ? 'border-red-500' : ''}
                  />
                  {error && <Text className="text-red-500 text-xs mt-1">{error.message}</Text>}
                </>
              )}
            />
          </View>
          <View className="flex-1 ml-2">
            <Controller
              control={control}
              name="toDate"
              rules={{
                required: !isPresent ? "To date is required when 'Present' is not checked" : false,
                validate: (value) => {
                  if (isPresent) return true;
                  if (!value) return 'To date is required';
                  return validateDate(FromDate, value);
                },
              }}
              render={({ field: { onChange }, fieldState: { error } }) => (
                <>
                  <DatePicker
                    title="To"
                    selectedDate={ToDate!}
                    onDateChange={(date) => {
                      if (date instanceof Date) {
                        onChange(date.toISOString().split('T')[0]);
                      }
                    }}
                    showMonthYear={true}
                    disabled={isPresent}
                    className={error ? 'border-red-500' : ''}
                  />
                  {error && !isPresent && (
                    <Text className="text-red-500 text-xs mt-1">{error.message}</Text>
                  )}
                </>
              )}
            />
            <Controller
              control={control}
              name="isPresent"
              render={({ field: { value } }) => (
                <Checkbox
                  label="Present"
                  className="pt-3"
                  labelClassName="text-base text-sm"
                  onValueChange={handlePresentCheckbox}
                  checked={value}
                />
              )}
            />
          </View>
        </View>
        <View className="mb-6">
          <ChipInput
            title="Skills"
            placeholder="Add a skill"
            chips={localSkills}
            onRemove={(id) => setLocalSkills((prev) => prev.filter((s) => s.id !== id))}
            disabled={isSkillLimitReached}
            error={isSkillLimitReached ? `Skill limit reached (max ${MAX_SKILLS})` : undefined}
          />
        </View>
      </View>
      <CustomModal
        isVisible={showDiscardModal}
        title="Discard changes?"
        cancelText="Cancel"
        confirmText="Discard"
        description="You have unsaved changes. Are you sure you want to discard them and leave this screen?"
        onCancel={handleDiscardCancel}
        onConfirm={handleDiscardConfirm}
        confirmButtonVariant="danger"
      />
    </ScrollView>
  );
};

export default EditEducationItem;
