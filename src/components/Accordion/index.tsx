import { useState } from 'react';
import { Pressable, Text, View } from 'react-native';
import ChevronDown from '@/src/assets/svgs/ChevronDown';
import ChevronUp from '@/src/assets/svgs/ChevronUp';
import { AccordionPropsI } from './types';

const Accordion = ({
  title,
  subTitle,
  content,
  isLast,
  toggleExpand,
  defaultExpanded = false,
  hideBorder = false,
  contentClassName,
}: AccordionPropsI) => {
  const [expanded, setExpanded] = useState(defaultExpanded);

  const onToggle = () => {
    toggleExpand?.();
    setExpanded((prev) => !prev);
  };

  return (
    <View className="mt-6">
      <Pressable
        className="flex-row justify-between items-center py-2"
        onPress={toggleExpand ? onToggle : () => setExpanded((prev) => !prev)}
      >
        <View className="flex-1 max-w-[80%] gap-1">
          <Text
            className="font-semibold text-base leading-5 text-gray-900"
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {title}
          </Text>
          {subTitle && (
            <Text
              className="text-sm leading-4 text-gray-600"
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {subTitle}
            </Text>
          )}
        </View>
        <View className="ml-3 p-1">
          {expanded ? (
            <ChevronUp className="text-gray-700" />
          ) : (
            <ChevronDown className="text-gray-700" />
          )}
        </View>
      </Pressable>

      {expanded && (
        <View className={contentClassName ? contentClassName : 'mt-4 pb-2'}>{content}</View>
      )}

      {!isLast && !hideBorder && <View className="h-px bg-borderGrayLight w-full mt-6" />}
    </View>
  );
};

export default Accordion;
