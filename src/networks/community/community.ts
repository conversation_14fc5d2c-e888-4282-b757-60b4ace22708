import { CommunityResponse, CreateCommunityPayload } from '@/src/redux/slices/community/types';
import { apiCall } from '@/src/services/api';
import { CommunityFetchForClientI, FetchCommunityClientI } from './types';

export const createCommunity = async (payload: CreateCommunityPayload) => {
  return await apiCall<CreateCommunityPayload, CommunityResponse>(
    '/backend/api/v1/forum/community',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );
};

export const fetchOwnCommunities = async (
  query?: CommunityFetchForClientI,
): Promise<FetchCommunityClientI[]> => {
  const pageSize = 10;
  const result = await apiCall<CommunityFetchForClientI, FetchCommunityClientI[]>(
    '/backend/api/v1/forum/community',
    'GET',
    {
      query: { pageSize, ...query },
      isAuth: true,
    },
  );

  return result;
};
