/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { ClipPath, Defs, G, Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const Attachment: React.FC<FilledIconPropsI> = ({
  width = 3.596,
  height = 3.472,
  fill = '#000',
  color,
  disabled,
  accessibilityLabel = 'Attachment',
  ...props
}) => {
  const fillColor = color || fill;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 29 28"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <G clipPath="url(#clip0_532_1087)">
        <Path
          d="M19.554 13.665a.47.47 0 01.1.513.47.47 0 01-.1.153l-6.383 6.43A4.184 4.184 0 0110.2 22a4.184 4.184 0 01-2.97-1.24A4.252 4.252 0 016 17.764c0-1.123.443-2.2 1.231-2.994l7.722-7.897A2.954 2.954 0 0117.043 6a2.936 2.936 0 012.09.872A2.983 2.983 0 0120 8.978a3 3 0 01-.865 2.108l-7.722 7.894a1.71 1.71 0 01-1.21.506 1.7 1.7 0 01-1.211-.504 1.726 1.726 0 01-.502-1.22 1.737 1.737 0 01.5-1.22l6.478-6.639a.467.467 0 01.765.507.472.472 0 01-.1.154l-6.477 6.641a.785.785 0 00-.171.855.784.784 0 00.717.486.77.77 0 00.551-.229l7.72-7.895a2.047 2.047 0 00.002-2.883 2.014 2.014 0 00-2.86-.002l-7.723 7.894a3.307 3.307 0 00-.958 2.328c0 .874.343 1.712.955 2.33a3.254 3.254 0 002.31.966c.867 0 1.698-.346 2.311-.963l6.383-6.43a.467.467 0 01.51-.1.465.465 0 01.15.103z"
          fill={fillColor}
        />
      </G>
      <Defs>
        <ClipPath id="clip0_532_1087">
          <Path fill="#fff" transform="translate(.5)" d="M0 0H28V28H0z" />
        </ClipPath>
      </Defs>
    </Svg>
  );
};

export default Attachment;
