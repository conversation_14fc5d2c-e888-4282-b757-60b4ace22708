import React from 'react';
import { useNavigation } from '@react-navigation/native';
import SafeArea from '@/src/components/SafeArea';
import { BottomTabNavigationI } from '@/src/navigation/types';
import UserSettingsMenu from './components/UserSettingsMenu';

const UserSettings = () => {
  const navigation = useNavigation<BottomTabNavigationI>();
  return (
    <SafeArea>
      <UserSettingsMenu onBack={() => navigation.goBack()} />
    </SafeArea>
  );
};

export default UserSettings;
