import { useEffect, useState } from 'react';
import { showToast } from '@/src/utilities/toast';
import { fetchPrivacyPolicyAPI } from '@/src/networks/profile/privacyPolicy';

export const usePrivacyModal = (isVisible: boolean) => {
  const [policyContent, setPolicyContent] = useState('');
  const [loading, setLoading] = useState(false);

  const fetchPrivacyPolicy = async () => {
    try {
      setLoading(true);
      const result = await fetchPrivacyPolicyAPI({
        type: 'SIGNUP_PRIVACY_POLICY',
      });

      if (result) {
        setPolicyContent(result.content);
      }
    } catch (error) {
      showToast({
        message: 'Error loading privacy policy',
        type: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPrivacyPolicy();
  }, [isVisible]);

  return {
    policyContent,
    loading,
    isVisible,
  };
};
