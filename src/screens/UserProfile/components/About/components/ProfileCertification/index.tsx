import React, { SetStateAction, useState } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { SectionHeader } from '@/src/components/SectionHeader';
import Tabs from '@/src/components/Tabs';
import { navigate } from '@/src/utilities/navigation';
import Certification from '@/src/assets/svgs/Certification';
import EditPencil from '@/src/assets/svgs/EditPencil';
import ProfileStatutory from './components/Statutory';
import ProfileValueAdded from './components/ValueAdded';
import { CertificationTabsI, ProfileCertificationPropsI } from './types';

const ProfileCertification = ({ isUserProfile, profileId }: ProfileCertificationPropsI) => {
  const tabs = [
    {
      id: 'statutory',
      label: 'Statutory',
    },
    {
      id: 'value-added',
      label: 'Value Added',
    },
  ];

  const tabScreens: CertificationTabsI = {
    statutory: <ProfileStatutory profileId={profileId} isUserProfile={isUserProfile} />,
    'value-added': <ProfileValueAdded profileId={profileId} isUserProfile={isUserProfile} />,
  };

  const [activeTab, setActiveTab] = useState<keyof CertificationTabsI>('statutory');

  return (
    <View className="pt-4">
      <View className="flex-row items-center justify-between">
        <SectionHeader title="Certification" icon={Certification} />
        {isUserProfile && (
          <TouchableOpacity
            onPress={() =>
              navigate('EditCertificationList', {
                editable: true,
                tab: activeTab,
              })
            }
          >
            <EditPencil width={2.3} height={2.3} />
          </TouchableOpacity>
        )}
      </View>
      <View className="py-4">
        <Tabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab as React.Dispatch<SetStateAction<string>>}
        />
        {tabScreens[activeTab]}
      </View>
    </View>
  );
};

export default ProfileCertification;
