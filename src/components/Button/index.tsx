import { ActivityIndicator, Pressable, Text, View } from 'react-native';
import { twMerge } from 'tailwind-merge';
import { VARIANTS } from './consts';
import { ButtonProps } from './types';

const Button = ({
  onPress,
  variant = 'primary',
  label,
  disabled,
  loading,
  className,
  labelClassName,
  prefixIcon,
  suffixIcon,
}: ButtonProps) => {
  const isOAuthVariant = variant === 'oauth';
  const appliedVariant = disabled && !isOAuthVariant ? 'tertiary' : variant;
  const variantStyles = VARIANTS[appliedVariant];

  const containerStyle =
    'containerStyle' in variantStyles ? (variantStyles as any).containerStyle : {};
  const labelStyle = typeof variantStyles?.label === 'object' ? variantStyles.label : {};

  const getContainerClass = () => {
    if (disabled && isOAuthVariant) {
      return variantStyles.disabledContainer || variantStyles.container;
    }
    return variantStyles.container;
  };

  return (
    <Pressable
      onPress={onPress}
      disabled={disabled || loading}
      className={twMerge(
        'w-full justify-center items-center flex-row',
        getContainerClass(),
        className,
      )}
      style={containerStyle || {}}
    >
      {loading ? (
        <ActivityIndicator size="small" color={VARIANTS[variant].spinnerColor} />
      ) : (
        <View className="flex-row items-center gap-2">
          {prefixIcon}
          <Text
            className={twMerge(
              'text-base font-medium font-inter-medium',
              typeof variantStyles?.label === 'string' ? variantStyles.label : '',
              labelClassName,
            )}
            style={labelStyle}
          >
            {label}
          </Text>
          {suffixIcon}
        </View>
      )}
    </Pressable>
  );
};

export default Button;
