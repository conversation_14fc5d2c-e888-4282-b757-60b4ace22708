/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from '@/src/assets/svgs/types';

const PhotoPreview: React.FC<OutlinedIconPropsI> = ({
  width = 3.0,
  height = 3.5,
  stroke = '#448600',
  color,
  strokeWidth = 1,
  accessibilityLabel = 'Photo Preview',
  disabled,
  ...props
}) => {
  const strokeColor = color || stroke;
  const opacity = disabled ? 0.5 : 1;
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 20 18"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        d="M18.25.75H1.75a1.5 1.5 0 00-1.5 1.5v13.5a1.5 1.5 0 001.5 1.5h16.5a1.5 1.5 0 001.5-1.5V2.25a1.5 1.5 0 00-1.5-1.5zm0 1.5v9.633L15.806 9.44a1.5 1.5 0 00-2.122 0l-1.875 1.875L7.684 7.19a1.5 1.5 0 00-2.12 0L1.75 11.003V2.25h16.5zM1.75 13.125L6.625 8.25l7.5 7.5H1.75v-2.625zm16.5 2.625h-2.003l-3.375-3.375 1.875-1.875 3.503 3.504v1.746zM11.5 6.375a1.125 1.125 0 112.25 0 1.125 1.125 0 01-2.25 0z"
        fill={strokeColor}
      />
    </Svg>
  );
};

export default PhotoPreview;
