/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from '@/src/assets/svgs/types';

const SideTabIcon: React.FC<OutlinedIconPropsI> = ({
  width = 3.2,
  height = 2.4,
  stroke = '#000000',
  color,
  strokeWidth = 1,
  accessibilityLabel = 'Side Tab Icon',
  disabled,
  ...props
}) => {
  const strokeColor = color || stroke;
  const opacity = disabled ? 0.5 : 1;
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 20 15"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        d="M19.454 7.5a.802.802 0 01-.802.802H1.009a.802.802 0 110-1.604h17.643a.802.802 0 01.802.802zM1.009 1.886h17.643a.802.802 0 100-1.604H1.009a.802.802 0 000 1.604zm17.643 11.228H1.009a.802.802 0 000 1.603h17.643a.802.802 0 000-1.603z"
        fill={strokeColor}
      />
    </Svg>
  );
};

export default SideTabIcon;
