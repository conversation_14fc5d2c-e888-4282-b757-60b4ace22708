import { KeyboardAvoidingView, Platform, TouchableWithoutFeedback, Keyboard } from 'react-native';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import SafeArea from '@/src/components/SafeArea';
import { ProfileStackParamsListI } from '@/src/navigation/types';
import EditShipItem from './components/EditShipItem';

type RouteProps = RouteProp<ProfileStackParamsListI, 'EditShipItem'>;

const EditShipItemScreen = () => {
  const route = useRoute<RouteProps>();
  const { data, field, shipId, fromProfileExperience, refetch } = route.params || {};
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();

  return (
    <SafeArea>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <KeyboardAvoidingView
          className="flex-1"
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 10 : 20}
        >
          <EditShipItem
            onBack={navigation.goBack}
            profileId={'profileId'}
            shipId={shipId}
            preFilledData={data!}
            field={field!}
            fromProfileExperience={fromProfileExperience!}
            refetch={refetch!}
          />
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </SafeArea>
  );
};

export default EditShipItemScreen;
