/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ObjUnknownI } from '@/src/types/common/data';
import { ProfileExternalI } from '@/src/networks/connect/types';

export interface BlockedUserI extends ObjUnknownI {
  Profile: ProfileExternalI;
}

export interface UseBlockedProfilesReturn {
  blockedUsers: BlockedUserI[];
  loading: boolean;
  refreshing: boolean;
  isUnblocking: Record<string, boolean>;
  totalCount: number;
  handleRefresh: () => Promise<void>;
  handleLoadMore: () => Promise<void>;
  handleUnblock: (profileId: string) => Promise<void>;
}

export interface BlockedProfilesListProps {
  onBack: () => void;
}
