import type { Image } from 'react-native-image-crop-picker';

export type CommunityAccessE = 'PUBLIC' | 'PRIVATE';

export type MemberTypeE = 'ADMIN' | 'MODERATOR' | 'CONTRIBUTOR' | 'MEMBER';

export type CommunityMemberI = {
  profileId: string;
  type: MemberTypeE;
};

export type CommunityStateI = {
  name: string;
  description: string;
  access: CommunityAccessE;
  isRestricted: boolean;
  restrictions: string[];
  members: any[];
  moderators: any[];
  avatar: {
    url: string | null;
    image: Image | null;
  };
  loading: boolean;
  error: string | null;
};

export type CommunityUpdatePayload = Partial<CommunityStateI>;

export type FileOperationI = {
  opr: 'CREATE' | 'UPDATE' | 'DELETE';
  fileUrl?: string;
};

export type CreateCommunityPayload = {
  name: string;
  description?: string;
  access: 'PUBLIC' | 'PRIVATE';
  isRestricted: boolean;
  members?: CommunityMemberI[];
  avatar?: FileOperationI;
};

export type CommunityResponse = {
  id: string;
  name: string;
  access: string;
  isRestricted: boolean;
  creatorId: string;
  cursorId: string;
};
