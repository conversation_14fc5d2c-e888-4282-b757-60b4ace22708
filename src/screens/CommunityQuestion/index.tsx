import { View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { ScrollView } from 'react-native-gesture-handler';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useDispatch, useSelector } from 'react-redux';
import SafeArea from '@/src/components/SafeArea';
import {
  selectQuestionIsGeneralDepartment,
  selectQuestionType,
} from '@/src/redux/selectors/question';
import { selectSelectionByKey, selectMultipleSelectionsByKey } from '@/src/redux/selectors/search';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import {
  setTopics,
  setEquipmentCategory,
  setEquipmentModel,
  setEquipmentManufacturer,
  setDepartment,
  setImo,
} from '@/src/redux/slices/question/questionSlice';
import { AppDispatch } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import CreateCommunityHeader from '../CreateCommunity/components/CreateCommunityHeader';
import AskQuestionForm, { validateAskQuestionForm } from './components/AskQuestionForm';

const CommunityQuestionScreen = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const dispatch = useDispatch<AppDispatch>();

  const topicsSelection = useSelector(selectMultipleSelectionsByKey('topic'));
  const departmentSelection = useSelector(selectSelectionByKey('department'));
  const equipmentCategorySelection = useSelector(selectSelectionByKey('equipmentCategory'));
  const equipmentManufacturerSelection = useSelector(selectSelectionByKey('equipmentManufacturer'));
  const equipmentModelSelection = useSelector(selectSelectionByKey('equipmentModel'));
  const imoSelection = useSelector(selectSelectionByKey('ship')) as unknown as {
    imo: string;
    dataType: string;
    name: string;
  };
  const questionType = useSelector(selectQuestionType);
  const questionIsGeneral = useSelector(selectQuestionIsGeneralDepartment);

  const handleNext = () => {
    const state = {
      question: { formData: { type: questionType, isGeneral: questionIsGeneral } },
      search: {
        multipleSelections: { topic: topicsSelection },
        selections: {
          department: departmentSelection,
          equipmentCategory: equipmentCategorySelection,
          equipmentManufacturer: equipmentManufacturerSelection,
          equipmentModel: equipmentModelSelection,
          imo: imoSelection,
        },
      },
    };

    const validation = validateAskQuestionForm(state);

    if (!validation.isValid) {
      showToast({
        type: 'error',
        message: 'Validation Error',
        description: validation.message,
      });
      return;
    }

    if (topicsSelection) {
      dispatch(setTopics(topicsSelection));
    }

    if (departmentSelection) {
      dispatch(setDepartment(departmentSelection));
    }

    if (questionType === 'TROUBLESHOOT') {
      if (equipmentCategorySelection) {
        dispatch(setEquipmentCategory(equipmentCategorySelection));
      }
      if (equipmentManufacturerSelection) {
        dispatch(setEquipmentManufacturer(equipmentManufacturerSelection));
      }
      if (equipmentModelSelection) {
        dispatch(setEquipmentModel(equipmentModelSelection));
      }
      if (imoSelection) {
        dispatch(setImo(imoSelection as unknown as SearchResultI));
      }
    }
    navigation.navigate('CreateQuestion');
  };

  const insets = useSafeAreaInsets();

  return (
    <SafeArea>
      <ScrollView
        contentContainerStyle={{
          paddingBottom: 85 + insets.bottom,
        }}
      >
        <View className="flex-1 p-4 bg-white">
          <CreateCommunityHeader currentPage={1} onNext={handleNext} />
          <AskQuestionForm />
        </View>
      </ScrollView>
    </SafeArea>
  );
};

export default CommunityQuestionScreen;
