/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import * as React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';

const MessageIcon = ({ width = 2.976, height = 3.1 }) => {
  return (
    <Svg width={RFPercentage(width)} height={RFPercentage(height)} viewBox="0 0 24 25" fill="none">
      <Path
        d="M20.338 17.064a5.91 5.91 0 00.66-2.847 5.924 5.924 0 00-.79-2.817 6.224 6.224 0 00-2.06-2.145 6.557 6.557 0 00-2.863-.987A6.107 6.107 0 0013.87 6.24a6.412 6.412 0 00-2.13-1.336 6.625 6.625 0 00-4.985.109A6.38 6.38 0 004.691 6.44a6.076 6.076 0 00-1.318 2.087 5.885 5.885 0 00.288 4.773l-.613 1.993c-.059.19-.063.392-.013.584.051.191.155.367.3.507.147.14.33.24.53.288.2.048.41.044.608-.012l2.083-.588c.677.32 1.408.523 2.16.598a6.11 6.11 0 001.454 2.067 6.422 6.422 0 002.194 1.338 6.63 6.63 0 005.086-.238l2.076.587c.198.057.408.061.608.012.2-.048.383-.148.53-.288.146-.14.25-.315.3-.507.05-.192.046-.393-.013-.583l-.613-1.994zM6.6 15.097a.516.516 0 00-.138.019l-2.272.64a.171.171 0 01-.161-.039.15.15 0 01-.041-.155l.668-2.178a.457.457 0 00-.039-.355 4.987 4.987 0 01-.507-3.574A5.164 5.164 0 016.162 6.43a5.567 5.567 0 013.614-1.005 5.504 5.504 0 013.42 1.502 5.084 5.084 0 011.567 3.28 5.025 5.025 0 01-1.048 3.464 5.424 5.424 0 01-3.154 1.967 5.605 5.605 0 01-3.728-.486.508.508 0 00-.233-.056zM19.34 17.15l.668 2.178a.15.15 0 01-.041.155.166.166 0 01-.162.04l-2.272-.641a.515.515 0 00-.37.036 5.619 5.619 0 01-4.17.371 5.469 5.469 0 01-1.87-.99 5.217 5.217 0 01-1.323-1.608 6.582 6.582 0 002.62-.724 6.307 6.307 0 002.066-1.708 5.998 5.998 0 001.132-2.379 5.865 5.865 0 00-.01-2.615c.835.15 1.62.486 2.293.98a5.227 5.227 0 011.583 1.869c.367.733.549 1.54.53 2.353a5.009 5.009 0 01-.637 2.328.457.457 0 00-.035.355h-.002z"
        fill="#000"
      />
    </Svg>
  );
};

export default MessageIcon;
