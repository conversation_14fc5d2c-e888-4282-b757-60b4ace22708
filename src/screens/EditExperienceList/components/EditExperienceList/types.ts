import type { StackNavigationProp } from '@react-navigation/stack';
import type { ExperienceFetchForClientResultI } from '@/src/redux/slices/experience/types';
import type { ProfileStackParamsListI } from '@/src/navigation/types';

export type EditExperienceListPropsI = {
  profileId: string;
  onBack: () => void;
  editable?: boolean;
};

export interface UseEditExperienceListI {
  experiences: ExperienceFetchForClientResultI[];
  isLoading: boolean;
  loadingMore: boolean;
  hasMore: boolean;
  handleLoadMore: () => void;
  isModalVisible: boolean;
  handleModal: () => void;
  onAddExperience: () => void;
  onEditExperience: (experienceId: string) => void;
  onDeleteExperience: (experienceId: string) => Promise<void>;
  navigation: StackNavigationProp<ProfileStackParamsListI>;
}

export type Duration = {
  years: number;
  months: number;
};
