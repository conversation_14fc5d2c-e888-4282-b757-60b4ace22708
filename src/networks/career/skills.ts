import { apiCall } from '@/src/services/api';
import type { DeleteSkillPayloadI, fetchSkillsResultI, SkillsPayloadI } from './types';

export const fetchMaritimeSkillsAPI = async (
  profileId: string,
  page = 1,
  pageSize = 20,
): Promise<{ skills: fetchSkillsResultI[]; hasMore: boolean }> => {
  const result = await apiCall<string, { skills: fetchSkillsResultI[]; hasMore: boolean }>(
    '/backend/api/v1/career/profile-skills',
    'GET',
    {
      isAuth: true,
      query: {
        profileId,
        category: 'MARITIME',
        page,
        pageSize,
      },
    },
  );
  return result;
};

export const fetchOtherSkillsAPI = async (
  profileId: string,
  page = 1,
  pageSize = 20,
): Promise<{ skills: fetchSkillsResultI[]; hasMore: boolean }> => {
  const result = await apiCall<string, { skills: fetchSkillsResultI[]; hasMore: boolean }>(
    '/backend/api/v1/career/profile-skills',
    'GET',
    {
      isAuth: true,
      query: {
        profileId,
        category: 'OTHER',
        page,
        pageSize,
      },
    },
  );
  return result;
};

export const addSkills = async (payload: SkillsPayloadI[]): Promise<number> => {
  const result = await apiCall<unknown, number>('/backend/api/v1/career/profile-skills', 'POST', {
    isAuth: true,
    payload,
  });
  return result;
};

export const deleteSkillsAPI = async (skills: DeleteSkillPayloadI[]) => {
  await apiCall<unknown>(`/backend/api/v1/career/profile-skills`, 'DELETE', {
    isAuth: true,
    payload: {
      skills,
    },
  });
};
