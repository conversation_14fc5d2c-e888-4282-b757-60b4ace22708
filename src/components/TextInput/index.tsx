/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React, { useState } from 'react';
import {
  Platform,
  TextInput as RNTextInput,
  TextInputProps as RNTextInputProps,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { twMerge } from 'tailwind-merge';
import EyeClosed from '@/src/assets/svgs/EyeClosed';
import EyeO<PERSON> from '@/src/assets/svgs/EyeOpen';
import { TextInputProps } from './types';

type ExtendedTextInputProps = TextInputProps & {
  borderless?: boolean;
  inputRef?: React.RefObject<RNTextInput | null>;
};

const TextInput = ({
  type = 'text',
  label,
  error,
  className,
  inputClassName,
  labelClassName,
  errorClassName,
  placeholder,
  numberOfLines = 3,
  borderless = false,
  editable = true,
  inputRef,
  ...props
}: ExtendedTextInputProps) => {
  const [secureTextEntry, setSecureTextEntry] = useState(type === 'password');

  const inputProps: Partial<RNTextInputProps> = {
    ...props,
    placeholderTextColor: '#A3A3A3',
    style: {
      color: '#000000',
      paddingRight: type === 'password' ? 48 : 16,
      height: type === 'textarea' ? 200 : 'auto',
    },
    className: twMerge(
      'w-full px-4 py-4 rounded-xl bg-white',
      !editable ? 'bg-gray-100' : 'bg-white',
      borderless ? 'border-0 bg-transparent' : 'border border-[#D4D4D4]',
      'text-base text-black',
      Platform.select({
        ios: 'leading-6',
        android: 'leading-5',
      }),
      error && 'border-red-500',
      inputClassName,
    ),
  };

  const renderInput = () => {
    if (type === 'textarea') {
      return (
        <RNTextInput
          ref={inputRef}
          multiline
          numberOfLines={numberOfLines}
          textAlignVertical="top"
          placeholder={placeholder}
          editable={editable}
          {...inputProps}
        />
      );
    }

    const isPassword = type === 'password';

    return (
      <View className="relative">
        <RNTextInput
          ref={inputRef}
          placeholder={placeholder}
          editable={editable}
          secureTextEntry={secureTextEntry}
          keyboardType={!secureTextEntry ? 'visible-password' : 'default'}
          {...inputProps}
        />
        {isPassword && (
          <TouchableOpacity
            onPress={() => setSecureTextEntry((prev) => !prev)}
            className="absolute right-4 top-0 h-full justify-center"
            hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
            activeOpacity={0.7}
          >
            {!secureTextEntry ? <EyeOpen /> : <EyeClosed />}
          </TouchableOpacity>
        )}
      </View>
    );
  };

  return (
    <View className={twMerge('space-y-1', className)}>
      {label && (
        <Text className={twMerge('text-sm font-medium text-black mb-2 leading-4', labelClassName)}>
          {label}
        </Text>
      )}
      {renderInput()}
      {error && <Text className={twMerge('text-sm text-red-500', errorClassName)}>{error}</Text>}
    </View>
  );
};

export default TextInput;
