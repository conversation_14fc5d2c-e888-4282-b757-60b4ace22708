import { FlatList, Pressable, StatusBar, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import BackButton from '@/src/components/BackButton';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import AddItem from '@/src/assets/svgs/AddItem';
import { FetchCommunityClientI } from '@/src/networks/community/types';
import CommunityItem from '../CommunityItem';
import useMyCommunities from './useHook';

const CommunityList = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const insets = useSafeAreaInsets();

  const onBack = () => {
    navigation.navigate('Forum');
  };
  const { communities, loadMore } = useMyCommunities();

  const renderCommunityItem = ({ item }: { item: FetchCommunityClientI }) => (
    <CommunityItem
      community={item}
      // onPress={() => onCommunityPress?.(item)}
    />
  );

  const handleAdd = () => {
    navigation.navigate('CreateCommunity');
  };

  return (
    <View className="px-3 flex-1">
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      <View className="flex-row items-center justify-between ">
        <BackButton
          onBack={onBack}
          label="My communities"
          labelClassname="font-medium text-xl leading-6"
        />
        <Pressable onPress={handleAdd}>
          <AddItem />
        </Pressable>
      </View>

      <FlatList
        data={communities}
        renderItem={renderCommunityItem}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        className="flex-1"
        onEndReached={loadMore}
        onEndReachedThreshold={0.7}
        contentContainerStyle={{
          flexGrow: 1,
          paddingBottom: 50 + insets.bottom,
        }}
      />
    </View>
  );
};

export default CommunityList;
