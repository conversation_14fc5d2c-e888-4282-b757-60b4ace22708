import { Keyboard } from 'react-native';
import type { RouteProp } from '@react-navigation/native';
import { useNavigation, useRoute } from '@react-navigation/native';
import SafeArea from '@/src/components/SafeArea';
import type { BottomTabNavigationI, CreateStackParamsListI } from '@/src/navigation/types';
import CreatePostForm from './components/CreatePostForm';

const CreatePostScreen = () => {
  const navigation = useNavigation<BottomTabNavigationI>();
  const { params } = useRoute<RouteProp<CreateStackParamsListI, 'CreateContent'>>();

  const handleSuccess = () => {
    Keyboard.dismiss();
    setTimeout(() => {
      navigation.goBack();
    }, 100);
  };

  return (
    <SafeArea>
      <CreatePostForm
        onSuccess={handleSuccess}
        type={params?.type ?? 'USER_POST'}
        {...(params?.portUnLocode && { portUnLocode: params.portUnLocode })}
        {...(params?.editing && { editing: params.editing })}
        {...(params?.postId && { postId: params.postId })}
      />
    </SafeArea>
  );
};

export default CreatePostScreen;
