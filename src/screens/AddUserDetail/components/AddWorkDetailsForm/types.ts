/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { UseFormReturn } from 'react-hook-form';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';

export interface WorkDetailsFormDataI {
  entity: SearchResultI;
  designation: SearchResultI;
}

export interface UseWorkDetailsFormI {
  methods: UseFormReturn<WorkDetailsFormDataI>;
  isSubmitting: boolean;
  onSubmit: (data: WorkDetailsFormDataI) => Promise<void>;
  handleBack: () => void;
}

export interface WorkDetailsPropsI {
  onNext: (data: WorkDetailsFormDataI) => void;
  onBack: () => void;
  personalData: {
    fullName: string;
    country: SearchResultI;
    gender: string;
  };
  initialData?: WorkDetailsFormDataI;
}
