import { apiCall } from '@/src/services/api';
import {
  AddCertificationBodyI,
  AddCertificationResultI,
  CertificationI,
  EditCertificationBodyI,
  FetchCertificationListBodyI,
  FetchCertificationResultI,
} from './types';

export const addCertificationAPI = async (
  payload: AddCertificationBodyI,
): Promise<AddCertificationResultI> => {
  const result = await apiCall<AddCertificationBodyI, AddCertificationResultI>(
    '/backend/api/v1/career/profile-certificate',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};

export const fetchCertificationAPI = async (
  certificationId: string,
): Promise<FetchCertificationResultI> => {
  const result = await apiCall<string, FetchCertificationResultI>(
    `/backend/api/v1/career/profile-certificate/${certificationId}`,
    'GET',
    {
      isAuth: true,
    },
  );

  return result;
};

export const deleteCertificationAPI = async (certificationId: string) => {
  await apiCall<string>(`/backend/api/v1/career/profile-certificate/${certificationId}`, 'DELETE', {
    isAuth: true,
  });
};

export const fetchStatutoryCertificationsAPI = async (
  profileId: string,
  page: number,
  pageSize: number,
): Promise<CertificationI[]> => {
  const result = await apiCall<FetchCertificationListBodyI, CertificationI[]>(
    '/backend/api/v1/career/profile-certificates',
    'GET',
    {
      isAuth: true,
      query: {
        profileId: profileId,
        type: 'STATUTORY',
        page,
        pageSize,
      },
    },
  );

  return result;
};

export const fetchValueAddedCertificationsAPI = async (
  profileId: string,
  page: number,
  pageSize: number,
): Promise<CertificationI[]> => {
  const result = await apiCall<FetchCertificationListBodyI, CertificationI[]>(
    '/backend/api/v1/career/profile-certificates',
    'GET',
    {
      isAuth: true,
      query: {
        profileId: profileId,
        type: 'VALUE_ADDED',
        page,
        pageSize,
      },
    },
  );

  return result;
};

export const editCertificationAPI = async (
  certificationId: string,
  payload: EditCertificationBodyI,
): Promise<AddCertificationResultI> => {
  const result = await apiCall<EditCertificationBodyI, AddCertificationResultI>(
    `/backend/api/v1/career/profile-certificate/${certificationId}`,
    'PATCH',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};
