/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { View } from 'react-native';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from './types';

const Block: React.FC<OutlinedIconPropsI> = ({
  width = 2,
  height = 2,
  color = '#374151',
  stroke = '#374151',
  strokeWidth = 2,
  containerStyle,
  accessibilityLabel = 'Block icon',
  active,
  disabled,
  ...props
}) => {
  const iconColor = disabled ? '#9CA3AF' : active ? '#1D4ED8' : stroke || color;

  return (
    <View style={containerStyle}>
      <Svg
        width={RFPercentage(width)}
        height={RFPercentage(height)}
        viewBox="0 0 24 24"
        fill="none"
        stroke={iconColor}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
        accessibilityLabel={accessibilityLabel}
        {...props}
      >
        <Path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" />
        <Path d="M4.93 4.93l14.14 14.14" />
      </Svg>
    </View>
  );
};

export default Block;
