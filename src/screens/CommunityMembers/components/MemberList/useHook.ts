/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState, useEffect, useCallback } from 'react';
import { ListItem } from '@/src/components/UsersList/types';
import { UseMemberListPropsI, MemberListHookResultI, ForumMemberI } from './types';

// Mock data for demonstration - replace with actual API calls
const mockMembers: ForumMemberI[] = [
  {
    id: '1',
    Profile: {
      id: '1',
      avatar:
        'https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face',
      name: '<PERSON>',
      designation: {
        id: '1',
        name: 'Second Engineer',
        dataType: 'master',
      },
      entity: {
        id: '1',
        name: 'Seashore Corporations',
        dataType: 'master',
      },
    },
    role: 'MEMBER',
    joinedAt: '2024-01-15',
    status: 'ACTIVE',
  },
  {
    id: '2',
    Profile: {
      id: '2',
      avatar:
        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=60&h=60&fit=crop&crop=face',
      name: '<PERSON> Nutt',
      designation: {
        id: '2',
        name: 'Second Engineer',
        dataType: 'master',
      },
      entity: {
        id: '2',
        name: 'Seashore Corporations',
        dataType: 'master',
      },
    },
    role: 'ADMIN',
    joinedAt: '2024-02-10',
    status: 'ACTIVE',
  },
  {
    id: '3',
    Profile: {
      id: '3',
      avatar:
        'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=60&h=60&fit=crop&crop=face',
      name: 'Ole Ullrich',
      designation: {
        id: '3',
        name: 'Second Engineer',
        dataType: 'master',
      },
      entity: {
        id: '3',
        name: 'Seashore Corporations',
        dataType: 'master',
      },
    },
    role: 'ADMIN',
    joinedAt: '2024-03-05',
    status: 'ACTIVE',
  },
  {
    id: '4',
    Profile: {
      id: '4',
      avatar:
        'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=60&h=60&fit=crop&crop=face',
      name: 'Ruth Ruiz',
      designation: {
        id: '4',
        name: 'Second Engineer',
        dataType: 'master',
      },
      entity: {
        id: '4',
        name: 'Seashore Corporations',
        dataType: 'master',
      },
    },
    role: 'ADMIN',
    joinedAt: '2024-03-12',
    status: 'ACTIVE',
  },
  {
    id: '5',
    Profile: {
      id: '5',
      avatar:
        'https://images.unsplash.com/photo-*************-8f129e1688ce?w=60&h=60&fit=crop&crop=face',
      name: 'Patricia Banks',
      designation: {
        id: '5',
        name: 'Second Engineer',
        dataType: 'master',
      },
      entity: {
        id: '5',
        name: 'Seashore Corporations',
        dataType: 'master',
      },
    },
    role: 'MEMBER',
    joinedAt: '2024-03-18',
    status: 'ACTIVE',
  },
  {
    id: '6',
    Profile: {
      id: '6',
      avatar:
        'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=60&h=60&fit=crop&crop=face',
      name: 'Martin Heinrich',
      designation: {
        id: '6',
        name: 'Second Engineer',
        dataType: 'master',
      },
      entity: {
        id: '6',
        name: 'Seashore Corporations',
        dataType: 'master',
      },
    },
    role: 'MEMBER',
    joinedAt: '2024-03-25',
    status: 'ACTIVE',
  },
  {
    id: '7',
    Profile: {
      id: '7',
      avatar:
        'https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face',
      name: 'Larry Warren',
      designation: {
        id: '7',
        name: 'Second Engineer',
        dataType: 'master',
      },
      entity: {
        id: '7',
        name: 'Seashore Corporations',
        dataType: 'master',
      },
    },
    role: 'MEMBER',
    joinedAt: '2024-03-28',
    status: 'ACTIVE',
  },
];

export const useMemberList = ({
  forumId,
  pageSize = 20,
}: UseMemberListPropsI): MemberListHookResultI => {
  const [data, setData] = useState<ListItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [page, setPage] = useState<number>(1);

  const transformMembersToListItems = (members: ForumMemberI[]): ListItem[] => {
    return members.map((member) => ({
      Profile: member.Profile,
      status: member.role,
    }));
  };

  const fetchMembers = useCallback(
    async (pageNum: number, isRefresh = false) => {
      try {
        if (isRefresh) {
          setRefreshing(true);
        } else {
          setLoading(true);
        }

        // Simulate API call delay
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Mock pagination
        const startIndex = (pageNum - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const paginatedMembers = mockMembers.slice(startIndex, endIndex);

        const transformedData = transformMembersToListItems(paginatedMembers);

        if (isRefresh || pageNum === 1) {
          setData(transformedData);
        } else {
          setData((prev) => [...prev, ...transformedData]);
        }

        setHasMore(endIndex < mockMembers.length);
        setError(null);
      } catch (err) {
        setError(err as Error);
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    },
    [pageSize],
  );

  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchMembers(nextPage);
    }
  }, [loading, hasMore, page, fetchMembers]);

  const refresh = useCallback(() => {
    setPage(1);
    fetchMembers(1, true);
  }, [fetchMembers]);

  useEffect(() => {
    fetchMembers(1);
  }, [fetchMembers]);

  return {
    data,
    loading,
    refreshing,
    hasMore,
    error,
    loadMore,
    refresh,
  };
};
