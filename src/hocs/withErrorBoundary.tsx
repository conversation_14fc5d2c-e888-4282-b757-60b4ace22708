import type React from 'react';
import ErrorBoundary from '@/src/components/ErrorBoundary';

export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  options: {
    title?: string;
    subtitle?: string;
  } = {},
) {
  const { title, subtitle } = options;

  const WrappedComponent = (props: P) => {
    return (
      <ErrorBoundary title={title} subtitle={subtitle}>
        <Component {...props} />
      </ErrorBoundary>
    );
  };

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name || 'Component'})`;

  return WrappedComponent;
}
