#!/bin/bash

extensions=(
  "esbenp.prettier-vscode"
  "editorconfig.editorconfig"
  "dsznajder.es7-react-js-snippets"
  "eamodio.gitlens"
  "formulahendry.auto-rename-tag"
  "formulahendry.auto-close-tag"
  "naumovs.color-highlight"
  "orta.vscode-jest"
  "redhat.vscode-yaml"
)

if command -v code >/dev/null 2>&1; then
  editor_cmd="code"
else
  echo "ERROR: 'code' (VS Code) command not found."
  echo "Please make sure VS Code is added to your PATH."
  exit 1
fi

install_extension() {
  local extension=$1
  local max_attempts=3
  local attempt=1
  
  while [ $attempt -le $max_attempts ]; do
    if [ $attempt -eq 1 ]; then
      "$editor_cmd" --install-extension "$extension" && return 0
    else
      "$editor_cmd" --install-extension "$extension" --force && return 0
    fi
    attempt=$((attempt + 1))
    sleep 1
  done
  
  return 1
}

successful=()
failed=()

for extension in "${extensions[@]}"; do
  if install_extension "$extension"; then
    successful+=("$extension")
  else
    failed+=("$extension")
  fi
  sleep 2
done

echo ""
echo "=== Installation Summary ==="
echo "Successfully installed (${#successful[@]}):"
for ext in "${successful[@]}"; do
  echo "  - $ext"
done

if [ ${#failed[@]} -gt 0 ]; then
  echo ""
  echo "Failed to install (${#failed[@]}):"
  for ext in "${failed[@]}"; do
    echo "  - $ext"
  done
  echo ""
  echo "For failed extensions, you can try:"
  echo "1. Installing them manually through the Extensions marketplace"
  echo "2. Running VS Code with administrator privileges"
  echo "3. Checking your internet connection"
  echo "4. Temporarily disabling your antivirus/firewall"
fi

echo ""
echo "Installation process completed!"
