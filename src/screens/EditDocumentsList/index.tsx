/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useSelector } from 'react-redux';
import SafeArea from '@/src/components/SafeArea';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { ProfileStackParamsListI } from '@/src/navigation/types';
import EditDocumentsList from './components/EditDocumentationList';

type RouteProps = RouteProp<ProfileStackParamsListI, 'EditDocumentList'>;

const EditDocumentsListScreen = () => {
  const route = useRoute<RouteProps>();
  const { editable, tab, profileId } = route.params;
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();

  return (
    <SafeArea>
      <EditDocumentsList
        onBack={() => navigation.goBack()}
        profileId={editable ? useSelector(selectCurrentUser).profileId : profileId!}
        onAdd={(type: string) =>
          navigation.navigate('EditDocumentItem', {
            profileId,
            type,
          })
        }
        editable={editable!}
        tab={tab!}
      />
    </SafeArea>
  );
};

export default EditDocumentsListScreen;
