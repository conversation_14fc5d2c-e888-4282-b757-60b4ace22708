import NotificationScreen from '@/src/screens/Notifications';
import { withErrorBoundary } from '../../../hocs/withErrorBoundary';
import { NotificationStackParamsListI, StackScreenI } from '../../types';

const createErrorBoundaryScreen = (Component: any, title: string, subtitle: string) =>
  withErrorBoundary(Component, { title, subtitle });

const screenConfigs = [
  {
    name: 'Notification',
    component: NotificationScreen,
    title: 'Notifications Error',
    subtitle: 'Something went wrong loading your notifications. Please try again.',
  },
];

export const screens: StackScreenI<NotificationStackParamsListI>[] = screenConfigs.map(
  ({ name, component, title, subtitle }) => ({
    name: name as keyof NotificationStackParamsListI,
    component: createErrorBoundaryScreen(component, title, subtitle),
  }),
);
