/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { View } from 'react-native';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from './types';

const Copy: React.FC<OutlinedIconPropsI> = ({
  width = 24,
  height = 24,
  color = '#000',
  stroke = '#000',
  strokeWidth = 1.5,
  containerStyle,
  accessibilityLabel = 'Copy',
  active = false,
  disabled = false,
  ...props
}) => {
  const actualColor = disabled ? '#9CA3AF' : active ? '#10B981' : stroke || color;

  return (
    <View style={containerStyle} accessibilityLabel={accessibilityLabel}>
      <Svg
        width={RFPercentage(width)}
        height={RFPercentage(height)}
        viewBox="0 0 24 24"
        fill="none"
        stroke={actualColor}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
        {...props}
      >
        <Path d="M8 4v12a2 2 0 002 2h8a2 2 0 002-2V7.242a2 2 0 00-.602-1.43L16.083 2.57A2 2 0 0014.685 2H10a2 2 0 00-2 2z" />
        <Path d="M16 18v2a2 2 0 01-2 2H6a2 2 0 01-2-2V9a2 2 0 012-2h2" />
      </Svg>
    </View>
  );
};

export default Copy;
