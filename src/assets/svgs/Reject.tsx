/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const Reject: React.FC<FilledIconPropsI> = ({
  width = 1.6,
  height = 1.48,
  fill = '#DC2626',
  color,
  disabled,
  accessibilityLabel = 'Reject',
  ...props
}) => {
  const fillColor = color || fill;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 13 12"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.621 7.59a.376.376 0 11-.53.533l-1.589-1.59-1.6 1.6a.38.38 0 01-.534 0 .38.38 0 010-.535l1.6-1.602L4.379 4.41a.375.375 0 11.53-.533l1.589 1.59L8.11 3.855a.38.38 0 01.534 0 .38.38 0 010 .536L7.032 6.004l1.59 1.586zM6.5 0a6 6 0 00-6 6 6 6 0 006 6 6 6 0 006-6 6 6 0 00-6-6z"
        fill={fillColor}
      />
    </Svg>
  );
};

export default Reject;
