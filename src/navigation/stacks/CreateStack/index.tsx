/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { createStackNavigator } from '@react-navigation/stack';
import { CreateStackParamsListI } from '@/src/navigation/types';
import { screens } from './screen';

const CreateStack = createStackNavigator<CreateStackParamsListI>();

type CreateRouteNames = keyof CreateStackParamsListI;

const CreateStackNavigator = ({
  initialRouteName = 'CreateContent',
}: {
  initialRouteName?: CreateRouteNames;
}) => (
  <CreateStack.Navigator
    screenOptions={{
      headerShown: false,
      animation: 'none',
      cardStyle: { backgroundColor: 'white' },
      cardOverlayEnabled: false,
      cardShadowEnabled: false,
    }}
    initialRouteName={initialRouteName}
  >
    {screens.map(({ name, component }) => (
      <CreateStack.Screen key={name} name={name} component={component} />
    ))}
  </CreateStack.Navigator>
);

export default CreateStackNavigator;
