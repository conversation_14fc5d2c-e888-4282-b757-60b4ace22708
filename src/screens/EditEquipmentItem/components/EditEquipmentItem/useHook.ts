import { useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { selectMultipleSelectionsByKey } from '@/src/redux/selectors/search';
import {
  clearSelection,
  clearMultipleSelections,
} from '@/src/redux/slices/entitysearch/searchSlice';
import type { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import {
  addEquipmentCategoryShipExperience,
  addClientEquipment,
  updateClientEquipment,
} from '@/src/redux/slices/experience/experienceSlice';
import type { ClientEquipmentI } from '@/src/redux/slices/experience/types';
import type { AppDispatch, RootState } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import type { HomeStackParamListI } from '@/src/navigation/types';
import type { ShipCreateEditPayloadI } from '@/src/screens/EditShipItem/components/EditShipItem/types';
import { fetchSingleShipEquipmentCategory } from '@/src/networks/experienceShip.ts/ship';
import { generatePayloadEquipment } from '../utils';
import type {
  EquipmentCreateUpdatePayloadI,
  EquipmentDetailsFormDataI,
  UseEditEquipmentItemI,
} from './types';

export const useEditEquipmentItem = (
  equipmentId?: string,
  preFilledData?: ShipCreateEditPayloadI[],
  refetch?: () => void,
  isClientMode = false,
  onClientSave?: (equipment: any) => void,
  shipTempId?: string,
): UseEditEquipmentItemI & {
  handleClientSave: (data: EquipmentDetailsFormDataI) => void;
  clearFields: () => void;
  isSaving: boolean;
  isFuelLimitReached: boolean;
  MAX_FUEL_TYPES: number;
} => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [localFuelTypes, setLocalFuelTypes] = useState<SearchResultI[]>([]);
  const [initialFuelTypes, setInitialFuelTypes] = useState<SearchResultI[]>([]);
  const [loading, setLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  const navigation = useNavigation<StackNavigationProp<HomeStackParamListI>>();
  const dispatch = useDispatch<AppDispatch>();
  const fuelTypesSelection = useSelector(selectMultipleSelectionsByKey('fuelType'));
  const clientEquipments = useSelector((state: RootState) => state.experience.clientEquipments);

  const MAX_FUEL_TYPES = 10;
  const isFuelLimitReached = localFuelTypes.length > MAX_FUEL_TYPES;

  const methods = useForm<EquipmentDetailsFormDataI>({
    mode: 'onChange',
    defaultValues: {
      category: {},
      manufacturerName: '',
      model: '',
    },
  });

  const fuelTypesChanged =
    localFuelTypes.length !== initialFuelTypes.length ||
    localFuelTypes.some((fuel) => !initialFuelTypes.find((initial) => initial.id === fuel.id)) ||
    initialFuelTypes.some((initial) => !localFuelTypes.find((fuel) => fuel.id === initial.id));

  const watchedValues = methods.watch();
  const categoryChanged =
    watchedValues.category?.id !== methods.formState.defaultValues?.category?.id;

  const hasChanges = methods.formState.isDirty || fuelTypesChanged || categoryChanged;

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const clearFields = () => {
    dispatch(clearSelection('category'));
    dispatch(clearSelection('fuelType'));
    dispatch(clearMultipleSelections('fuelType'));
    setIsSubmitted(false);
    setLocalFuelTypes([]);
  };

  useEffect(() => {
    if (equipmentId && !isClientMode && !isInitialized) {
      const fetchEquipment = async () => {
        try {
          setLoading(true);
          const response = await fetchSingleShipEquipmentCategory(equipmentId);
          const fetchedEquipment: EquipmentDetailsFormDataI = {
            category: response.equipmentCategory,
            manufacturerName: response.manufacturerName,
            model: response.model,
            power: response.powerCapacity,
            fuelType: response.fuelTypes,
            additionalDetails: response.details!,
          };
          setLocalFuelTypes(response.fuelTypes?.length > 0 ? response.fuelTypes : []);
          setInitialFuelTypes(response.fuelTypes?.length > 0 ? response.fuelTypes : []);
          methods.reset(fetchedEquipment);
          setIsInitialized(true);
        } catch (err) {
          triggerErrorBoundary(
            new Error(
              'Failed to load equipment details: ' +
                (err instanceof Error ? err.message : 'Unknown error'),
            ),
          );
        } finally {
          setLoading(false);
        }
      };
      fetchEquipment();
    }
  }, [equipmentId, isClientMode, isInitialized, methods, triggerErrorBoundary]);

  useEffect(() => {
    if (equipmentId && isClientMode && !isInitialized) {
      const clientEquipment = clientEquipments.find(
        (equipment) => equipment.tempId === equipmentId,
      );
      if (clientEquipment) {
        const equipmentData: EquipmentDetailsFormDataI = {
          category: clientEquipment.category,
          manufacturerName: clientEquipment.manufacturerName,
          model: clientEquipment.model,
          power: clientEquipment.power,
          fuelType: clientEquipment.fuelTypes,
          additionalDetails: clientEquipment.additionalDetails,
        };
        setLocalFuelTypes(clientEquipment.fuelTypes || []);
        setInitialFuelTypes(clientEquipment.fuelTypes || []);
        methods.reset(equipmentData);
        setIsInitialized(true);
      }
    }
  }, [equipmentId, isClientMode, clientEquipments, isInitialized, methods]);

  useEffect(() => {
    if (!equipmentId && !isInitialized) {
      setIsInitialized(true);
    }
  }, [equipmentId, isInitialized]);

  useEffect(() => {
    if (!fuelTypesSelection) return;
    setLocalFuelTypes((prev) => {
      const existingIds = new Set(prev.map((s) => s.id));
      const merged = [...prev, ...fuelTypesSelection.filter((s) => !existingIds.has(s.id))];
      return merged;
    });
  }, [fuelTypesSelection]);

  const onSubmit = async (data: EquipmentDetailsFormDataI) => {
    if (isFuelLimitReached) {
      showToast({
        message: 'Fuel Type Limit Reached',
        description: `Max ${MAX_FUEL_TYPES} fuel types allowed`,
        type: 'error',
      });
      return;
    }
    const payload: EquipmentCreateUpdatePayloadI = generatePayloadEquipment(
      data,
      preFilledData!,
      localFuelTypes,
      initialFuelTypes,
      equipmentId,
    );
    try {
      setIsSubmitting(true);
      setIsSaving(true);
      await dispatch(addEquipmentCategoryShipExperience({ payload })).unwrap();
      showToast({
        message: 'Success',
        description: equipmentId
          ? 'Equipment updated successfully'
          : 'Equipment added successfully',
        type: 'success',
      });
      setTimeout(() => {
        clearFields();
        navigation.goBack();
      }, 100);
    } catch (error) {
      setIsSaving(false);
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Failed to Save Equipment Category',
            description: 'Unable to save Equipment Category',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsSubmitting(false);
      if (refetch) {
        refetch();
      }
    }
  };

  const handleClientSave = (data: EquipmentDetailsFormDataI) => {
    if (isFuelLimitReached) {
      showToast({
        message: 'Fuel Type Limit Reached',
        description: `Max ${MAX_FUEL_TYPES} fuel types allowed`,
        type: 'error',
      });
      return;
    }
    setIsSaving(true);
    const equipmentData: ClientEquipmentI = {
      tempId: equipmentId || `equipment_${Date.now()}_${Math.random()}`,
      shipTempId: shipTempId || '',
      category: {
        id: data.category.id,
        name: data.category.name,
        dataType: data.category.dataType,
        hasFuelType: data.category.hasFuelType || false,
      },
      manufacturerName: data.manufacturerName,
      model: data.model,
      power: data.power || '',
      additionalDetails: data.additionalDetails,
      fuelTypes: localFuelTypes,
    };

    if (equipmentId) {
      dispatch(updateClientEquipment({ tempId: equipmentId, equipment: equipmentData }));
    } else {
      dispatch(addClientEquipment(equipmentData));
    }
    if (onClientSave) {
      onClientSave(equipmentData);
    }
    setTimeout(() => {
      clearFields();
      navigation.goBack();
    }, 100);
  };

  return {
    methods,
    isSubmitting,
    onSubmit,
    navigation,
    localFuelTypes,
    setLocalFuelTypes,
    clearFields,
    isSubmitted,
    setIsSubmitted,
    loading,
    hasChanges,
    handleClientSave,
    isSaving,
    isFuelLimitReached,
    MAX_FUEL_TYPES,
  };
};
