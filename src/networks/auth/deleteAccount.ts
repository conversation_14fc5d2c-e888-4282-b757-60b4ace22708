import { apiCall } from '@/src/services/api';

export const deleteAccountAPI = async (): Promise<void> => {
  await apiCall<void, void>('/backend/api/v1/user/profile-status/delete', 'DELETE', {
    isAuth: true,
  });
};

export const deactivateAccountAPI = async (): Promise<void> => {
  await apiCall('/backend/api/v1/user/profile-status/deactivate', 'PATCH', {
    isAuth: true,
    payload: {
      status: 'DEACTIVATE',
    },
  });
};
