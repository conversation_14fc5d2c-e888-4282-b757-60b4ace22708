import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import APIResError from '@/src/errors/networks/APIResError';
import { CargoCreateUpdatePayloadI } from '@/src/screens/EditCargoItem/components/EditCargoItem/types';
import { EquipmentCreateUpdatePayloadI } from '@/src/screens/EditEquipmentItem/components/EditEquipmentItem/types';
import { ShipCreateEditPayloadI } from '@/src/screens/EditShipItem/components/EditShipItem/types';
import { addExperience } from '@/src/networks/experience/experience';
import {
  addCargoForShipExperience,
  addEquipmentCategoryForShipExperience,
  addShipForExperience,
} from '@/src/networks/experienceShip.ts/ship';
import { ExperienceStateI, ClientEquipmentI, ClientCargoI } from './types';

const initialState: ExperienceStateI = {
  experiences: { data: [], total: 0, portVisits: { total: 0, data: [] } },
  loading: false,
  error: null,
  clientEquipments: [],
  clientCargos: [],
};

export const addExperienceAsync = createAsyncThunk<
  unknown,
  { payload: any[] },
  { rejectValue: string }
>('experience/addExperience', async ({ payload }, { rejectWithValue }) => {
  try {
    await addExperience(payload);
  } catch (error: unknown) {
    if (error instanceof APIResError) {
      return rejectWithValue(error.message || 'Failed to add experience');
    }
    return rejectWithValue('Failed to add experience');
  }
});

export const addShipExperience = createAsyncThunk<
  string[],
  { payload: ShipCreateEditPayloadI[] },
  { rejectValue: string }
>('experience/addShipExperience', async ({ payload }, { rejectWithValue }) => {
  try {
    const response = await addShipForExperience(payload);
    return response;
  } catch (error: unknown) {
    if (error instanceof Error) {
      return rejectWithValue(error.message || 'Something went wrong');
    }
    return rejectWithValue('Something went wrong');
  }
});

export const addEquipmentCategoryShipExperience = createAsyncThunk<
  string[],
  { payload: EquipmentCreateUpdatePayloadI },
  { rejectValue: string }
>('experience/addEquipmentCategoryShipExperience', async ({ payload }, { rejectWithValue }) => {
  try {
    return await addEquipmentCategoryForShipExperience(payload);
  } catch (error: any) {
    return rejectWithValue(error?.message || 'Something went wrong');
  }
});

export const addCargoShipExperience = createAsyncThunk<
  string[],
  { payload: CargoCreateUpdatePayloadI },
  { rejectValue: string }
>('experience/addCargoShipExperience', async ({ payload }, { rejectWithValue }) => {
  try {
    return await addCargoForShipExperience(payload);
  } catch (error: any) {
    return rejectWithValue(error?.message || 'Something went wrong');
  }
});

const experienceSlice = createSlice({
  name: 'experience',
  initialState,
  reducers: {
    resetExperienceState: () => initialState,
    setProfileId: (state, action: PayloadAction<string>) => {
      state.profileId = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    addClientEquipment: (state, action: PayloadAction<ClientEquipmentI>) => {
      state.clientEquipments.push(action.payload);
    },
    updateClientEquipment: (
      state,
      action: PayloadAction<{ tempId: string; equipment: ClientEquipmentI }>,
    ) => {
      const index = state.clientEquipments.findIndex((eq) => eq.tempId === action.payload.tempId);
      if (index !== -1) {
        state.clientEquipments[index] = action.payload.equipment;
      }
    },
    removeClientEquipment: (state, action: PayloadAction<string>) => {
      state.clientEquipments = state.clientEquipments.filter((eq) => eq.tempId !== action.payload);
    },
    addClientCargo: (state, action: PayloadAction<ClientCargoI>) => {
      state.clientCargos.push(action.payload);
    },
    updateClientCargo: (state, action: PayloadAction<{ tempId: string; cargo: ClientCargoI }>) => {
      const index = state.clientCargos.findIndex((c) => c.tempId === action.payload.tempId);
      if (index !== -1) {
        state.clientCargos[index] = action.payload.cargo;
      }
    },
    removeClientCargo: (state, action: PayloadAction<string>) => {
      state.clientCargos = state.clientCargos.filter((c) => c.tempId !== action.payload);
    },
    clearClientEquipmentsByShip: (state, action: PayloadAction<string>) => {
      state.clientEquipments = state.clientEquipments.filter(
        (eq) => eq.shipTempId !== action.payload,
      );
    },
    clearClientCargosByShip: (state, action: PayloadAction<string>) => {
      state.clientCargos = state.clientCargos.filter((c) => c.shipTempId !== action.payload);
    },
    clearAllClientData: (state) => {
      state.clientEquipments = [];
      state.clientCargos = [];
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(addShipExperience.fulfilled, (state, action: PayloadAction<string[]>) => {
        state.clientEquipments = [];
        state.clientCargos = [];
      })
      .addCase(
        addEquipmentCategoryShipExperience.fulfilled,
        (state, action: PayloadAction<string[]>) => {},
      )
      .addCase(addCargoShipExperience.fulfilled, (state, action: PayloadAction<string[]>) => {});
  },
});

export const {
  resetExperienceState,
  setProfileId,
  clearError,
  addClientEquipment,
  updateClientEquipment,
  removeClientEquipment,
  addClientCargo,
  updateClientCargo,
  removeClientCargo,
  clearClientEquipmentsByShip,
  clearClientCargosByShip,
  clearAllClientData,
} = experienceSlice.actions;

export default experienceSlice.reducer;
