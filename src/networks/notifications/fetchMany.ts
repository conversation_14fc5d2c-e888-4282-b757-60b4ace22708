import { apiCall } from '@/src/services/api';
import {
  NotificationFetchManyResult,
  NotificationFetchManyItemI,
  NotificationFetchManyResultI,
} from './types';

export const fetchNotifications = async (params: {
  cursorId: string | null;
  pageSize?: number;
}): Promise<NotificationFetchManyResult> => {
  const result = await apiCall<unknown, NotificationFetchManyResult>(
    `/communication/api/v1/notifications`,
    'GET',
    {
      isAuth: true,
      query: {
        cursorId: params.cursorId,
        pageSize: params.pageSize || 10,
      },
    },
  );

  return result;
};

export const fetchNotificationData = async (
  items: NotificationFetchManyItemI[],
): Promise<NotificationFetchManyResultI> => {
  const result = await apiCall<unknown, NotificationFetchManyResultI>(
    `/backend/api/v1/communication/notifications`,
    'POST',
    {
      isAuth: true,
      payload: { items },
    },
  );

  return result;
};
