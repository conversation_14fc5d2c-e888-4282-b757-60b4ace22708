import type {
  NotificationFetchManyQueryI,
  ProfileForNotificationI,
  PostForNotificationI,
} from '@/src/networks/notifications/types';

export interface UseNotificationListReturn {
  notifications: NotificationFetchManyQueryI[];
  profiles: ProfileForNotificationI[];
  posts: PostForNotificationI[];
  loading: boolean;
  initialLoading: boolean;
  hasMore: boolean;
  refreshing: boolean;
  loadMore: () => void;
  refresh: () => void;
  markAllAsRead: () => Promise<void>;
  markAsRead: (notificationId: string) => Promise<void>;
  getProfileById: (id: string) => ProfileForNotificationI | undefined;
  getPostById: (id: string) => PostForNotificationI | undefined;
}
