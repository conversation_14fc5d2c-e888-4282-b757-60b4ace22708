import { useEffect, useState } from 'react';
import { Platform, Pressable, Text, View } from 'react-native';
import Modal from 'react-native-modal';
import DateTimePicker, { type DateTimePickerEvent } from '@react-native-community/datetimepicker';
import { twMerge } from 'tailwind-merge';
import Calendar from '@/src/assets/svgs/Calendar';
import TextView from '../TextView';
import type { DatePickerPropsI } from './types';

const DatePicker = ({
  title,
  selectedDate: initialSelectedDate,
  onDateChange,
  showMonthYear,
  disabled = false,
  className,
}: DatePickerPropsI & { className?: string }) => {
  const [date, setDate] = useState<Date>(new Date());
  const [visible, setVisible] = useState<boolean>(false);
  const [formattedDate, setFormattedDate] = useState<string>('');

  useEffect(() => {
    if (initialSelectedDate) {
      const newDate = new Date(initialSelectedDate);
      setDate(newDate);
      const formatDate = newDate.toLocaleDateString('en-US', {
        ...(showMonthYear
          ? { month: 'long', year: 'numeric' }
          : { day: '2-digit', month: 'long', year: 'numeric' }),
      });
      setFormattedDate(formatDate);
    } else {
      setFormattedDate(showMonthYear ? 'MM-YYYY' : 'DD-MM-YYYY');
    }
  }, [initialSelectedDate, showMonthYear]);

  const handleChange = (_event: DateTimePickerEvent, newSelectedDate?: Date) => {
    if (newSelectedDate) {
      setDate(newSelectedDate);
      onDateChange(newSelectedDate);
    }
    if (Platform.OS === 'android') {
      setVisible(false);
    }
  };

  const renderDatePicker = () => {
    if (Platform.OS === 'ios') {
      return (
        <Modal
          isVisible={visible}
          onBackdropPress={() => setVisible(false)}
          onBackButtonPress={() => setVisible(false)}
          style={{ margin: 0, justifyContent: 'flex-end' }}
          animationIn="slideInUp"
          animationOut="slideOutDown"
          animationInTiming={250}
          animationOutTiming={250}
          backdropTransitionInTiming={250}
          backdropTransitionOutTiming={1}
          statusBarTranslucent
          useNativeDriverForBackdrop
          hideModalContentWhileAnimating={false}
          avoidKeyboard
        >
          <View
            style={{
              backgroundColor: 'white',
              borderTopLeftRadius: 20,
              borderTopRightRadius: 20,
              paddingTop: 20,
              zIndex: 1000,
            }}
          >
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                paddingHorizontal: 20,
                paddingBottom: 10,
              }}
            >
              <Pressable onPress={() => setVisible(false)}>
                <Text style={{ color: '#448600', fontSize: 16 }}>Cancel</Text>
              </Pressable>
              <Pressable
                onPress={() => {
                  onDateChange(date);
                  setVisible(false);
                }}
              >
                <Text style={{ color: '#448600', fontSize: 16, fontWeight: 'bold' }}>Done</Text>
              </Pressable>
            </View>
            <View className="items-center">
              <DateTimePicker
                value={date}
                mode="date"
                display="spinner"
                onChange={handleChange}
                style={{ height: 200 }}
                minimumDate={new Date(1970, 0, 1)}
              />
            </View>
          </View>
        </Modal>
      );
    }
    return visible ? (
      <View className="items-center">
        <DateTimePicker
          value={date}
          mode="date"
          display="spinner"
          onChange={handleChange}
          onTouchCancel={() => setVisible(!visible)}
        />
      </View>
    ) : null;
  };

  return (
    <View className="">
      <Text className="pb-2 text-gray-700">{title}</Text>
      <Pressable onPress={() => !disabled && setVisible(!visible)} disabled={disabled}>
        <View
          className={twMerge(
            `border border-[#D4D4D4] flex-row items-center rounded-xl py-2 w-full justify-center ${
              disabled ? 'bg-gray-100' : ''
            }`,
            className,
          )}
        >
          <TextView
            subtitle={formattedDate}
            subtitleClassName={`pb-3 pl-3 font-inter ${
              showMonthYear ? 'text-[14px]' : 'text-[13px]'
            } ${!initialSelectedDate ? 'text-gray-400' : 'text-gray-700'}`}
          />
          <View className="ml-auto pr-[14px]">
            <Calendar />
          </View>
        </View>
      </Pressable>
      {renderDatePicker()}
    </View>
  );
};

export default DatePicker;
