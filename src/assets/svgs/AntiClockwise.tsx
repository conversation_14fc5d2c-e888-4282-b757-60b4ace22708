/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from './types';

const Anticlockwise: React.FC<OutlinedIconPropsI> = ({
  width = 2.48,
  height = 2.356,
  stroke = '#404040',
  color,
  accessibilityLabel = 'Anticlockwise',
  disabled,
  ...props
}) => {
  const strokeColor = color || stroke;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 20 19"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        d="M10.8467 4.76016V8.83547L14.2329 10.867C14.4035 10.9695 14.5264 11.1355 14.5746 11.3285C14.6227 11.5216 14.5922 11.7258 14.4898 11.8964C14.3874 12.067 14.2214 12.1899 14.0283 12.238C13.8353 12.2862 13.631 12.2557 13.4604 12.1533L9.71043 9.90329C9.59944 9.8366 9.50761 9.74233 9.44386 9.62962C9.38012 9.51692 9.34664 9.38964 9.34668 9.26016V4.76016C9.34668 4.56125 9.4257 4.37048 9.56635 4.22983C9.707 4.08918 9.89777 4.01016 10.0967 4.01016C10.2956 4.01016 10.4864 4.08918 10.627 4.22983C10.7677 4.37048 10.8467 4.56125 10.8467 4.76016ZM10.0967 0.26016C8.91355 0.257213 7.74158 0.488941 6.64861 0.941935C5.55563 1.39493 4.56334 2.06019 3.72918 2.89922C3.04762 3.58922 2.44199 4.25297 1.84668 4.94766V3.26016C1.84668 3.06125 1.76766 2.87048 1.62701 2.72983C1.48636 2.58918 1.29559 2.51016 1.09668 2.51016C0.897767 2.51016 0.707002 2.58918 0.56635 2.72983C0.425697 2.87048 0.34668 3.06125 0.34668 3.26016V7.01016C0.34668 7.20907 0.425697 7.39984 0.56635 7.54049C0.707002 7.68114 0.897767 7.76016 1.09668 7.76016H4.84668C5.04559 7.76016 5.23636 7.68114 5.37701 7.54049C5.51766 7.39984 5.59668 7.20907 5.59668 7.01016C5.59668 6.81125 5.51766 6.62048 5.37701 6.47983C5.23636 6.33918 5.04559 6.26016 4.84668 6.26016H2.69043C3.36074 5.47079 4.02824 4.72735 4.78949 3.95672C5.83186 2.91436 7.15828 2.20229 8.60303 1.90948C10.0478 1.61667 11.5468 1.75611 12.9127 2.31037C14.2787 2.86462 15.451 3.80913 16.2832 5.02586C17.1155 6.24258 17.5707 7.67759 17.592 9.15155C17.6133 10.6255 17.1998 12.0731 16.4031 13.3134C15.6064 14.5537 14.4619 15.5317 13.1126 16.1252C11.7632 16.7188 10.2689 16.9015 8.81627 16.6506C7.36366 16.3997 6.0172 15.7263 4.94512 14.7145C4.87347 14.6468 4.78918 14.5939 4.69707 14.5587C4.60496 14.5236 4.50683 14.507 4.40828 14.5097C4.30973 14.5125 4.2127 14.5347 4.12272 14.575C4.03274 14.6153 3.95158 14.6729 3.88387 14.7445C3.81615 14.8162 3.76322 14.9005 3.72808 14.9926C3.69294 15.0847 3.67629 15.1828 3.67908 15.2814C3.68186 15.3799 3.70403 15.477 3.74432 15.5669C3.7846 15.6569 3.84221 15.7381 3.91387 15.8058C4.9821 16.8139 6.28082 17.5452 7.69668 17.9359C9.11255 18.3266 10.6025 18.3649 12.0366 18.0473C13.4706 17.7298 14.8052 17.0661 15.9237 16.1142C17.0423 15.1622 17.9109 13.951 18.4537 12.5862C18.9965 11.2214 19.197 9.74452 19.0378 8.2844C18.8785 6.82427 18.3643 5.42531 17.5401 4.2096C16.7159 2.99389 15.6066 1.99838 14.3092 1.30996C13.0117 0.621533 11.5655 0.261116 10.0967 0.26016Z"
        fill={strokeColor}
      />
    </Svg>
  );
};

export default Anticlockwise;
