import { Text, View } from 'react-native';
import BackButton from '@/src/components/BackButton';

const MemberListHeader = ({ handleBack }: { handleBack: () => void }) => {
  return (
    <View className="flex-row items-center px-4 py-3 border-b border-gray-100">
      <BackButton onBack={handleBack} label="" />
      <Text className="text-xl font-semibold text-gray-900 ml-2">Member List</Text>
    </View>
  );
};

export default MemberListHeader;
