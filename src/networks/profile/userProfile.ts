/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { apiCall } from '@/src/services/api';
import { EditUserProfilePayloadI, FetchProfileResultI } from './types';

export const fetchProfileAPI = async (routeId: string): Promise<FetchProfileResultI> => {
  const result = await apiCall<unknown, FetchProfileResultI>(
    `/backend/api/v1/user/profile/data`,
    'GET',
    {
      isAuth: true,
      routeId,
    },
  );

  return result;
};

export const editUserProfileAPI = async (payload: EditUserProfilePayloadI) => {
  const response = await apiCall<unknown, unknown>('/backend/api/v1/user/profile/bio', 'PATCH', {
    isAuth: true,
    payload,
  });

  return response;
};
