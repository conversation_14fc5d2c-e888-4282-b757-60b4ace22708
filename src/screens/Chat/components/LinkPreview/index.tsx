import { useState, useEffect } from 'react';
import { View, Text, Image, ActivityIndicator, Pressable, Linking } from 'react-native';
import { URL } from 'react-native-url-polyfill';
import type { LinkPreviewProps, OGData } from './types';

const LinkPreview = ({ url }: LinkPreviewProps) => {
  const [ogData, setOgData] = useState<OGData>({
    url,
    loading: true,
    error: false,
  });

  useEffect(() => {
    const fetchOGData = async () => {
      try {
        const response = await fetch(url);
        const html = await response.text();

        const getMetaTag = (name: string): string | undefined => {
          const match = html.match(
            new RegExp(
              `<meta(?:.*?)(?:property|name)=["']${name}["'](?:.*?)content=["'](.*?)["']`,
              'i',
            ),
          );
          return match ? match[1] : undefined;
        };

        const title = getMetaTag('og:title') || getMetaTag('twitter:title') || getMetaTag('title');
        const description =
          getMetaTag('og:description') ||
          getMetaTag('twitter:description') ||
          getMetaTag('description');
        const image = getMetaTag('og:image') || getMetaTag('twitter:image');
        const siteName = getMetaTag('og:site_name') || new URL(url).hostname;

        if (title || image) {
          setOgData({
            title,
            description,
            image,
            siteName,
            url,
            loading: false,
            error: false,
          });
        } else {
          setOgData({
            url,
            loading: false,
            error: true,
          });
        }
      } catch (error) {
        setOgData({
          url,
          loading: false,
          error: true,
        });
      }
    };

    fetchOGData();
  }, [url]);

  const handlePress = () => {
    Linking.openURL(url).catch(() => {});
  };

  if (ogData.loading) {
    return (
      <View className="mt-2 p-3 bg-gray-100 rounded-lg">
        <ActivityIndicator size="small" color="#666666" />
      </View>
    );
  }

  if (ogData.error) {
    return (
      <Pressable onPress={handlePress} className="mt-2 p-3 bg-gray-100 rounded-lg">
        <Text className="text-green-800 text-xs" numberOfLines={1}>
          {url}
        </Text>
      </Pressable>
    );
  }

  return (
    <Pressable
      onPress={handlePress}
      className="mt-2 bg-gray-100 rounded-lg overflow-hidden shadow-sm"
    >
      {ogData.image && (
        <Image source={{ uri: ogData.image }} className="w-full h-36" resizeMode="cover" />
      )}
      <View className="p-3">
        {ogData.title && (
          <Text className="font-semibold text-sm" numberOfLines={2}>
            {ogData.title}
          </Text>
        )}
        {ogData.description && (
          <Text className="text-xs text-gray-600 mt-1" numberOfLines={3}>
            {ogData.description}
          </Text>
        )}
        <View className="flex-row items-center mt-2">
          <View className="w-3 h-3 rounded-full bg-gray-300 mr-2" />
          <Text className="text-xs text-gray-500">{ogData.siteName || new URL(url).hostname}</Text>
        </View>
      </View>
    </Pressable>
  );
};

export default LinkPreview;
