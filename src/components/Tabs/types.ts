/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { Dispatch, SetStateAction } from 'react';

export interface Tab {
  id: string;
  label: string;
}

export interface TabsProps<T = string> {
  tabs: Tab[];
  activeTab: T;
  onTabChange: Dispatch<SetStateAction<T>>;
  styles?: string;
  disabled?: boolean;
}

export interface TabItemProps {
  tab: Tab;
  isActive: boolean;
  onPress: () => void;
  styles?: string;
  disabled?: boolean;
}
