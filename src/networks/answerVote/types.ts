export type VoteType = 'UPVOTE' | 'DOWNVOTE';

export interface ForumAnswerVoteFetchManyParamsI {
  answerId: string;
  cursorId?: number;
  pageSize?: number;
  type: VoteType;
}

export interface ProfileExternalI {
  id: string;
  name: string;
  avatar: string | null;
  cursorId?: number;
  designation: { id: string; name: string; dataType: string } | null;
  entity: { id: string; name: string; dataType: string } | null;
}

export interface ForumAnswerVoteFetchManyResponseI {
  total: number;
  data: ProfileExternalI[];
  nextCursorId: number | null;
}

export interface ForumAnswerVoteCreateOnePayloadI {
  answerId: string;
  type: VoteType;
}

export interface ForumAnswerVoteCreateOneResponseI {
  id: string;
  cursorId: number;
}
