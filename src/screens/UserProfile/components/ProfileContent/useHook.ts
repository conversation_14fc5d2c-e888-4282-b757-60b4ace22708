/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { selectProfilePosts, selectProfilePostsPagination } from '@/src/redux/selectors/content';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import {
  addReactionOptimistc,
  deletePostOptimistic,
  fetchProfilePosts,
  removeReactionOptimistic,
  revertDeletePostOptimistic,
} from '@/src/redux/slices/content/contentSlice';
import { AppDispatch } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import { followOrUnfollowAPI } from '@/src/networks/connect/follow';
import { deletePostAPI } from '@/src/networks/content/post';
import { upsertReactionAPI, deleteReactionAPI } from '@/src/networks/content/reaction';
import { PostExternalClientI } from '@/src/networks/content/types';
import { FetchProfileResultI } from '@/src/networks/profile/types';
import { AboutProfileI, UseProfileContentResult } from './types';

const useProfileContent = (
  data: FetchProfileResultI,
  profileId: string,
  aboutProfile: AboutProfileI | null,
): UseProfileContentResult => {
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [following, setFollowing] = useState(aboutProfile?.isFollowing ?? false);
  const [error, setError] = useState<Error | null>(null);
  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) throw error;

  const dispatch = useDispatch<AppDispatch>();

  const profilePosts = useSelector((state) => selectProfilePosts(state, profileId));
  const pagination = useSelector((state) => selectProfilePostsPagination(state, profileId));
  const currentUser = useSelector(selectCurrentUser);

  const fetchPosts = async (refresh = false) => {
    try {
      const newCursorId = refresh ? null : pagination.cursorId?.toString()!;
      return await dispatch(
        fetchProfilePosts({
          profileId,
          refresh,
          cursorId: newCursorId,
        }),
      ).unwrap();
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Error',
        description: 'Failed to load posts',
      });
      if (!profilePosts || profilePosts.length === 0) {
        triggerErrorBoundary(new Error('Failed to load profile posts'));
      }
      throw error;
    }
  };

  useEffect(() => {
    const loadInitialPosts = async () => {
      if (!profileId) return;

      setLoading(true);
      try {
        await fetchPosts(true);
      } catch (error) {
      } finally {
        setLoading(false);
      }
    };

    loadInitialPosts();
  }, [profileId]);

  const handleRefresh = async () => {
    if (refreshing) return;

    setRefreshing(true);
    try {
      await fetchPosts(true);
    } catch (error) {
    } finally {
      setRefreshing(false);
    }
  };

  const handleLoadMore = async () => {
    if (
      !pagination.hasMore ||
      !pagination.cursorId ||
      loading ||
      refreshing ||
      profilePosts.length === 0
    ) {
      return;
    }

    setLoading(true);
    try {
      await fetchPosts(false);
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const handleLikePost = async (post: PostExternalClientI, reactionType = 'LIKE') => {
    if (!currentUser || !post?.id) return;

    const currentPost = profilePosts.find((p) => p.id === post.id);
    const wasLiked = currentPost?.isLiked ?? post.isLiked;

    try {
      if (wasLiked) {
        dispatch(removeReactionOptimistic({ postId: post.id }));
        await deleteReactionAPI({ postId: post.id });
      } else {
        dispatch(addReactionOptimistc({ postId: post.id }));
        await upsertReactionAPI({ postId: post.id, reactionType });
      }
    } catch (error) {
      if (wasLiked) {
        dispatch(addReactionOptimistc({ postId: post.id }));
        showToast({
          type: 'error',
          message: 'Error',
          description: 'Failed to unlike post',
        });
      } else {
        dispatch(removeReactionOptimistic({ postId: post.id }));
        showToast({
          type: 'error',
          message: 'Error',
          description: 'Failed to like post',
        });
      }
    }
  };

  const handleDeletePost = async (post: PostExternalClientI) => {
    if (!post?.id) return;

    try {
      dispatch(deletePostOptimistic({ post }));
      await deletePostAPI(post.id);
    } catch (error) {
      dispatch(revertDeletePostOptimistic({ postId: post.id }));
      showToast({
        type: 'error',
        message: 'Error',
        description: 'Failed to delete post',
      });
    }
  };

  const handleFollow = async () => {
    if (!data?.profileId) return;
    const isCurrentlyFollowing = following;
    try {
      await followOrUnfollowAPI(data.profileId, isCurrentlyFollowing);
      return true;
    } catch (error) {
      throw error;
    }
  };

  return {
    profilePosts,
    refreshing,
    loading,
    following,
    setFollowing,
    handleFollow,
    handleRefresh,
    handleLoadMore,
    handleLikePost,
    handleDeletePost,
  };
};

export default useProfileContent;
