import React from 'react';
import { View, Text, Image, Pressable } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import SafeArea from '@/src/components/SafeArea';
import { BottomTabNavigationI } from '@/src/navigation/types';

const NotFoundScreen = () => {
  const navigation = useNavigation<BottomTabNavigationI>();

  const handleGoBack = () => navigation.navigate('HomeStack', { screen: 'Home' });

  return (
    <SafeArea>
      <View className="flex-1 justify-center items-center px-8 bg-white">
        <View className="items-center mb-12">
          <Image
            source={require('@/src/assets/images/others/notfound.png')}
            className="w-64 h-64 mb-8"
            resizeMode="contain"
          />

          <Text className="text-2xl font-bold text-slate-800 mb-3 text-center">
            Oops! Page Not Found
          </Text>

          <Text className="text-base text-slate-500 text-center leading-relaxed max-w-sm">
            The page you're looking for seems to have sailed away. Let's get you back on course.
          </Text>
        </View>

        <Pressable
          onPress={handleGoBack}
          className="bg-green-800 px-8 py-4 rounded-2xl shadow-lg active:scale-95"
        >
          <Text className="text-white font-semibold text-base">Navigate Back</Text>
        </Pressable>
      </View>
    </SafeArea>
  );
};

export default NotFoundScreen;
