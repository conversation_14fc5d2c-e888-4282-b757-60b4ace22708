/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import {
  QuestionCommentI,
  QuestionCommentReplyI,
  AnswerCommentI,
  AnswerCommentReplyI,
} from '@/src/networks/forum/types';

export type ForumCommentTypeI = QuestionCommentI | AnswerCommentI;
export type ForumCommentReplyTypeI = QuestionCommentReplyI | AnswerCommentReplyI;

export type UseForumCommentsPropsI = {
  postId: string;
  type: 'FORUM_QUESTION' | 'FORUM_ANSWER';
};
