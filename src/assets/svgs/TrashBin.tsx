/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from './types';

const TrashBin: React.FC<OutlinedIconPropsI> = ({
  width = 2.976,
  height = 3.224,
  color = '#000000',
  stroke,
  strokeWidth = 1.4,
  ...props
}) => {
  const strokeColor = stroke || color;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 24 26"
      fill="none"
      {...props}
    >
      <Path
        d="M9.25 13V19.6666"
        stroke={strokeColor}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M14.75 13V19.6666"
        stroke={strokeColor}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M1 6.3335H23"
        stroke={strokeColor}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M3.75 10.3333V20.9999C3.75 23.2091 5.59683 24.9998 7.875 24.9998H16.125C18.4032 24.9998 20.25 23.2091 20.25 20.9999V10.3333"
        stroke={strokeColor}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M7.875 3.66665C7.875 2.1939 9.10622 1 10.625 1H13.375C14.8938 1 16.125 2.1939 16.125 3.66665V6.3333H7.875V3.66665Z"
        stroke={strokeColor}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default TrashBin;
