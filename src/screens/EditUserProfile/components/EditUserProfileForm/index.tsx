import { useCallback, useState, useEffect, useRef } from 'react';
import {
  Pressable,
  Text,
  View,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { useFocusEffect, useNavigation, type NavigationAction } from '@react-navigation/native';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import EntitySearch from '@/src/components/EntitySearch';
import CustomModal from '@/src/components/Modal';
import TextInput from '@/src/components/TextInput';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import AvatarPicker from '../AvatarPicker';
import type { EditUserProfileFormPropsI } from './types';
import useEditUserProfile from './useHook';

const EditUserProfileForm = ({ onBack }: EditUserProfileFormPropsI) => {
  const {
    methods,
    isSubmitting,
    onSubmit,
    handleAvatarChange,
    handleAvatarDelete,
    avatarFile,
    isAvatarDeleted,
    loading,
    hasChanges,
  } = useEditUserProfile();

  const [charCount, setCharCount] = useState(0);
  const currentUser = useSelector(selectCurrentUser);
  const entitySelection = useSelector(selectSelectionByKey('entity'));
  const designationSelection = useSelector(selectSelectionByKey('designation'));

  const {
    control,
    handleSubmit,
    setValue,
    trigger,
    formState: { errors },
  } = methods;

  const navigation = useNavigation();
  const [showDiscardModal, setShowDiscardModal] = useState(false);
  const exitActionRef = useRef<NavigationAction | null>(null);

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', (e) => {
      if (hasChanges && !isSubmitting) {
        e.preventDefault();
        exitActionRef.current = e.data.action;
        setShowDiscardModal(true);
      }
    });
    return unsubscribe;
  }, [navigation, hasChanges, isSubmitting]);

  const handleDiscardConfirm = () => {
    setShowDiscardModal(false);
    setTimeout(() => {
      if (exitActionRef.current) {
        navigation.dispatch(exitActionRef.current);
      }
    }, 300);
  };

  const handleDiscardCancel = () => {
    setShowDiscardModal(false);
  };

  useFocusEffect(
    useCallback(() => {
      if (entitySelection) {
        setValue('entity', entitySelection);
        trigger('entity');
      }
      if (designationSelection) {
        setValue('designation', designationSelection);
        trigger('designation');
      }
      if (currentUser.description) {
        setCharCount(currentUser.description.length);
      }
    }, [entitySelection, designationSelection, currentUser.description, setValue, trigger]),
  );

  const getAvatarValue = () => {
    if (isAvatarDeleted) return null;
    if (avatarFile?.path) return avatarFile.path;
    return currentUser.avatar;
  };

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#448600" />
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      <View className="px-4">
        <View className="flex-row items-center justify-between pb-4">
          <BackButton onBack={onBack} label="" />
          <Pressable onPress={handleSubmit(onSubmit)} disabled={isSubmitting || !hasChanges}>
            <Text
              className={`font-inter text-lg font-medium ${isSubmitting || !hasChanges ? 'text-gray-400' : 'text-[#448600]'}`}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </Text>
          </Pressable>
        </View>
      </View>
      <ScrollView
        className="flex-1 px-4"
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        contentContainerStyle={{ paddingBottom: 50 }}
      >
        <Controller
          control={control}
          name="avatar"
          render={() => (
            <AvatarPicker
              value={getAvatarValue()}
              onChange={handleAvatarChange}
              onDelete={handleAvatarDelete}
              size={120}
              loading={loading}
            />
          )}
        />
        <Controller
          control={control}
          name="name"
          rules={{
            required: 'Name is required',
          }}
          render={({ field: { onChange, value } }) => (
            <TextInput
              label="Name"
              placeholder="Enter your name"
              value={value}
              onChangeText={onChange}
              error={errors.name?.message}
              className="mb-4"
            />
          )}
        />
        <View className="mb-4">
          <Controller
            control={control}
            name="designation"
            rules={{
              required: 'Rank is required',
            }}
            render={({ fieldState: { error } }) => (
              <EntitySearch
                placeholder="Search rank"
                selectionKey="designation"
                title="Rank"
                data={designationSelection?.name || currentUser.designation?.name}
                error={error?.message}
                className="my-0"
                titleClassName="text-black"
              />
            )}
          />
        </View>
        <View className="mb-4">
          <Controller
            control={control}
            name="entity"
            render={({ fieldState: { error } }) => (
              <EntitySearch
                placeholder="Search company"
                selectionKey="entity"
                title="Company"
                data={entitySelection?.name || currentUser.organisation?.name}
                error={error?.message}
                className="my-0"
                titleClassName="text-black"
              />
            )}
          />
        </View>
        <View className="relative mt-2">
          <Controller
            control={control}
            name="description"
            render={({ field: { onChange, value } }) => (
              <>
                <TextInput
                  label="Description"
                  placeholder="Write a short bio about yourself..."
                  value={value}
                  onChangeText={(text) => {
                    if (text.length <= 255) {
                      onChange(text);
                      setCharCount(text.length);
                    }
                  }}
                  type="textarea"
                  numberOfLines={6}
                  error={errors.description?.message}
                  maxLength={255}
                  multiline={true}
                  textAlignVertical="top"
                />
                <View className="absolute bottom-2 right-2 px-2 py-1 rounded-full bg-gray-100">
                  <Text
                    className={`text-xs font-medium ${
                      charCount > 230
                        ? charCount > 245
                          ? 'text-red-600'
                          : 'text-orange-600'
                        : 'text-gray-600'
                    }`}
                  >
                    {charCount}/255
                  </Text>
                </View>
              </>
            )}
          />
        </View>
      </ScrollView>
      <CustomModal
        isVisible={showDiscardModal}
        title="Discard changes?"
        description="You have unsaved changes. Are you sure you want to discard them and leave this screen?"
        onCancel={handleDiscardCancel}
        onConfirm={handleDiscardConfirm}
        cancelText="Cancel"
        confirmText="Discard"
        confirmButtonVariant="danger"
      />
    </KeyboardAvoidingView>
  );
};

export default EditUserProfileForm;
