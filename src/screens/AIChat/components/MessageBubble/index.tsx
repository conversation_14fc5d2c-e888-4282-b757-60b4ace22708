import type React from 'react';
import { View, Text } from 'react-native';
import TypingDots from '../TypingDots';
import type { MessageBubbleProps } from './types';
import { useMessageBubble } from './useMessageBubble';

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isLoading,
  streamedText,
  currentAiMessageId,
}) => {
  const { formatMessageTime } = useMessageBubble();
  const isAI = message.from === 'ai';
  const isLastAI = isAI && isLoading && !message.text;
  const isCurrentStreaming = currentAiMessageId === message.id && streamedText;

  const displayText = isCurrentStreaming ? streamedText : message.text;

  return (
    <View className={`mb-2 ${isAI ? 'self-start' : 'self-end'} max-w-[80%]`}>
      <View
        className={`px-3 py-3 rounded-xl ${
          isAI ? 'bg-white border border-gray-100' : 'bg-violet-100 border border-violet-100'
        }`}
      >
        {isLastAI ? (
          <View className="flex-row items-center">
            <TypingDots />
          </View>
        ) : (
          <Text className={`text-sm ${isAI ? 'text-gray-900' : 'text-gray-900'}`}>
            {displayText}
          </Text>
        )}
        <Text className={`text-xs text-gray-400 mt-1 text-right`}>
          {formatMessageTime(new Date(message.timestamp))}
        </Text>
      </View>
    </View>
  );
};
