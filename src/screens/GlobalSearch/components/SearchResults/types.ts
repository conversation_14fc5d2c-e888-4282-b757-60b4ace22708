import type { GlobalSearchCategory, GlobalSearchResponse } from '@/src/utilities/search/types';

export interface SearchResultsProps {
  activeTab: GlobalSearchCategory;
  loading: boolean;
  searchResults: GlobalSearchResponse;
  onTabChange?: (tab: GlobalSearchCategory) => void;
  onLoadMore?: () => void;
  onRefresh: () => void;
  refreshing: boolean;
  searchText?: string;
  onError?: (error: Error) => void;
}

export interface SearchCategoryTabs {
  id: GlobalSearchCategory;
  label: string;
}

export type SearchResultItemI = {
  id: string;
  profileId?: string;
  name?: string;
  title?: string;
  imo?: string;
  unLocode?: string;
  dataType?: string;
  matchedName?: string;
  imageUrl?: string;
  avatar?: string;
  priority?: number;
  designation?: {
    name: string;
  };
  entity?: {
    name: string;
  };
  city?: {
    name: string;
  };
  country?: {
    name: string;
  };
  [key: string]: unknown;
};
