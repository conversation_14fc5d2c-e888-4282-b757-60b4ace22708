/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { createStackNavigator } from '@react-navigation/stack';
import { LearnCollabStackParamsListI } from '../../types';
import { screens } from './screen';

const LearnCollabStack = createStackNavigator<LearnCollabStackParamsListI>();

type LearnCollabRouteNames = keyof LearnCollabStackParamsListI;

const LearnCollabStackNavigator = ({
  initialRouteName = 'Forum',
}: {
  initialRouteName?: LearnCollabRouteNames;
}) => (
  <LearnCollabStack.Navigator
    screenOptions={{ headerShown: false }}
    initialRouteName={initialRouteName}
  >
    {screens.map(({ name, component }) => (
      <LearnCollabStack.Screen key={name} name={name} component={component} />
    ))}
  </LearnCollabStack.Navigator>
);

export default LearnCollabStackNavigator;
