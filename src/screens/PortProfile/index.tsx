import React from 'react';
import { ReactNode, useState, useEffect } from 'react';
import { ActivityIndicator, View } from 'react-native';
import { RouteProp, useRoute } from '@react-navigation/native';
import Button from '@/src/components/Button';
import CustomModal from '@/src/components/Modal';
import SafeArea from '@/src/components/SafeArea';
import Tabs from '@/src/components/Tabs';
import { HomeStackParamListI } from '@/src/navigation/types';
import Tick from '@/src/assets/svgs/Tick';
import { fetchPortProfile } from '@/src/networks/port/profile';
import { addPortVisitor, removePortVisitor } from '@/src/networks/port/visitor';
import PortAbout from './components/PortAbout';
import { PortProfileData } from './components/PortAbout/types';
import PortHeader from './components/PortHeader';
import PortScrapbook from './components/PortScrapbook';
import PortVisitors from './components/PortVisitors';

const PortScreen: React.FC = () => {
  const routes = useRoute<RouteProp<HomeStackParamListI, 'PortProfile'>>();
  const portUnLocode = routes.params?.unLocode;
  const shipDataType = routes.params?.dataType;

  const [activeTab, setActiveTab] = useState('about');
  const [loading, setLoading] = useState(true);
  const [portData, setPortData] = useState<PortProfileData | null>(null);
  const [isVisited, setIsVisited] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const tabs = [
    { id: 'about', label: 'About' },
    { id: 'visitors', label: 'Visitors' },
    { id: 'scrapbook', label: 'Scrapbook' },
  ];

  useEffect(() => {
    if (!portUnLocode) return;

    setLoading(true);
    fetchPortProfile({
      unLocode: portUnLocode,
      dataType: shipDataType,
    })
      .then((data) => {
        setPortData(data as PortProfileData);
        setIsVisited((data as PortProfileData).isVisited);
      })
      .catch((error) => {
        triggerErrorBoundary(
          new Error(
            `Failed to load port profile: ${error instanceof Error ? error.message : 'Unknown error'}`,
          ),
        );
      })
      .finally(() => {
        setLoading(false);
      });
  }, [portUnLocode, shipDataType]);

  const tabComponents: { [key: string]: ReactNode } = {
    about: portData ? <PortAbout portData={portData} /> : null,
    visitors:
      portData?.unLocode && portData?.dataType ? (
        <PortVisitors unLocode={portData.unLocode} dataType={portData.dataType} />
      ) : null,
    scrapbook:
      portData?.unLocode && portData?.dataType ? (
        <PortScrapbook unLocode={portData.unLocode} />
      ) : null,
  };

  const handleVisitPort = async () => {
    if (!isVisited) {
      setIsVisited(true);
      await addPortVisitor({
        portUnLocode: portData?.unLocode!,
      });
    } else {
      setIsModalVisible(true);
    }
  };

  const handleRemoveVisit = async () => {
    setIsDeleting(true);
    await removePortVisitor({
      portUnLocode: portData?.unLocode!,
    });
    setIsDeleting(false);
    setIsModalVisible(false);
    setIsVisited(false);
  };

  if (loading) {
    return (
      <SafeArea>
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" color="#5a8d3b" />
        </View>
      </SafeArea>
    );
  }

  return (
    <SafeArea>
      <PortHeader
        label={portData?.name || 'Port'}
        imageUri={portData?.imageUrl || null}
        portId={portUnLocode}
        portDataType={portData?.dataType}
      />

      <View className="px-5 py-6">
        {isVisited ? (
          <Button
            variant="primaryOutline"
            label="You have visited this port"
            className="rounded-full"
            onPress={handleVisitPort}
            prefixIcon={<Tick color="green" width={1.5} height={1.5} />}
          />
        ) : (
          <Button variant="outline" label="I have visited this port" onPress={handleVisitPort} />
        )}
      </View>

      <View className="my-4">
        <Tabs
          styles="bg-white border-b border-gray-100"
          activeTab={activeTab}
          onTabChange={setActiveTab}
          tabs={tabs}
        />
      </View>
      {tabComponents[activeTab]}
      <CustomModal
        isVisible={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        onConfirm={handleRemoveVisit}
        title="Remove from visited ports"
        description="Are you sure you want to remove this port from your visited list? This action cannot be undone."
        cancelText="Cancel"
        confirmText="Remove"
        confirmButtonVariant="danger"
        isConfirming={isDeleting}
      />
    </SafeArea>
  );
};

export default PortScreen;
