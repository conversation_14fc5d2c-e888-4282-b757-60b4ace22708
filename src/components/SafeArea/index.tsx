/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { StatusBar, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import type { SafeAreaProps } from './types';

const SafeArea = ({ children }: SafeAreaProps) => {
  const { left, right, top } = useSafeAreaInsets();

  return (
    <View
      className="flex-1 bg-white"
      style={{
        paddingTop: top,
        paddingLeft: left,
        paddingRight: right,
      }}
    >
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />
      {children}
    </View>
  );
};

export default SafeArea;
