import CreateContentScreen from '@/src/screens/CreateContent';
import { withErrorBoundary } from '@/src/hocs/withErrorBoundary';
import { CreateStackParamsListI, StackScreenI } from '../../types';

const createErrorBoundaryScreen = (Component: any, title: string, subtitle: string) =>
  withErrorBoundary(Component, { title, subtitle });

const screenConfigs = [
  {
    name: 'CreateContent',
    component: CreateContentScreen,
    title: 'Content Creation Error',
    subtitle: 'Something went wrong while creating content. Please try again.',
  },
];

export const screens: StackScreenI<CreateStackParamsListI>[] = screenConfigs.map(
  ({ name, component, title, subtitle }) => ({
    name: name as keyof CreateStackParamsListI,
    component: createErrorBoundaryScreen(component, title, subtitle),
  }),
);
