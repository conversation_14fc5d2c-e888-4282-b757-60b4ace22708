/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React, { useEffect, useRef } from 'react';
import { Animated, Dimensions, Easing, StyleSheet, View } from 'react-native';
import { LinearGradient } from 'react-native-linear-gradient';
import { ShimmerBoxProps } from './types';

const ShimmerBox = ({
  width,
  height,
  borderRadius = 4,
  style,
  className,
  variant = 'rectangle',
}: ShimmerBoxProps) => {
  const shimmerAnim = useRef(new Animated.Value(0)).current;
  const screenWidth = Dimensions.get('window').width;

  useEffect(() => {
    const shimmerAnimation = Animated.loop(
      Animated.timing(shimmerAnim, {
        toValue: 1,
        duration: 1500,
        easing: Easing.linear,
        useNativeDriver: true,
      }),
    );

    shimmerAnimation.start();

    return () => {
      shimmerAnimation.stop();
    };
  }, []);

  const translateX = shimmerAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [-screenWidth, screenWidth],
  });

  const isCircle = variant === 'sphere';
  const size = (width as number) || (height as number) || 80;

  return (
    <View
      className={className}
      style={[
        {
          backgroundColor: '#E1E9EE',
          overflow: 'hidden',
          width: isCircle ? size : width,
          height: isCircle ? size : height,
          borderRadius: isCircle ? size / 2 : borderRadius,
        },
        style,
      ]}
    >
      <Animated.View
        style={[
          StyleSheet.absoluteFillObject,
          {
            transform: [{ translateX }],
          },
        ]}
      >
        <LinearGradient
          colors={['transparent', '#FFFFFF66', 'transparent']}
          start={{ x: 0, y: 0.5 }}
          end={{ x: 1, y: 0.5 }}
          style={{ flex: 1 }}
        />
      </Animated.View>
    </View>
  );
};

export default ShimmerBox;
