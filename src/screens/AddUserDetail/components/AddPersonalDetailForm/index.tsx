import { useEffect } from 'react';
import { Text, View } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import Button from '@/src/components/Button';
import EntitySearch from '@/src/components/EntitySearch';
import InfoModal from '@/src/components/InfoModal';
import ProgressBar from '@/src/components/Progress';
import Select from '@/src/components/Select';
import TextInput from '@/src/components/TextInput';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import type { PersonalDetailsFormDataI, PersonalDetailsPropsI } from './types';
import usePersonalDetails from './useHook';

const GENDER_OPTIONS = [
  { title: 'Male', id: 'm' },
  { title: 'Female', id: 'f' },
  { title: 'Other', id: 'o' },
  { title: 'Prefer Not to Say', id: 'n' },
];

const PersonalDetails = ({ onNext, onBack, initialData }: PersonalDetailsPropsI) => {
  const { methods, isSubmitting, onSubmit } = usePersonalDetails(onNext);
  const {
    control,
    handleSubmit,
    setValue,
    trigger,
    formState: { isValid },
  } = methods;

  const countrySelection = useSelector(selectSelectionByKey('country'));

  useEffect(() => {
    if (initialData) {
      Object.entries(initialData).forEach(([key, value]) => {
        setValue(key as keyof PersonalDetailsFormDataI, value);
      });
      trigger();
    }
  }, [initialData, setValue, trigger]);

  useFocusEffect(() => {
    if (countrySelection) {
      setValue('country', countrySelection);
      trigger('country');
    }
  });

  return (
    <View className="flex-1 bg-white">
      {onBack && <BackButton onBack={onBack} />}
      <View className="my-4">
        <View className="flex-row items-center justify-between">
          <Text className="text-xl font-bold leading-6">We need a few more details to proceed</Text>
        </View>
      </View>
      <View className="mt-2 flex flex-row items-center gap-4">
        <Text className="text-base font-medium leading-6">Personal details (1/2)</Text>
        <ProgressBar progress={50} />
      </View>
      <View className="mt-5 flex flex-col gap-4">
        <Controller
          control={control}
          name="fullName"
          rules={{
            required: 'Full name is required',
            validate: (value) => {
              value = value.replace(/[\u2018\u2019]/g, "'");
              if (!value) return 'Full name is required';

              if (value !== value.trim()) return 'Cannot start or end with spaces';

              if (value.length < 2) return 'Name too short (min 2 characters)';
              if (value.length > 50) return 'Name too long (max 50 characters)';

              if (/^[. ']/.test(value)) return 'Cannot start with a space, period, or apostrophe';
              if (/[. ']$/.test(value)) return 'Cannot end with a space, period, or apostrophe';

              if (/\.\./.test(value)) return 'Cannot have consecutive periods';
              if (/  /.test(value)) return 'Cannot have consecutive spaces';
              if (/\'\'/.test(value)) return 'Cannot have consecutive apostrophes';

              if (/^[. ']+$/.test(value))
                return 'Name cannot consist of only spaces, periods, or apostrophes';
              if (/[^A-Za-z .']/.test(value))
                return 'Only letters, spaces, periods, and apostrophes allowed';

              return true;
            },
          }}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <TextInput
              label="Full name"
              value={value}
              onChangeText={(val) => {
                onChange(val);
                trigger('fullName');
              }}
              placeholder="Enter full name"
              error={error?.message}
            />
          )}
        />
        <Controller
          control={control}
          name="gender"
          rules={{ required: 'Gender is required' }}
          render={({ field: { onChange, value, ref }, fieldState: { error } }) => (
            <Select
              ref={ref}
              label="Gender"
              options={GENDER_OPTIONS}
              value={value}
              onChange={(val) => {
                onChange(val);
                trigger('gender');
              }}
              placeholder="Select gender"
              error={error?.message}
            />
          )}
        />
        <Controller
          control={control}
          name="country"
          rules={{ required: 'Country is required' }}
          render={({ fieldState: { error } }) => (
            <EntitySearch
              placeholder="Search country"
              selectionKey="country"
              title="Country"
              data={countrySelection?.name || ''}
              error={error?.message}
            />
          )}
        />
      </View>
      <InfoModal type="country" />
      <View className="mt-8">
        <Button
          label="Next"
          onPress={handleSubmit(onSubmit)}
          variant={isValid ? 'primary' : 'tertiary'}
          disabled={!isValid || isSubmitting}
          loading={isSubmitting}
        />
      </View>
    </View>
  );
};

export default PersonalDetails;
