/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Controller } from 'react-hook-form';
import BackButton from '@/src/components/BackButton';
import Button from '@/src/components/Button';
import TextInput from '@/src/components/TextInput';
import TextView from '@/src/components/TextView';
import { emailRegex } from '@/src/consts/regEx';
import { AppStackParamListI } from '@/src/navigation/types';
import { ForgotPasswordFormProps } from './types';
import { useForgotPassword } from './useHook';

export function ForgotPasswordForm({ onBack }: ForgotPasswordFormProps) {
  const navigation = useNavigation<StackNavigationProp<AppStackParamListI>>();

  const { methods, isSubmitting, handleEmailSubmit } = useForgotPassword((email: string) => {
    navigation.navigate('VerifyPasswordReset', { email });
  });

  const {
    control,
    handleSubmit,
    formState: { isValid, errors },
  } = methods;

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <View className="px-5 gap-2">
          <BackButton onBack={onBack} />
          <View className="my-8">
            <TextView
              title="Forgot password"
              subtitle="Don't worry! Enter your email ID to get a verification code"
            />
          </View>
          <View>
            <Controller
              control={control}
              name="email"
              rules={{
                required: 'Email is required',
                pattern: {
                  value: emailRegex,
                  message: 'Invalid email address',
                },
              }}
              render={({ field: { onChange, value } }) => (
                <TextInput
                  label="Email ID"
                  value={value}
                  onChangeText={onChange}
                  placeholder="Email ID"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  error={errors.email?.message}
                />
              )}
            />
          </View>
          <View className="mt-8">
            <Button
              label="Send code"
              onPress={handleSubmit(handleEmailSubmit)}
              variant={isValid ? 'primary' : 'tertiary'}
              disabled={!isValid || isSubmitting}
              loading={isSubmitting}
            />
          </View>
        </View>
      </KeyboardAvoidingView>
    </TouchableWithoutFeedback>
  );
}
