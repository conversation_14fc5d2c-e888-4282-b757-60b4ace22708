import React from 'react';
import BottomSheet from '@/src/components/Bottomsheet/index';
import { OptionItem, OptionsMenu } from '@/src/components/OptionsMenu';
import TrashBin from '@/src/assets/svgs/TrashBin';
import type { ChatOptionsProps } from './types';

export const ChatOptions: React.FC<ChatOptionsProps> = ({ visible, onClose, onClearChat }) => {
  return (
    <BottomSheet visible={visible} onClose={onClose} height={120} onModalHide={() => {}}>
      <OptionsMenu>
        <OptionItem
          icon={<TrashBin stroke="#EF4444" strokeWidth={1.5} width={2} height={2} />}
          label="Clear Messages"
          textClassName="text-red-500"
          onPress={onClearChat}
        />
      </OptionsMenu>
    </BottomSheet>
  );
};
