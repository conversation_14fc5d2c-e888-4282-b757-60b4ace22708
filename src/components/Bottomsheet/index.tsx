import type React from 'react';
import { View } from 'react-native';
import Modal from 'react-native-modal';
import { BottomSheetProps } from './types';

const BottomSheet: React.FC<BottomSheetProps> = ({ visible, onClose, onModalHide, children }) => {
  return (
    <View>
      <Modal
        isVisible={visible}
        onBackdropPress={onClose}
        onBackButtonPress={onClose}
        onModalHide={onModalHide}
        style={{
          justifyContent: 'flex-end',
          margin: 0,
        }}
        animationIn="slideInUp"
        animationOut="slideOutDown"
        backdropOpacity={0.5}
        backdropTransitionOutTiming={1}
        backdropTransitionInTiming={250}
        backdropColor="#000"
        useNativeDriver={true}
        hideModalContentWhileAnimating={true}
      >
        {children}
      </Modal>
    </View>
  );
};

export default BottomSheet;
