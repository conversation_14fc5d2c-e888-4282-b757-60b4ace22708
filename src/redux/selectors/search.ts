/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../store';

export const selectSearchState = (state: RootState) => state.search;

export const selectAllSelections = createSelector(
  [selectSearchState],
  (searchState) => searchState.selections,
);

export const selectAllMultipleSelections = createSelector(
  [selectSearchState],
  (searchState) => searchState.multipleSelections,
);

export const selectSelectionByKey = (key: string) =>
  createSelector([selectAllSelections], (selections) => selections[key]);

export const selectMultipleSelectionsByKey = (key: string) =>
  createSelector([selectAllMultipleSelections], (multipleSelections) => multipleSelections[key]);
