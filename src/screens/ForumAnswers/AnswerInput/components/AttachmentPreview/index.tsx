import { View, Text, Image, Pressable } from 'react-native';
import TrashBin from '@/src/assets/svgs/TrashBin';
import { AttachmentPreviewPropsI, AttachmentI } from '../../types';
import { isImage } from '../../utils';

const AttachmentPreview = ({ attachments, onRemove }: AttachmentPreviewPropsI) => {
  const renderAttachmentThumbnail = (attachment: any, index: number) => {
    const _isImage = isImage(attachment.filename);
    const isPdf = attachment.type === 'application/pdf';
    const isText = attachment.type === 'text/plain';

    return (
      <View
        key={`attachment-${index}`}
        className="flex-row items-center p-3 bg-gray-50 rounded-lg mb-2"
      >
        <View className="mr-3">
          {_isImage ? (
            <Image
              source={{ uri: attachment.uri }}
              style={{ width: 40, height: 40, borderRadius: 4 }}
              resizeMode="cover"
            />
          ) : (
            <View className="w-10 h-10 bg-gray-200 rounded items-center justify-center">
              <Text className="text-xs font-medium text-gray-600">
                {isPdf ? 'PDF' : isText ? 'TXT' : 'FILE'}
              </Text>
            </View>
          )}
        </View>
        <View className="flex-1">
          <Text className="text-sm font-medium text-gray-900" numberOfLines={1}>
            {attachment.filename}
          </Text>
          <Text className="text-xs text-gray-500">{attachment.type}</Text>
        </View>
        <Pressable
          onPress={() => onRemove(index)}
          className="p-2"
          hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
        >
          <TrashBin color="#EF4444" width={2} height={2} />
        </Pressable>
      </View>
    );
  };

  return (
    <View className="mb-2">
      <Text className="text-base font-medium text-gray-900 mb-3">
        Attachments ({attachments.length}/5)
      </Text>
      {attachments.map((attachment: any, index: number) =>
        renderAttachmentThumbnail(attachment, index),
      )}
    </View>
  );
};

export default AttachmentPreview;
