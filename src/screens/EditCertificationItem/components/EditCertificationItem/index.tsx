import { useEffect, useRef, useState } from 'react';
import { ActivityIndicator, Pressable, ScrollView, Text, View } from 'react-native';
import { useNavigation, type NavigationAction } from '@react-navigation/native';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import Checkbox from '@/src/components/Checkbox';
import ChipInput from '@/src/components/ChipInput';
import DatePicker from '@/src/components/DatePicker';
import EntitySearch from '@/src/components/EntitySearch';
import CustomModal from '@/src/components/Modal';
import Select from '@/src/components/Select';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { validateDate } from '@/src/screens/EditEducationItem/components/utils';
import Download from '@/src/assets/svgs/Download';
import Pdf from '@/src/assets/svgs/Pdf';
import TrashBin from '@/src/assets/svgs/TrashBin';
import Upload from '@/src/assets/svgs/Upload';
import type { EditCertificationItemPropsI } from './types';
import { useEditCertificationItem } from './useHook';

export const EditCertificationItem = ({
  onBack,
  profileId,
  certificationId,
  type,
}: EditCertificationItemPropsI) => {
  const certificateCourseSelection = useSelector(selectSelectionByKey('certificate-course'));
  const providerSelection = useSelector(selectSelectionByKey('entity'));
  const {
    methods,
    typeOptions,
    isSubmitting,
    onSubmit,
    selectedFile,
    handleAttachment,
    localSkills,
    setLocalSkills,
    loading,
    handleDownload,
    handleRemoveFile,
    isFileRemoved,
    clearFields,
    isPresent,
    handlePresentCheckbox,
    isSubmitted,
    setIsSubmitted,
    hasChanges,
    isSkillLimitReached,
    MAX_SKILLS,
  } = useEditCertificationItem(profileId, certificationId, type);

  const [showDiscardModal, setShowDiscardModal] = useState(false);
  const {
    control,
    handleSubmit,
    watch,
    formState: { errors },
  } = methods;

  const navigation = useNavigation();
  const exitActionRef = useRef<NavigationAction | null>(null);

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', (e) => {
      if (hasChanges && !isSubmitting) {
        e.preventDefault();
        exitActionRef.current = e.data.action;
        setShowDiscardModal(true);
      }
    });

    return unsubscribe;
  }, [navigation, hasChanges, isSubmitting]);

  const handleDiscardConfirm = () => {
    setShowDiscardModal(false);
    setTimeout(() => {
      if (exitActionRef.current) {
        navigation.dispatch(exitActionRef.current);
      }
    }, 300);
  };

  const handleDiscardCancel = () => {
    setShowDiscardModal(false);
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName?.toLowerCase().split('.').pop();
    if (extension === 'pdf') {
      return <Pdf width={2} height={2} color="#DC2626" />;
    }
    return <Upload width={2} height={2} color="#6B7280" />;
  };

  const getFileTypeText = (fileName: string) => {
    const extension = fileName?.toLowerCase().split('.').pop();
    if (extension === 'pdf') {
      return 'PDF Document';
    }
    if (['jpg', 'jpeg', 'png'].includes(extension || '')) {
      return 'Image File';
    }
    return 'Document';
  };

  useEffect(() => {
    if (certificateCourseSelection) {
      methods.setValue('course', certificateCourseSelection, { shouldDirty: true });
    }
    if (providerSelection) {
      methods.setValue('provider', providerSelection, { shouldDirty: true });
    }
    return () => {
      clearFields();
    };
  }, [certificateCourseSelection, providerSelection, methods, clearFields]);

  const validFromDate = watch('validFrom');
  const validUntilDate = watch('validUntil');
  const selectedType = watch('type');
  const selectedId =
    typeOptions.find((item) => item.title === selectedType || item.id === selectedType)?.id || '';
  const selectionKey = `certificate-course/${selectedId}`;

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#448600" />
      </View>
    );
  }

  return (
    <ScrollView className="flex-1 bg-white" showsVerticalScrollIndicator={false}>
      <View className="px-4">
        <View className="flex-row items-center justify-between py-4">
          <BackButton
            onBack={onBack}
            label={certificationId ? 'Edit certification' : 'Create certification'}
          />
          <Pressable
            onPress={() => {
              setIsSubmitted(true);
              handleSubmit(onSubmit)();
            }}
            disabled={isSubmitting || !hasChanges}
          >
            <Text
              className={`text-lg font-medium ${isSubmitting || !hasChanges ? 'text-gray-400' : 'text-[#448600]'}`}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </Text>
          </Pressable>
        </View>
        <View className="mb-3">
          <Text className="mb-2 text-base font-medium">Certificate type</Text>
          <Controller
            control={control}
            name="type"
            rules={{ required: 'Certificate type is required' }}
            disabled={!!certificationId}
            render={({ field: { onChange, value } }) => (
              <Select
                options={typeOptions}
                value={value}
                onChange={(val) => {
                  onChange(val);
                  methods.setValue('type', val, { shouldDirty: true });
                }}
                placeholder="Select certificate type"
                error={errors.type?.message}
                disabled={!!certificationId}
              />
            )}
          />
        </View>
        <Controller
          control={control}
          name="course"
          rules={{ required: 'Course is required' }}
          render={({ fieldState: { error } }) => (
            <EntitySearch
              placeholder="Search course"
              selectionKey={selectionKey}
              title="Certificate Course"
              data={
                certificateCourseSelection
                  ? certificateCourseSelection.name
                  : methods.watch('course')?.name || ''
              }
              error={
                isSubmitted && !certificateCourseSelection && !methods.watch('course')?.id
                  ? 'Course is required'
                  : error?.message
              }
            />
          )}
        />
        <Controller
          control={control}
          name="provider"
          rules={{ required: 'Provider is required' }}
          render={({ fieldState: { error } }) => (
            <EntitySearch
              placeholder="Search provider"
              selectionKey="entity"
              title="Provider"
              data={
                providerSelection ? providerSelection.name : methods.watch('provider')?.name || ''
              }
              error={
                isSubmitted && !providerSelection && !methods.watch('provider')?.id
                  ? 'Provider is required'
                  : error?.message
              }
            />
          )}
        />
        <View className="flex-row mb-6">
          <View className="flex-1 mr-2">
            <Controller
              control={control}
              name="validFrom"
              rules={{
                required: 'Start date is required',
                validate: (value) => validateDate(value, validUntilDate),
              }}
              render={({ field: { onChange }, fieldState: { error } }) => (
                <>
                  <DatePicker
                    title="Valid from"
                    selectedDate={validFromDate}
                    onDateChange={(date) => {
                      if (date instanceof Date) {
                        onChange(date.toISOString().split('T')[0]);
                      }
                    }}
                    showMonthYear={true}
                    className={error ? 'border-red-500' : ''}
                  />
                  {error && <Text className="text-red-500 text-xs mt-1">{error.message}</Text>}
                </>
              )}
            />
          </View>
          <View className="flex-1 ml-2">
            <Controller
              control={control}
              name="validUntil"
              rules={{
                required: !isPresent
                  ? 'End date is required when "Unlimited" is not checked'
                  : false,
                validate: (value) => {
                  if (isPresent) return true;
                  if (!value) return 'End date is required';
                  return validateDate(validFromDate, value);
                },
              }}
              render={({ field: { onChange }, fieldState: { error } }) => (
                <>
                  <DatePicker
                    title="Valid until"
                    selectedDate={isPresent ? null : validUntilDate}
                    onDateChange={(date) => {
                      if (date instanceof Date && !isPresent) {
                        onChange(date.toISOString().split('T')[0]);
                      }
                    }}
                    showMonthYear={true}
                    disabled={isPresent}
                    className={error ? 'border-red-500' : ''}
                  />
                  {error && !isPresent && (
                    <Text className="text-red-500 text-xs mt-1">{error.message}</Text>
                  )}
                </>
              )}
            />
            <Checkbox
              label="Unlimited"
              className="pt-3"
              labelClassName="text-base text-sm"
              onValueChange={handlePresentCheckbox}
              checked={isPresent}
            />
          </View>
        </View>
        <View className="mb-6">
          <Text className="mb-3 text-base font-semibold text-gray-900">
            Certificate document (optional)
          </Text>
          {selectedFile || (methods.watch('documentUrl') && !isFileRemoved) ? (
            <View className="border border-dashed border-[#E5E5E5] rounded-xl p-6 bg-gray-50/30">
              <View className="flex-row items-center justify-between">
                <View className="flex-1 pr-4">
                  {selectedFile ? (
                    <View className="flex-row items-center">
                      <View className="mr-3">{getFileIcon(selectedFile)}</View>
                      <View className="flex-1">
                        <Text
                          className="text-sm font-semibold text-gray-900 mb-1"
                          numberOfLines={1}
                        >
                          {selectedFile}
                        </Text>
                        <Text className="text-xs text-gray-500">
                          {getFileTypeText(selectedFile)} • Uploaded
                        </Text>
                      </View>
                    </View>
                  ) : (
                    <View className="flex-row items-center">
                      <View className="mr-3">
                        <Pdf width={2} height={2} color="#DC2626" />
                      </View>
                      <View className="flex-1">
                        <Text className="text-sm font-semibold text-gray-900 mb-1">
                          Certificate uploaded
                        </Text>
                        <Text className="text-xs text-gray-500">Certificate file available</Text>
                      </View>
                    </View>
                  )}
                </View>
                <View className="flex-row items-center gap-x-2">
                  {methods.watch('documentUrl') && !selectedFile && (
                    <Pressable onPress={handleDownload} className="p-2 bg-gray-50 rounded-lg">
                      <Download width={2} height={2} color="#6B7280" />
                    </Pressable>
                  )}
                  <Pressable
                    onPress={handleAttachment}
                    className="p-2 bg-gray-50 rounded-lg active:bg-gray-100"
                  >
                    <Upload width={2} height={2} color="#6B7280" />
                  </Pressable>
                  <Pressable
                    onPress={handleRemoveFile}
                    className="p-2 bg-gray-50 rounded-lg active:bg-red-100"
                  >
                    <TrashBin width={2} height={2} color="#DC2626" />
                  </Pressable>
                </View>
              </View>
            </View>
          ) : isFileRemoved ? (
            <View className="border border-dashed border-red-300 rounded-xl p-6 bg-red-50/50">
              <View className="flex-row items-center justify-between">
                <View className="flex-1 pr-4">
                  <View className="flex-row items-center">
                    <View className="mr-3">
                      <TrashBin width={2} height={2} color="#DC2626" />
                    </View>
                    <View className="flex-1">
                      <Text className="text-sm font-medium text-red-700 mb-1">
                        File marked for removal
                      </Text>
                      <Text className="text-xs text-red-600">
                        This file will be permanently removed when you save
                      </Text>
                    </View>
                  </View>
                </View>
                <Pressable
                  onPress={handleAttachment}
                  className="p-2 bg-white rounded-lg shadow-sm active:bg-gray-50 border border-gray-200"
                >
                  <Upload width={2} height={2} color="#6B7280" />
                </Pressable>
              </View>
            </View>
          ) : (
            <Pressable
              className="border border-dashed border-[#E5E5E5] rounded-xl p-8 items-center bg-gray-50/20 active:bg-gray-50/40"
              onPress={handleAttachment}
            >
              <View className="w-12 h-12 bg-gray-100 rounded-full items-center justify-center mb-4">
                <Upload width={2} height={2} color="#6B7280" />
              </View>
              <Text className="text-base font-semibold text-gray-900 mb-2">Upload certificate</Text>
              <View className="items-center">
                <Text className="text-sm text-[#6B7280] text-center leading-5">
                  Supported file types:
                </Text>
                <Text className="text-sm text-center leading-5 mt-1">
                  <Text className="font-semibold text-[#4B5563]">PDF</Text>
                  <Text className="text-[#6B7280]"> (Max 4MB), </Text>
                  <Text className="font-semibold text-[#4B5563]">JPG</Text>
                  <Text className="text-[#6B7280]"> and </Text>
                  <Text className="font-semibold text-[#4B5563]">JPEG</Text>
                  <Text className="text-[#6B7280]"> (Max 500KB)</Text>
                </Text>
              </View>
            </Pressable>
          )}
        </View>
        <View className="mb-6">
          <ChipInput
            title="Skills"
            placeholder="Add a skill"
            chips={localSkills}
            onRemove={(id) => setLocalSkills((prev) => prev.filter((s) => s.id !== id))}
            disabled={isSkillLimitReached}
            error={isSkillLimitReached ? `Skill limit reached (max ${MAX_SKILLS})` : undefined}
          />
        </View>
        <CustomModal
          isVisible={showDiscardModal}
          title="Discard changes?"
          description="You have unsaved changes. Are you sure you want to discard them and leave this screen?"
          cancelText="Cancel"
          confirmText="Discard"
          onCancel={handleDiscardCancel}
          onConfirm={handleDiscardConfirm}
          confirmButtonVariant="danger"
        />
      </View>
    </ScrollView>
  );
};

export default EditCertificationItem;
