/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { Text, View } from 'react-native';
import { twMerge } from 'tailwind-merge';
import { TextViewProps } from './types';

const TextView = ({
  title,
  subtitle,
  titleClassName,
  subtitleClassName,
  className,
  numberOfLines,
  ellipsizeMode,
}: TextViewProps) => {
  if (!title && !subtitle) return null;

  return (
    <View className={twMerge('space-y-2', className)}>
      {title && (
        <Text
          className={twMerge(
            'text-2xl font-semibold tracking-tight text-[#0A0A0A] leading-8',
            titleClassName,
          )}
        >
          {title}
        </Text>
      )}
      {subtitle && (
        <Text
          numberOfLines={numberOfLines}
          ellipsizeMode={ellipsizeMode}
          className={twMerge('text-base text-[#525252] leading-5 mt-3', subtitleClassName)}
        >
          {subtitle}
        </Text>
      )}
    </View>
  );
};

export default TextView;
