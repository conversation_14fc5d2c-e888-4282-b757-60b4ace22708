/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { Text, View } from 'react-native';
import { VARIANTS } from './consts';
import { CustomToastProps } from './types';

const CustomToast = ({ text, description, variant = 'success' }: CustomToastProps) => {
  const styles = VARIANTS[variant];

  return (
    <View className={`w-[90%] mx-auto p-3 mb-4 rounded-md ${styles.container}`}>
      <Text className={styles.label}>{text}</Text>
      {description && <Text className={`${styles.label} opacity-80 text-sm`}>{description}</Text>}
    </View>
  );
};

export default CustomToast;
