import { useState } from 'react';
import { ActivityIndicator, FlatList, Pressable, ScrollView, Text, View } from 'react-native';
import Accordion from '@/src/components/Accordion';
import BackButton from '@/src/components/BackButton';
import CustomModal from '@/src/components/Modal';
import NotFound from '@/src/components/NotFound';
import TextView from '@/src/components/TextView';
import type { ExperienceFetchForClientResultI } from '@/src/redux/slices/experience/types';
import { getYearsMonths, formatDate } from '@/src/utilities/datetime';
import ShipDetails from '@/src/screens/UserProfile/components/Experience/components/ShipDetails';
import AddItem from '@/src/assets/svgs/AddItem';
import EditPencil from '@/src/assets/svgs/EditPencil';
import DeleteIcon from '@/src/assets/svgs/TrashBin';
import type { EditExperienceListPropsI } from './types';
import useEditExperienceList from './useHook';

const ExperienceContent = ({ data }: { data: ExperienceFetchForClientResultI }) => {
  return data.designations?.length ? (
    <>
      <View>
        {data?.designations?.map((item, index) => {
          if (!data?.designations) return;
          const isLast = index === data?.designations.length - 1;
          return (
            <View key={index} className="mt-3">
              <View className="flex-row items-center">
                <View className="flex-row">
                  <View className="flex-col items-center">
                    <View className="h-2 w-2 rounded-full bg-labelGray mb-1" />
                    <View className={`flex-1 w-px ${isLast ? '' : 'bg-borderGray'}`} />
                  </View>

                  <View className="ml-3 flex-1 gap-1.5">
                    <Text className="font-semibold text-sm leading-4">
                      {item?.designation?.name}
                    </Text>
                    <Text className="text-subLabelGrayDark text-sm leading-4">
                      {item.fromDate ? formatDate(item.fromDate, 'MMMM, YYYY') : ''} -{' '}
                      {item.toDate ? formatDate(item.toDate, 'MMMM, YYYY') : 'Present'}
                    </Text>
                    {item?.ships?.map((ship, shipIndex) => (
                      <ShipDetails key={shipIndex} ship={ship} />
                    ))}
                  </View>
                </View>
              </View>
            </View>
          );
        })}
      </View>
    </>
  ) : (
    <></>
  );
};

const CompanyExperience = ({
  data,
  isLast,
  isFirst,
}: {
  data: ExperienceFetchForClientResultI;
  isLast: boolean;
  isFirst: boolean;
}) => {
  return data ? (
    <Accordion
      title={data.entity.name}
      subTitle={getYearsMonths(data?.years as number, data?.months as number)}
      isLast={isLast}
      content={<ExperienceContent data={data} />}
      defaultExpanded={isFirst}
    />
  ) : (
    <></>
  );
};

const EditExperienceList = ({ profileId, onBack, editable }: EditExperienceListPropsI) => {
  const {
    isLoading,
    loadingMore,
    handleLoadMore,
    isModalVisible,
    handleModal,
    experiences,
    onAddExperience,
    onEditExperience,
    onDeleteExperience,
  } = useEditExperienceList(profileId);
  const [experienceId, setExperienceId] = useState<null | string>(null);

  const handleDelete = (id: string) => {
    handleModal();
    setExperienceId(id);
  };

  const handleConfirm = () => {
    onDeleteExperience(experienceId as string);
  };

  const renderExperienceItem = ({
    item,
    index,
  }: {
    item: ExperienceFetchForClientResultI;
    index: number;
  }) => {
    const duration = getYearsMonths(item.years, item.months);
    return (
      <View
        className={`${index === experiences.length - 1 ? `` : `border-b border-[#D4D4D4]`} mx-2 p-4`}
      >
        <View className="flex-row flex-wrap justify-between items-center">
          <View className="w-3/4">
            <TextView
              title={item.entity.name}
              subtitle={duration}
              subtitleClassName="font-medium leading-22 text-[#000000] text-sm"
              titleClassName="text-lg"
              ellipsizeMode="tail"
              numberOfLines={1}
            />
          </View>
          <View className="flex-row">
            <Pressable onPress={() => onEditExperience(item.id)} className="p-2">
              <EditPencil width={2.3} height={2.3} />
            </Pressable>
            <Pressable onPress={() => handleDelete(item.id)} className="p-2">
              <DeleteIcon width={2.3} height={2.3} />
            </Pressable>
          </View>
        </View>
      </View>
    );
  };

  const renderFooter = () =>
    loadingMore ? (
      <View className="py-4 items-center">
        <ActivityIndicator size="small" color="#448600" />
      </View>
    ) : null;

  if (isLoading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#448600" />
      </View>
    );
  }

  if (editable) {
    return (
      <>
        <View className="flex-1">
          <View className="flex-row items-center justify-between px-4">
            <BackButton onBack={onBack} label="Edit Experiences" />
            <View>
              <Pressable onPress={onAddExperience}>
                <AddItem width={4} height={4} />
              </Pressable>
            </View>
          </View>

          {experiences.length > 0 ? (
            <FlatList
              data={experiences}
              renderItem={({ item, index }) => renderExperienceItem({ item, index })}
              keyExtractor={(item) => item.id}
              onEndReached={handleLoadMore}
              onEndReachedThreshold={0.3}
              ListFooterComponent={renderFooter}
              className="mt-5"
              contentContainerStyle={{
                flexGrow: 1,
                backgroundColor: 'white',
                paddingVertical: 20,
              }}
            />
          ) : (
            <NotFound imageStyle={{ height: 120, width: 120 }} />
          )}
        </View>
        <View>
          <CustomModal
            isVisible={isModalVisible}
            title="Confirm Action"
            description={'Are you sure you want to delete?'}
            cancelText="Cancel"
            confirmText="Confirm"
            confirmButtonVariant="danger"
            onConfirm={handleConfirm}
            onCancel={handleModal}
            isConfirming={isLoading}
          />
        </View>
      </>
    );
  }

  return (
    <ScrollView
      className="flex-1 px-4"
      contentContainerStyle={{ flexGrow: 1, paddingBottom: 20 }}
      showsVerticalScrollIndicator={false}
    >
      <BackButton onBack={onBack} label="View Experiences" />
      {experiences.length > 0 ? (
        <View className="mt-5 flex-1">
          {experiences.map((experience, index) => (
            <CompanyExperience
              key={experience.id}
              data={experience}
              isLast={index === experiences.length - 1}
              isFirst={index === 0}
            />
          ))}
        </View>
      ) : (
        <NotFound imageStyle={{ height: 120, width: 120 }} />
      )}
    </ScrollView>
  );
};

export default EditExperienceList;
