/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { StackNavigationProp } from '@react-navigation/stack';
import { AppDispatch } from '@/src/redux/store';
import { AppStackParamListI, BottomTabNavigationI } from '@/src/navigation/types';

export interface SearchResultI {
  id: string;
  name: string;
  dataType: 'raw' | 'master';
}

export interface OptionPropsI {
  item: SearchResultI;
  select: (item: SearchResultI) => void;
}

export interface HeaderComponentPropsI {
  handleAdd: () => void;
  text: string | null;
  title: string;
  navigation: BottomTabNavigationI;
  dispatch: AppDispatch;
  selectionKey: string;
  multipleSelection: boolean;
  selectedOptions: SearchResultI[];
  deSelect: (itemId: string) => void;
}

export interface SearchScreenProps {
  title: string;
  placeholder: string;
  selectionKey: string;
  onBack?: () => void;
  multipleSelection: boolean;
  searchWithoutInput?: boolean;
  showDoneButton?: boolean;
}

export type SearchResponseI = {
  data: SearchResultI[];
  total: number;
};
