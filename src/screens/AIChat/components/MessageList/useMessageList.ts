import type { Message } from '@/src/redux/slices/aichat/types';
import { MessageGroup } from './types';

export const useMessageList = (messages: Message[]) => {
  const getDateLabel = (timestamp: string): string => {
    const messageDate = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const messageDateOnly = new Date(
      messageDate.getFullYear(),
      messageDate.getMonth(),
      messageDate.getDate(),
    );
    const todayOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const yesterdayOnly = new Date(
      yesterday.getFullYear(),
      yesterday.getMonth(),
      yesterday.getDate(),
    );

    if (messageDateOnly.getTime() === todayOnly.getTime()) {
      return 'Today';
    } else if (messageDateOnly.getTime() === yesterdayOnly.getTime()) {
      return 'Yesterday';
    } else {
      return messageDate.toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'short',
        day: 'numeric',
      });
    }
  };

  const groups: { [key: string]: Message[] } = {};

  const sortedMessages = [...messages].sort(
    (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime(),
  );

  sortedMessages.forEach((message) => {
    const dateLabel = getDateLabel(message.timestamp);
    if (!groups[dateLabel]) {
      groups[dateLabel] = [];
    }
    groups[dateLabel].push(message);
  });

  const messageGroups: MessageGroup[] = Object.entries(groups)
    .sort(([, messagesA], [, messagesB]) => {
      const earliestA = Math.min(...messagesA.map((m) => new Date(m.timestamp).getTime()));
      const earliestB = Math.min(...messagesB.map((m) => new Date(m.timestamp).getTime()));
      return earliestA - earliestB;
    })
    .map(([dateLabel, messages]) => ({ dateLabel, messages }));

  return { messageGroups };
};
