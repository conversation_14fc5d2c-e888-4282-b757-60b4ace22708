/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState, useEffect, useCallback } from 'react';
import { ListItem } from '@/src/components/UsersList/types';
import { UseBlockedListPropsI, BlockedListHookResultI, ForumBlockedUserI } from './types';

const mockBlockedUsers: ForumBlockedUserI[] = [
  {
    id: '1',
    Profile: {
      id: '1',
      avatar:
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face',
      name: '<PERSON>ed',
      designation: {
        id: '1',
        name: 'Marine Engineer',
      },
      entity: {
        id: '1',
        name: 'Blocked Corp',
      },
    },
    blockedAt: '2024-03-15',
    reason: 'Inappropriate behavior',
  },
  {
    id: '2',
    Profile: {
      id: '2',
      avatar:
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face',
      name: '<PERSON>',
      designation: {
        id: '2',
        name: 'Deck Officer',
      },
      entity: {
        id: '2',
        name: 'Spam Industries',
      },
    },
    blockedAt: '2024-03-10',
    reason: 'Spam posting',
  },
];

const useBlockedList = ({
  forumId,
  pageSize = 20,
}: UseBlockedListPropsI): BlockedListHookResultI => {
  const [data, setData] = useState<ListItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [page, setPage] = useState<number>(1);
  const [isUnblocking, setIsUnblocking] = useState<Record<string, boolean>>({});

  const transformBlockedUsersToListItems = (users: ForumBlockedUserI[]): ListItem[] => {
    return users.map((user) => ({
      Profile: user.Profile,
      status: 'BLOCKED',
    })) as ListItem[];
  };

  const fetchBlockedUsers = useCallback(
    async (pageNum: number, isRefresh = false) => {
      try {
        if (isRefresh) {
          setRefreshing(true);
        } else {
          setLoading(true);
        }

        await new Promise((resolve) => setTimeout(resolve, 1000));

        const startIndex = (pageNum - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const paginatedUsers = mockBlockedUsers.slice(startIndex, endIndex);

        const transformedData = transformBlockedUsersToListItems(paginatedUsers);

        if (isRefresh || pageNum === 1) {
          setData(transformedData);
        } else {
          setData((prev) => [...prev, ...transformedData]);
        }

        setHasMore(endIndex < mockBlockedUsers.length);
        setError(null);
      } catch (err) {
        setError(err as Error);
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    },
    [pageSize],
  );

  const handleUnblock = useCallback(async (profileId: string) => {
    setIsUnblocking((prev) => ({ ...prev, [profileId]: true }));

    try {
      await new Promise((resolve) => setTimeout(resolve, 1500));

      setData((prev) => prev.filter((item) => item.Profile.id !== profileId));
    } catch (err) {
      console.error('Failed to unblock user:', err);
      setError(err as Error);
    } finally {
      setIsUnblocking((prev) => ({ ...prev, [profileId]: false }));
    }
  }, []);

  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchBlockedUsers(nextPage);
    }
  }, [loading, hasMore, page, fetchBlockedUsers]);

  const refresh = useCallback(() => {
    setPage(1);
    fetchBlockedUsers(1, true);
  }, [fetchBlockedUsers]);

  useEffect(() => {
    fetchBlockedUsers(1);
  }, [fetchBlockedUsers]);

  return {
    data,
    loading,
    refreshing,
    hasMore,
    error,
    loadMore,
    refresh,
    handleUnblock,
    isUnblocking,
  };
};
export default useBlockedList;
