/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const Tick: React.FC<FilledIconPropsI> = ({ width = 2.48, height = 1.736, color, ...props }) => {
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 20 14"
      fill="none"
      {...props}
    >
      <Path
        d="M19.031 1.751l-12 12a.751.751 0 01-1.061 0L.72 8.501A.75.75 0 011.78 7.44l4.72 4.72L17.97.69a.75.75 0 111.061 1.061z"
        fill={color || '#448600'}
      />
    </Svg>
  );
};

export default Tick;
