/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ProfileForDataI } from '@/src/networks/question/types';
import { AttachmentI } from '../../Forum/components/ForumPost/types';

export type ForumAnswerProps = {
  answerId: string;
  postId: string;
  canModify: boolean;
  profile: ProfileForDataI;
  content: string;
  answerVerified: boolean;
  status: string;
  upVotes: number;
  downVotes: number;
  comments: number;
  commentView: boolean;
  userVote?: 'UPVOTE' | 'DOWNVOTE' | null;
  canUpdateStatus: boolean;
  attachments?: AttachmentI[];
};
