import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { showToast } from '@/src/utilities/toast';
import { sendConnectionRequestAPI, deleteConnectionAPI } from '@/src/networks/connect/connection';
import { respondReceivedRequestAPI } from '@/src/networks/connect/request';
import { fetchPortVisitors } from '@/src/networks/port/visitor';
import type {
  FetchVisitorsResult,
  UsePortVisitorsParams,
  UsePortVisitorsResult,
  FetchVisitorsParams,
  RequestStatusTypeE,
} from './types';

const usePortVisitors = ({
  unLocode,
  dataType,
  pageSize = 10,
  onError,
}: UsePortVisitorsParams): UsePortVisitorsResult => {
  const currentUser = useSelector(selectCurrentUser);

  const [visitors, setVisitors] = useState<FetchVisitorsResult>({
    total: 0,
    data: [],
  });
  const [loading, setLoading] = useState<boolean>(true);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(false);
  const [page, setPage] = useState<number>(0);
  const [connectionStates, setConnectionStates] = useState<
    Record<string, 'idle' | 'loading' | 'success'>
  >({});

  const fetchVisitors = async (pageNumber: number, isRefresh = false): Promise<void> => {
    try {
      if (isRefresh) {
        setRefreshing(true);
        setPage(0);
      } else if (pageNumber > 0) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }

      const params: FetchVisitorsParams = {
        unLocode,
        dataType,
        page: pageNumber,
        pageSize,
      };

      const result = await fetchPortVisitors({ ...params });

      if (isRefresh || pageNumber === 0) {
        setVisitors(result);
      } else {
        setVisitors((prev) => ({
          total: result.total,
          data: [...prev.data, ...result.data],
        }));
      }

      setPage(pageNumber);
      setHasMore(result.data.length === pageSize);
    } catch (err) {
      const errorMessage = `Failed to load port visitors: ${err instanceof Error ? err.message : 'Unknown error'}`;
      if (onError) {
        onError(new Error(errorMessage));
      } else {
        showToast({
          type: 'error',
          message: errorMessage,
        });
      }
    } finally {
      setLoading(false);
      setLoadingMore(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    if (unLocode && dataType) {
      fetchVisitors(0);
    }
  }, [unLocode, dataType]);

  const handleRefresh = (): void => {
    fetchVisitors(0, true);
  };

  const handleLoadMore = (): void => {
    if (!loadingMore && !loading && hasMore) {
      fetchVisitors(page + 1);
    }
  };

  const handleConnectRequest = async (profileId: string): Promise<void> => {
    try {
      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'loading',
      }));

      await sendConnectionRequestAPI({
        receiverProfileId: profileId,
        requestedStatus: 'PENDING',
      });

      setVisitors((prevVisitors) => ({
        ...prevVisitors,
        data: prevVisitors.data.map((visitor) =>
          visitor.id === profileId
            ? {
                ...visitor,
                requestStatus: 'PENDING' as RequestStatusTypeE,
              }
            : visitor,
        ),
      }));

      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'success',
      }));
    } catch (error) {
      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'idle',
      }));
      const errorMessage = `Failed to send connection request: ${error instanceof Error ? error.message : 'Unknown error'}`;
      if (onError) {
        onError(new Error(errorMessage));
      }
    }
  };

  const handleAcceptRequest = async (profileId: string): Promise<void> => {
    try {
      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'loading',
      }));

      await respondReceivedRequestAPI({
        senderProfileId: profileId,
        requestedStatus: 'ACCEPTED',
      });

      setVisitors((prevVisitors) => ({
        ...prevVisitors,
        data: prevVisitors.data.map((visitor) =>
          visitor.id === profileId
            ? {
                ...visitor,
                isConnected: true,
                requestStatus: 'ACCEPTED' as RequestStatusTypeE,
              }
            : visitor,
        ),
      }));

      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'success',
      }));
    } catch (error) {
      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'idle',
      }));
      const errorMessage = `Failed to accept connection request: ${error instanceof Error ? error.message : 'Unknown error'}`;
      if (onError) {
        onError(new Error(errorMessage));
      }
    }
  };

  const handleRejectRequest = async (profileId: string): Promise<void> => {
    try {
      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'loading',
      }));

      await respondReceivedRequestAPI({
        senderProfileId: profileId,
        requestedStatus: 'REJECTED',
      });

      setVisitors((prevVisitors) => ({
        ...prevVisitors,
        data: prevVisitors.data.map((visitor) =>
          visitor.id === profileId
            ? {
                ...visitor,
                requestStatus: 'REJECTED' as RequestStatusTypeE,
              }
            : visitor,
        ),
      }));

      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'success',
      }));
    } catch (error) {
      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'idle',
      }));
      const errorMessage = `Failed to reject connection request: ${error instanceof Error ? error.message : 'Unknown error'}`;
      if (onError) {
        onError(new Error(errorMessage));
      }
    }
  };

  const handleRevokeRequest = async (profileId: string): Promise<void> => {
    try {
      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'loading',
      }));

      await sendConnectionRequestAPI({
        receiverProfileId: profileId,
        requestedStatus: 'REVOKED',
      });

      setVisitors((prevVisitors) => ({
        ...prevVisitors,
        data: prevVisitors.data.map((visitor) =>
          visitor.id === profileId
            ? {
                ...visitor,
                requestStatus: 'REVOKED' as RequestStatusTypeE,
                isConnected: false,
              }
            : visitor,
        ),
      }));

      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'success',
      }));
    } catch (error) {
      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'idle',
      }));
      const errorMessage = `Failed to revoke connection request: ${error instanceof Error ? error.message : 'Unknown error'}`;
      if (onError) {
        onError(new Error(errorMessage));
      }
    }
  };

  const handleDisconnectRequest = async (profileId: string): Promise<void> => {
    try {
      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'loading',
      }));

      await deleteConnectionAPI({ profileId: profileId });

      setVisitors((prevVisitors) => ({
        ...prevVisitors,
        data: prevVisitors.data.map((visitor) =>
          visitor.id === profileId
            ? {
                ...visitor,
                requestStatus: 'DISCONNECTED' as RequestStatusTypeE,
                isConnected: false,
              }
            : visitor,
        ),
      }));

      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'success',
      }));
    } catch (error) {
      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'idle',
      }));
      const errorMessage = `Failed to disconnect: ${error instanceof Error ? error.message : 'Unknown error'}`;
      if (onError) {
        onError(new Error(errorMessage));
      }
    }
  };

  return {
    visitors,
    loading,
    loadingMore,
    hasMore,
    refreshing,
    connectionStates,
    handleRefresh,
    handleLoadMore,
    handleConnectRequest,
    handleAcceptRequest,
    handleRejectRequest,
    handleRevokeRequest,
    handleDisconnectRequest,
  };
};

export default usePortVisitors;
