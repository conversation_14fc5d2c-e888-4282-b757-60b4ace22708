name: 'Upload to App Store'
description: 'Uploads an iOS app to App Store'

inputs:
  app-store-connect-api-key-id:
    description: 'App Store Connect API Key ID'
    required: true
  app-store-connect-api-issuer-id:
    description: 'App Store Connect API Issuer ID'
    required: true
  app-store-connect-api-key-content:
    description: 'App Store Connect API Key Content'
    required: true
  bundle-id:
    description: 'App Bundle ID'
    required: true
  ipa-path:
    description: 'Path to the IPA file'
    required: true
  track:
    description: 'Release track (appstore, testflight)'
    required: false
    default: 'testflight'

runs:
  using: 'composite'
  steps:
    - name: 🧪 Debug Environment
      run: |
        echo "💻 macOS Version:"
        sw_vers
        echo "🛠️ Xcode Version:"
        xcodebuild -version
        echo "📤 iTMSTransporter Version:"
        which iTMSTransporter || echo "⚠️ iTMSTransporter not found in PATH"
        echo "📍 altool location:"
        which xcrun && xcrun --find altool
      shell: bash

    - name: 🔐 Set up API Key
      run: |
        mkdir -p ~/.appstoreconnect/private_keys/
        chmod 700 ~/.appstoreconnect/private_keys/

        echo "📄 Writing API key to file..."
        echo "${{ inputs.app-store-connect-api-key-content }}" > ~/.appstoreconnect/private_keys/AuthKey_${{ inputs.app-store-connect-api-key-id }}.p8
        chmod 600 ~/.appstoreconnect/private_keys/AuthKey_${{ inputs.app-store-connect-api-key-id }}.p8

        echo "🔍 API Key file permissions:"
        ls -la ~/.appstoreconnect/private_keys/
        echo "📏 API Key file size (bytes):"
        wc -c ~/.appstoreconnect/private_keys/AuthKey_${{ inputs.app-store-connect-api-key-id }}.p8

        echo "🧪 API Key format check:"
        grep -q "BEGIN PRIVATE KEY" ~/.appstoreconnect/private_keys/AuthKey_${{ inputs.app-store-connect-api-key-id }}.p8 && \
        grep -q "END PRIVATE KEY" ~/.appstoreconnect/private_keys/AuthKey_${{ inputs.app-store-connect-api-key-id }}.p8 && \
        echo "✅ Key appears to have proper BEGIN/END markers" || \
        echo "❌ Key is missing proper BEGIN/END markers!"

        echo "🆔 Key ID: ${{ inputs.app-store-connect-api-key-id }}"
        echo "🧾 Issuer ID: ${{ inputs.app-store-connect-api-issuer-id }}"
      shell: bash

    - name: 📦 Verify IPA file
      run: |
        if [ ! -f "${{ inputs.ipa-path }}" ]; then
          echo "❌ ERROR: IPA file not found at ${{ inputs.ipa-path }}"
          echo "📂 Current directory contents:"
          ls -la
          echo "📂 Parent directory contents:"
          ls -la ..
          echo "🔎 Looking for .ipa files:"
          find . -name "*.ipa" -type f
          exit 1
        fi

        echo "✅ IPA file exists:"
        ls -lh "${{ inputs.ipa-path }}"

        echo "📝 IPA file details:"
        file "${{ inputs.ipa-path }}"

        echo "🆔 Bundle ID from inputs: ${{ inputs.bundle-id }}"
      shell: bash

    - name: 🧪 Upload to TestFlight
      if: inputs.track == 'testflight'
      run: |
        xcrun altool --upload-app -f "${{ inputs.ipa-path }}" \
          --type ios \
          --apiKeyPath ~/.appstoreconnect/private_keys/AuthKey_${{ inputs.app-store-connect-api-key-id }}.p8 \
          --apiKey "${{ inputs.app-store-connect-api-key-id }}" \
          --apiIssuer "${{ inputs.app-store-connect-api-issuer-id }}" \
          --verbose
      shell: bash

    - name: 🛒 Upload to App Store
      if: inputs.track == 'appstore'
      run: |
        xcrun altool --upload-app -f "${{ inputs.ipa-path }}" \
          --type ios \
          --apiKeyPath ~/.appstoreconnect/private_keys/AuthKey_${{ inputs.app-store-connect-api-key-id }}.p8 \
          --apiKey "${{ inputs.app-store-connect-api-key-id }}" \
          --apiIssuer "${{ inputs.app-store-connect-api-issuer-id }}" \
          --verbose
      shell: bash
