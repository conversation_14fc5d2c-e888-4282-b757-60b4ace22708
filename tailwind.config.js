/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
module.exports = {
  content: ['./App.{js,jsx,ts,tsx}', './src/**/*.{js,jsx,ts,tsx}'],
  presets: [require('nativewind/preset')],
  theme: {
    extend: {
      boxShadow: {
        banner: '0px 4px 4px rgba(0, 0, 0, 0.05)',
      },
      fontSize: {
        xs: '12px',
        sm: '14px',
        base: '16px',
        lg: '18px',
        xl: '20px',
        '2xl': '24px',
        '2.5xl': '28px',
        '3xl': '30px',
        '4xl': '36px',
        '5xl': '48px',
        '6xl': '60px',
        '7xl': '72px',
        '8xl': '96px',
        '9xl': '128px',
      },
      colors: {
        labelBlack: '#262626',
        labelBlackExtra: '#171717',
        labelGray: '#737373',
        tableHeaderBlack: '#0A0A0A',
        subLabelGray: '#525252',
        subLabelGrayDark: '#404040',
        subLabelGrayLight: '#525252',
        subLabelGrayMedium: '##5D5D5D',
        subLabelGrayNormal: '#8A8A8A',
        borderLight: '#DEDEDE',
        borderGray: '#A3A3A3',
        borderGrayLight: '#D4D4D4',
        borderGrayExtraLight: '#E5E5E5',
        backgroundGray: '#F5F5F5',
        toggleGray: '#DBDBDB',
        primaryGreen: '#448600',
        whitePink: '#F3ECEC',
        chipGray: '#9E9E9E',
      },
    },
  },
  plugins: [require('@tailwindcss/typography'), require('@tailwindcss/forms')],
};
