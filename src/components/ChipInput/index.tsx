import React from 'react';
import { useRef, useState } from 'react';
import { Text, TextInput, View } from 'react-native';
import Chip from '@/src/components/Chip';
import { IdNameI } from '@/src/types/common/data';
import EntitySearch from '../EntitySearch';
import { ChipInputProps } from './types';

const ChipInput: React.FC<ChipInputProps> = ({
  title,
  placeholder = 'Add new item',
  chips = [],
  onRemove,
  onAdd,
  className = '',
  maxLength,
  disabled = false,
  error,
  titleClassName,
  removable = true,
  showTitle = true,
  showBorder = true,
  showDoneButton = false,
}) => {
  const [inputValue, setInputValue] = useState<string>('');
  const inputRef = useRef<TextInput>(null);

  const handleAddChip = () => {
    if (inputValue.trim() && onAdd) {
      onAdd(inputValue.trim());
      setInputValue('');
      inputRef.current?.focus();
    }
  };

  const handleRemoveChip = (chip: IdNameI) => {
    if (onRemove) {
      onRemove(chip.id);
    }
  };

  const renderField = () => {
    switch (title?.toLowerCase()) {
      case 'skills':
        return (
          <EntitySearch
            title=""
            placeholder="Add a skill"
            selectionKey="skill"
            multipleSelection={true}
            className="mt-1"
          />
        );
      case 'topics':
        return (
          <EntitySearch
            title=""
            placeholder="Add a topic"
            selectionKey="topic"
            multipleSelection={true}
            className="mt-1"
            showDoneButton={showDoneButton}
          />
        );
      case 'filter topics':
        return (
          <EntitySearch
            title=""
            placeholder="Add a topic"
            selectionKey="filterTopic"
            multipleSelection={true}
            className="mt-1"
          />
        );
      case 'fuel type':
        return (
          <EntitySearch
            title=""
            placeholder="Add a type"
            selectionKey="fuelType"
            multipleSelection={true}
            className="mt-1"
          />
        );
      default:
        return (
          <View className="flex-row mt-3">
            <TextInput
              ref={inputRef}
              className="flex-1 border border-gray-200 rounded-full py-3.5 px-4 text-base bg-gray-50"
              placeholder={placeholder}
              value={inputValue}
              onChangeText={setInputValue}
              onSubmitEditing={handleAddChip}
              maxLength={maxLength}
              editable={!disabled}
            />
          </View>
        );
    }
  };

  return (
    <View className={`mb-5 ${className}`}>
      {showTitle && title && (
        <Text className={`text-base font-semibold text-gray-800 mb-2.5 ${titleClassName}`}>
          {title}
        </Text>
      )}
      <View
        className={`rounded-xl ${
          showBorder ? 'border border-gray-200 p-4' : ''
        } ${error ? 'border-red-500' : 'bg-white'} ${disabled ? 'bg-gray-50' : 'bg-white'}`}
      >
        <View className="flex-row flex-wrap gap-2.5 mb-2">
          {chips
            .filter((chip, index, self) => index === self.findIndex((c) => c.id === chip.id))
            .map((chip) => (
              <Chip
                key={chip.id}
                label={chip.name}
                onRemove={() => handleRemoveChip(chip)}
                removable={removable}
                className="my-1.5 mx-0.5"
              />
            ))}
        </View>
        {!disabled && renderField()}
        {error && <Text className="text-red-500 mt-2 text-sm font-medium">{error}</Text>}
      </View>
    </View>
  );
};

export default ChipInput;
