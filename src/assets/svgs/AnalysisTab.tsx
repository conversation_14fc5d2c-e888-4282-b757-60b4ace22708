/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const AnalysisTab: React.FC<FilledIconPropsI> = ({
  width = 3.1,
  height = 3.1,
  fill = 'black',
  color,
  accessibilityLabel = 'Analysis Tab',
  disabled,
  ...props
}) => {
  const fillColor = color || fill;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 25 25"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        d="M24.625 12.5C24.625 12.7652 24.5196 13.0196 24.3321 13.2071C24.1446 13.3946 23.8902 13.5 23.625 13.5H13.625V23.5C13.625 23.7652 13.5196 24.0196 13.3321 24.2071C13.1446 24.3946 12.8902 24.5 12.625 24.5C12.3598 24.5 12.1054 24.3946 11.9179 24.2071C11.7304 24.0196 11.625 23.7652 11.625 23.5V13.5H1.625C1.35978 13.5 1.10543 13.3946 0.917893 13.2071C0.730357 13.0196 0.625 12.7652 0.625 12.5C0.625 12.2348 0.730357 11.9804 0.917893 11.7929C1.10543 11.6054 1.35978 11.5 1.625 11.5H11.625V1.5C11.625 1.23478 11.7304 0.98043 11.9179 0.792893C12.1054 0.605357 12.3598 0.5 12.625 0.5C12.8902 0.5 13.1446 0.605357 13.3321 0.792893C13.5196 0.98043 13.625 1.23478 13.625 1.5V11.5H23.625C23.8902 11.5 24.1446 11.6054 24.3321 11.7929C24.5196 11.9804 24.625 12.2348 24.625 12.5Z"
        fill={fillColor}
      />
    </Svg>
  );
};

export default AnalysisTab;
