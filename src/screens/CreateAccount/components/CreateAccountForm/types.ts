import type React from 'react';
import type { UseFormReturn } from 'react-hook-form';

export interface CreateAccountFormDataI {
  email: string;
  password: string;
  confirmPassword: string;
  acceptedTerms: boolean;
}

export interface UseCreateAccountFormI {
  methods: UseFormReturn<CreateAccountFormDataI>;
  isSubmitting: boolean;
  onSubmit: (data: CreateAccountFormDataI) => Promise<void>;
  showPrivacyModal: boolean;
  setShowPrivacyModal: React.Dispatch<React.SetStateAction<boolean>>;
  showTermsModal: boolean;
  setShowTermsModal: React.Dispatch<React.SetStateAction<boolean>>;
  handleGoogleSignUp: () => Promise<void>;
  handleAppleSignUp: () => Promise<void>;
  isGoogleSubmitting: boolean;
  isAppleSubmitting: boolean;
}

export interface CreateAccountPropsI {
  onSignIn?: () => void;
}
