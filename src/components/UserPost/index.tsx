import type React from 'react';
import { useEffect, useState, useRef } from 'react';
import { Dimensions, Image, Pressable, Text, View, Share, Linking } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { ScrollView } from 'react-native-gesture-handler';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  interpolate,
} from 'react-native-reanimated';
import Clipboard from '@react-native-clipboard/clipboard';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { formatSocialTime } from '@/src/utilities/datetime';
import { showToast } from '@/src/utilities/toast';
import type { BottomTabNavigationI } from '@/src/navigation/types';
import Comment from '@/src/assets/svgs/Comment';
import Copy from '@/src/assets/svgs/Copy';
import EditPencil from '@/src/assets/svgs/EditPencil';
import HorizontalEllipsis from '@/src/assets/svgs/HorizontalEllipsis';
import ReportFlag from '@/src/assets/svgs/ReportFlag';
import ShareIcon from '@/src/assets/svgs/Share';
import TrashBin from '@/src/assets/svgs/TrashBin';
import { fetchPostCaptionAPI } from '@/src/networks/content/post';
import type { PostCaptionResponse, PostMediaI } from '@/src/networks/content/types';
import { fetchScrapBookPostCaptionAPI } from '@/src/networks/port/scrapbook';
import { ScrapBookPostCaptionResponse } from '@/src/networks/port/types';
import BottomSheet from '../Bottomsheet';
import ImageViewer from '../ImageViewer';
import LikeButton from '../LikeButton';
import CustomModal from '../Modal';
import { OptionItem, OptionsMenu } from '../OptionsMenu';
import UserAvatar from '../UserAvatar';
import type { Match, MediaRendererProps, UserPostPropsI } from './types';

const SCREEN_WIDTH = Dimensions.get('window').width;
const CONTENT_WIDTH = SCREEN_WIDTH - 32;
const MEDIA_RADIUS = 12;
const MAX_CAPTION_LENGTH = 250;

const extractOGData = async (
  text: string,
): Promise<{
  url: string | null;
  image: string | null;
  title: string | null;
  description: string | null;
}> => {
  if (!text) return { url: null, image: null, title: null, description: null };
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  const matches = text.match(urlRegex);

  if (matches && matches.length > 0) {
    try {
      const response = await fetch(matches[0], {
        method: 'GET',
        headers: {
          Accept: 'text/html',
        },
      });

      const html = await response.text();
      const ogImageMatch = html.match(/<meta property="og:image" content="([^"]+)"/i);
      const ogTitleMatch = html.match(/<meta property="og:title" content="([^"]+)"/i);
      const ogDescMatch = html.match(/<meta property="og:description" content="([^"]+)"/i);

      return {
        url: matches[0],
        image: ogImageMatch && ogImageMatch[1] ? ogImageMatch[1] : null,
        title: ogTitleMatch && ogTitleMatch[1] ? ogTitleMatch[1] : null,
        description: ogDescMatch && ogDescMatch[1] ? ogDescMatch[1] : null,
      };
    } catch (error) {
      return { url: matches[0], image: null, title: null, description: null };
    }
  }

  return { url: null, image: null, title: null, description: null };
};

const renderTextWithLinks = (text: string | null | undefined): React.ReactNode => {
  if (!text) return null;
  const trimmedText = text.trim();

  const urlRegex = /(https?:\/\/[^\s]+)/g;
  const hashtagRegex = /(#[a-zA-Z0-9_]+\b)/g;

  const combinedMatches: Match[] = [];

  const urlMatches = [...trimmedText.matchAll(urlRegex)];
  urlMatches.forEach((match) => {
    combinedMatches.push({
      type: 'url',
      text: match[0],
      index: match.index,
      length: match[0]?.length,
    });
  });

  const hashtagMatches = [...trimmedText.matchAll(hashtagRegex)];
  hashtagMatches.forEach((match) => {
    combinedMatches.push({
      type: 'hashtag',
      text: match[0],
      index: match.index,
      length: match[0]?.length,
    });
  });

  combinedMatches.sort((a, b) => (a.index || 0) - (b.index || 0));

  const result: React.ReactNode[] = [];
  let lastIndex = 0;

  combinedMatches.forEach((match, index) => {
    const start = match.index || 0;

    if (lastIndex < start) {
      const textSegment = trimmedText.substring(lastIndex, start);
      result.push(<Text key={`text-${index}`}>{textSegment}</Text>);
    }

    if (match.type === 'url') {
      result.push(
        <Text
          key={`url-${index}`}
          className="text-green-800 underline"
          onPress={() => Linking.openURL(match.text)}
        >
          {match.text}
        </Text>,
      );
    } else if (match.type === 'hashtag') {
      result.push(
        <Text key={`hashtag-${index}`} className="text-green-800" onPress={() => {}}>
          {match.text}
        </Text>,
      );
    }

    lastIndex = start + match?.length;
  });

  if (lastIndex < trimmedText?.length) {
    const remainingText = trimmedText.substring(lastIndex);
    result.push(<Text key="text-end">{remainingText}</Text>);
  }

  return result;
};

const MediaRenderer: React.FC<MediaRendererProps> = ({ item, onPress }) => (
  <Pressable
    className="bg-white rounded-xl p-[5px] shadow-none"
    onPress={() => onPress && onPress()}
  >
    <View className="overflow-hidden rounded-xl shadow-none">
      <Image
        source={{ uri: item.fileUrl }}
        style={{
          width: CONTENT_WIDTH,
          height: CONTENT_WIDTH,
          borderRadius: MEDIA_RADIUS,
        }}
        className="shadow-none"
        resizeMode="cover"
      />
    </View>
    {item.caption && (
      <View className="py-3 px-2 shadow-none">
        <Text className="text-[15px] leading-[20px] text-gray-800 font-medium line-clamp-3">
          {renderTextWithLinks(item.caption)}
        </Text>
      </View>
    )}
  </Pressable>
);

const PaginationDot: React.FC<{ index: number; scrollX: Animated.SharedValue<number> }> = ({
  index,
  scrollX,
}) => {
  const inputRange = [
    (index - 1) * (CONTENT_WIDTH + 8),
    index * (CONTENT_WIDTH + 8),
    (index + 1) * (CONTENT_WIDTH + 8),
  ];

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: interpolate(scrollX.value, inputRange, [0.3, 1, 0.3], {
        extrapolateLeft: 'clamp',
        extrapolateRight: 'clamp',
      }),
      transform: [
        {
          scale: interpolate(scrollX.value, inputRange, [0.8, 1.2, 0.8], {
            extrapolateLeft: 'clamp',
            extrapolateRight: 'clamp',
          }),
        },
      ],
    };
  });

  return (
    <Animated.View
      className="h-2 w-2 mx-1 rounded-full bg-green-700 shadow-none"
      style={animatedStyle}
    />
  );
};

const UserPost: React.FC<UserPostPropsI> = ({
  post,
  onLikePress,
  onCommentPress,
  onLikeCountPress,
  onDeletePress,
  onEditPress,
  onReportPress,
  isOwnPost = false,
  onSharePress,
  isCommentEnabled = true,
  type = 'USER_POST',
  parentScrollRef,
  postIndex,
}) => {
  const navigation = useNavigation<BottomTabNavigationI>();
  const postRef = useRef<View>(null);
  const [ogData, setOgData] = useState<{
    url: string | null;
    image: string | null;
    title: string | null;
    description: string | null;
  }>({
    url: null,
    image: null,
    title: null,
    description: null,
  });
  const [optionsVisible, setOptionsVisible] = useState(false);
  const [imageViewerVisible, setImageViewerVisible] = useState(false);
  const [showFullCaption, setShowFullCaption] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [pendingDeleteAction, setPendingDeleteAction] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [fullCaption, setFullCaption] = useState<string>('');
  const [isLoadingCaption, setIsLoadingCaption] = useState(false);

  const scale = useSharedValue<number>(1);
  const scrollX = useSharedValue<number>(0);
  const currentUser = useSelector(selectCurrentUser);

  const hasMultipleMedia = post.Media && post.Media.length > 1;
  const shouldTruncate = post.isCaptionTruncated && post.caption && post.caption.trim().length > 0;
  const timeAgo = formatSocialTime(post.createdAt);

  const likeButtonStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  useEffect(() => {
    if (post.caption) {
      extractOGData(post.caption).then((data) => {
        setOgData(data);
      });
    }
  }, [post.caption]);

  const handleLikeAnimation = () => {
    scale.value = withSpring(1.2, {}, () => {
      scale.value = withSpring(1);
    });
  };

  const handleLike = () => {
    handleLikeAnimation();
    if (onLikePress) onLikePress();
  };

  const handleShare = async () => {
    if (onSharePress) {
      onSharePress();
      return;
    }

    try {
      const shareUrl = `https://network.navicater.com/post/${type}/${post.id}`;
      const shareMessage = `🚀 Hey there! ${post.Profile.name} just shared an absolutely amazing post on Navicater! 🌟 Don't miss out on this insightful content—check it out now and be inspired! 💡✨\n\n${shareUrl}\n\n#Navicater #GreatContent`;

      const result = await Share.share({
        message: shareMessage,
      });

      if (result.action === Share.sharedAction) {
        showToast({
          type: 'success',
          message: 'Shared Successfully!',
          description: 'Your post has been shared 🎉',
        });
      }
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Sharing Failed',
        description: 'Oops! Something went wrong. Try again.',
      });
    }
  };

  const handleOptions = () => {
    setOptionsVisible(true);
  };

  const handleCloseOptions = () => {
    setOptionsVisible(false);
  };

  const handleBottomSheetHide = () => {
    if (pendingDeleteAction) {
      setDeleteModalVisible(true);
      setPendingDeleteAction(false);
    }
  };

  const showDeleteConfirmation = () => {
    setPendingDeleteAction(true);
    setOptionsVisible(false);
  };

  const confirmDelete = async () => {
    if (onDeletePress) {
      setIsDeleting(true);
      onDeletePress();
      setIsDeleting(false);
      setDeleteModalVisible(false);
    }
  };

  const cancelDelete = () => {
    setDeleteModalVisible(false);
  };

  const handleEdit = () => {
    if (onEditPress) {
      onEditPress();
    }
    handleCloseOptions();
  };

  const handleReport = () => {
    if (onReportPress) {
      onReportPress();
    }
    handleCloseOptions();
  };

  const handleCopyLink = () => {
    const postUrl = `https://network.navicater.com/post/${type}/${post.id}`;
    Clipboard.setString(postUrl);
    showToast({
      type: 'success',
      message: 'Link Copied',
      description: 'Post link copied to clipboard',
    });
    handleCloseOptions();
  };

  const handleMediaPress = (index: number) => {
    setSelectedImageIndex(index);
    setImageViewerVisible(true);
  };

  const scrollToPost = () => {
    if (parentScrollRef?.current && typeof postIndex === 'number') {
      try {
        if (parentScrollRef.current.scrollToIndex) {
          parentScrollRef.current.scrollToIndex({
            index: postIndex,
            animated: true,
            viewPosition: 0,
          });
        } else if (parentScrollRef.current.scrollToOffset) {
          const estimatedItemHeight = 400;
          const offset = postIndex * estimatedItemHeight;
          parentScrollRef.current.scrollToOffset({
            offset: offset,
            animated: true,
          });
        }
      } catch (error) {}
    }
  };

  const fetchFullCaptionData = async () => {
    if (isLoadingCaption || fullCaption) return;

    try {
      setIsLoadingCaption(true);
      const response =
        type === 'USER_POST'
          ? await fetchPostCaptionAPI(post.id)
          : await fetchScrapBookPostCaptionAPI(post.id);

      setFullCaption(
        type === 'USER_POST'
          ? (response as PostCaptionResponse).caption
          : (response as ScrapBookPostCaptionResponse).caption,
      );
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Error',
        description: 'Failed to load full caption',
      });
    } finally {
      setIsLoadingCaption(false);
    }
  };

  const toggleCaptionExpand = async (event: any) => {
    event.stopPropagation();
    const wasExpanded = showFullCaption;

    if (!wasExpanded && shouldTruncate) {
      await fetchFullCaptionData();
    }

    setShowFullCaption(!showFullCaption);

    if (wasExpanded) {
      scrollToPost();
    }
  };

  const handleUserProfileNavigation = () => {
    if (post.Profile.id === currentUser.profileId) {
      navigation.reset({
        index: 0,
        routes: [
          {
            name: 'ProfileStack',
            state: {
              routes: [
                {
                  name: 'UserProfile',
                  params: { fromTabPress: false, profileId: currentUser.profileId },
                },
              ],
            },
          },
        ],
      });
    } else {
      navigation.navigate('HomeStack', {
        screen: 'OtherUserProfile',
        params: { fromTabPress: false, profileId: post.Profile.id },
      });
    }
  };

  const handlePostContentNavigation = () => {
    if (post.Profile.id === currentUser.profileId) {
      navigation.reset({
        index: 0,
        routes: [
          {
            name: 'ProfileStack',
            state: {
              routes: [
                {
                  name: 'Comment',
                  params: { postId: post.id, type },
                },
              ],
            },
          },
        ],
      });
    } else {
      navigation.navigate('Comment', { postId: post.id, type });
    }
  };

  const renderMediaItem = (media: PostMediaI, index: number) => {
    return (
      <View
        key={`media-${index}`}
        style={{
          width: CONTENT_WIDTH,
          marginRight: index < (post.Media ? post.Media.length - 1 : 0) ? 8 : 0,
        }}
        className="shadow-none"
      >
        <MediaRenderer item={media} isMultiple={true} onPress={() => handleMediaPress(index)} />
      </View>
    );
  };

  const renderCaptionContent = () => {
    if (!post.caption) return null;

    if (!shouldTruncate) {
      return (
        <View>
          <Pressable onPress={handlePostContentNavigation}>
            <Text className="text-base text-gray-800 w-full leading-6">
              {renderTextWithLinks(post.caption)}
            </Text>
          </Pressable>
        </View>
      );
    }

    if (showFullCaption) {
      const captionToShow = fullCaption || post.caption;
      return (
        <View>
          <Pressable onPress={handlePostContentNavigation}>
            <Text className="text-base text-gray-800 w-full leading-6">
              {renderTextWithLinks(captionToShow)}
            </Text>
          </Pressable>
          <Pressable
            onPress={toggleCaptionExpand}
            className="mt-2"
            hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
          >
            <Text className="text-green-800 font-medium">See Less</Text>
          </Pressable>
        </View>
      );
    }

    const truncatedText =
      post.caption.length > MAX_CAPTION_LENGTH
        ? post.caption.substring(0, MAX_CAPTION_LENGTH)
        : post.caption.substring(0, Math.min(100, post.caption.length));

    return (
      <View>
        <Pressable onPress={handlePostContentNavigation}>
          <Text className="text-base text-gray-800 w-full leading-6">
            {renderTextWithLinks(truncatedText)}
            <Text className="text-gray-400">...</Text>
          </Text>
        </Pressable>
        <Pressable
          onPress={toggleCaptionExpand}
          className="mt-2"
          hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
          disabled={isLoadingCaption}
        >
          <Text className="text-green-800 font-medium">
            {isLoadingCaption ? 'Loading...' : 'See More'}
          </Text>
        </Pressable>
      </View>
    );
  };

  return (
    <View
      ref={postRef}
      className="bg-white border-b-8 border-gray-200 mb-2 rounded-lg overflow-hidden"
    >
      <View className="px-5 pt-5">
        <View className="flex-row justify-between items-center">
          <Pressable
            onPress={handleUserProfileNavigation}
            className="flex-row items-center gap-3 flex-1"
          >
            <View className="h-[54px] w-[54px] bg-gray-100 rounded-full justify-center items-center border-2 border-gray-200">
              <UserAvatar
                avatarUri={post.Profile.avatar}
                height={54}
                width={54}
                name={post.Profile.name}
              />
            </View>
            <View className="flex-1 mr-2">
              <Text className="font-semibold text-base text-gray-900" numberOfLines={1}>
                {post.Profile.name}
              </Text>
              <Text className="text-gray-500 text-sm" numberOfLines={1}>
                {post.Profile.designation?.name}{' '}
                {post.Profile.entity?.name ? `at ${post.Profile.entity.name}` : ''}
              </Text>
              <Text className="text-gray-400 text-xs">{timeAgo}</Text>
            </View>
          </Pressable>
          <Pressable onPress={handleOptions} className="p-2 bg-gray-50 rounded-full">
            <HorizontalEllipsis />
          </Pressable>
        </View>

        {post.caption && <View className="mt-4 pb-4">{renderCaptionContent()}</View>}

        {ogData.image && !post.Media?.length && (
          <Pressable
            onPress={() => ogData.url && Linking.openURL(ogData.url)}
            className="mt-2 mb-4 border border-gray-200 rounded-lg overflow-hidden"
          >
            <Image
              source={{ uri: ogData.image }}
              style={{
                width: CONTENT_WIDTH,
                height: 200,
              }}
              resizeMode="cover"
            />
            <View className="p-3">
              {ogData.title && (
                <Text className="text-[15px] font-semibold text-gray-800 mb-1 line-clamp-2">
                  {ogData.title}
                </Text>
              )}
              {ogData.description && (
                <Text className="text-[13px] text-gray-600 line-clamp-2">{ogData.description}</Text>
              )}
              {ogData.url && (
                <Text className="text-[12px] text-gray-500 mt-1 line-clamp-1">
                  {ogData.url.replace(/^https?:\/\/(www\.)?/, '')}
                </Text>
              )}
            </View>
          </Pressable>
        )}
      </View>

      {post?.Media && post.Media.length > 0 && (
        <Pressable onPress={handlePostContentNavigation} className="mt-2 px-4 shadow-none">
          {hasMultipleMedia ? (
            <View className="shadow-none">
              <ScrollView
                horizontal
                pagingEnabled
                showsHorizontalScrollIndicator={false}
                className="w-full shadow-none"
                onScroll={({ nativeEvent }) => {
                  scrollX.value = nativeEvent.contentOffset.x;
                }}
                scrollEventThrottle={16}
                decelerationRate="fast"
              >
                {post.Media.map((media, index) => renderMediaItem(media, index))}
              </ScrollView>

              <View className="flex-row justify-center my-3 shadow-none">
                {post.Media.map((_, index) => (
                  <PaginationDot key={`dot-${index}`} index={index} scrollX={scrollX} />
                ))}
              </View>
            </View>
          ) : (
            <MediaRenderer
              item={post.Media[0]}
              isMultiple={false}
              onPress={() => handleMediaPress(0)}
            />
          )}
        </Pressable>
      )}

      <View className="px-5 py-6 shadow-none">
        <View className="flex-row justify-between items-center shadow-none">
          <View className="flex-row items-center gap-8 shadow-none">
            <View className="flex-row items-center shadow-none">
              <Pressable
                onPress={handleLike}
                className="pr-2 shadow-none"
                hitSlop={{ top: 10, bottom: 10, left: 10 }}
              >
                <Animated.View style={likeButtonStyle} className="shadow-none">
                  <LikeButton liked={post.isLiked} />
                </Animated.View>
              </Pressable>
              <Pressable onPress={onLikeCountPress} className="py-2 px-3 shadow-none">
                <Text className="text-gray-800 font-medium">{post.reactionsCount}</Text>
              </Pressable>
            </View>

            {isCommentEnabled && (
              <View className="flex-row items-center shadow-none">
                <Pressable
                  onPress={onCommentPress}
                  className="pr-2 shadow-none"
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                >
                  <Comment fill="#525252" />
                </Pressable>
                <Pressable
                  onPress={onCommentPress}
                  className="py-2 px-3 shadow-none"
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                >
                  <Text className="text-gray-800 font-medium">{post.totalCommentsCount}</Text>
                </Pressable>
              </View>
            )}
          </View>
          <Pressable
            onPress={handleShare}
            className="flex-row items-center gap-2 bg-gray-50 px-4 py-2 rounded-full shadow-none"
          >
            <ShareIcon />
          </Pressable>
        </View>
      </View>

      <BottomSheet
        height={isOwnPost ? 230 : 200}
        visible={optionsVisible}
        onClose={handleCloseOptions}
        onModalHide={handleBottomSheetHide}
      >
        <OptionsMenu>
          {isOwnPost ? (
            <>
              <OptionItem
                icon={<TrashBin stroke="#EF4444" strokeWidth={1.5} width={2} height={2} />}
                label="Delete post"
                textClassName="text-red-500"
                onPress={showDeleteConfirmation}
              />
            </>
          ) : (
            <OptionItem
              icon={<ReportFlag stroke="#374151" strokeWidth={1.5} width={2} height={2} />}
              label="Report post"
              onPress={handleReport}
            />
          )}
          <View className="h-[1px] bg-gray-200 my-2" />
          <OptionItem
            icon={<Copy stroke="#374151" strokeWidth={1.5} width={2} height={2} />}
            label="Copy Link"
            onPress={handleCopyLink}
          />
          <View className="h-[1px] bg-gray-200 my-2" />
          {onEditPress && isOwnPost && (
            <OptionItem
              icon={<EditPencil stroke="#374151" strokeWidth={1.5} width={2} height={2} />}
              label="Edit post"
              onPress={handleEdit}
            />
          )}
        </OptionsMenu>
      </BottomSheet>

      {imageViewerVisible && (
        <ImageViewer
          isVisible={imageViewerVisible}
          onClose={() => setImageViewerVisible(false)}
          post={post}
          initialIndex={selectedImageIndex}
        />
      )}

      <CustomModal
        isVisible={deleteModalVisible}
        title="Delete Post"
        description="Are you sure you want to delete this post? This action cannot be undone."
        confirmText="Delete"
        confirmButtonVariant="danger"
        cancelText="Cancel"
        onConfirm={confirmDelete}
        onCancel={cancelDelete}
        isConfirming={isDeleting}
      />
    </View>
  );
};

export default UserPost;
