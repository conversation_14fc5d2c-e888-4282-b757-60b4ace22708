/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from './types';

const Calendar: React.FC<OutlinedIconPropsI> = ({
  width = 2.6,
  height = 2.48,
  stroke = '#A3A3A3',
  color,
  strokeWidth = 2,
  disabled,
  accessibilityLabel = 'Calendar',
  ...props
}) => {
  const strokeColor = color || stroke;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 21 20"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        d="M13.833 1.667V5M7.167 1.667V5M3 8.333h15m-13.333-5h11.666C17.253 3.333 18 4.08 18 5v11.667c0 .92-.746 1.666-1.667 1.666H4.667c-.92 0-1.667-.746-1.667-1.666V5c0-.92.746-1.667 1.667-1.667z"
        stroke={strokeColor}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default Calendar;
