import React, { useState, useEffect } from 'react';
import { View, Text, Pressable } from 'react-native';
import { OptionSelectorProps } from './types';

const OptionSelector: React.FC<OptionSelectorProps> = ({ options, onSelect, defaultSelected }) => {
  const [selectedId, setSelectedId] = useState<string | null>(defaultSelected || null);

  useEffect(() => {
    if (defaultSelected) {
      setSelectedId(defaultSelected);
    }
  }, [defaultSelected]);

  const handleSelect = (id: string) => {
    setSelectedId(id);
    onSelect?.(id);
  };

  return (
    <View className="mb-4">
      {options.map((option) => {
        const isSelected = selectedId === option.id;

        return (
          <Pressable
            key={option.id}
            className="py-1 flex-row items-center"
            accessibilityRole="radio"
            accessibilityState={{ checked: isSelected }}
            onPress={() => handleSelect(option.id)}
          >
            <View
              className={`rounded-full border border-grey-200 p-1 w-4 h-4 mr-2 ${
                isSelected ? 'bg-[#448600] border-[#448600]' : 'bg-white'
              }`}
            />
            <Text className="text-base text-black">{option.title}</Text>
          </Pressable>
        );
      })}
    </View>
  );
};

export default OptionSelector;
