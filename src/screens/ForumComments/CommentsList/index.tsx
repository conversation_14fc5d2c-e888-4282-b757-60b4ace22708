/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React, { forwardRef, useImperativeHandle, useEffect } from 'react';
import { View, Text, Pressable, ActivityIndicator } from 'react-native';
import { FlatList } from 'react-native-gesture-handler';
import { useDispatch, useSelector } from 'react-redux';
import NotFound from '@/src/components/NotFound';
import {
  selectForumComments,
  selectForumLoading,
  selectForumError,
  selectForumAnswerComments,
} from '@/src/redux/selectors/forum';
import {
  fetchCommentsForQuestion,
  fetchCommentsForAnswer,
} from '@/src/redux/slices/forum/forumSlice';
import { AppDispatch, RootState } from '@/src/redux/store';
import CommentItem from '../CommentItem';
import { ForumCommentTypeI } from '../types';

type CommentsListProps = {
  postId: string;
  type: 'FORUM_QUESTION' | 'FORUM_ANSWER';
  onReply: (comment: ForumCommentTypeI) => void;
};

const CommentsList = forwardRef(({ postId, type, onReply }: CommentsListProps, ref) => {
  const dispatch = useDispatch<AppDispatch>();
  const { comments, total, nextCursorId } = useSelector((state: RootState) =>
    type === 'FORUM_QUESTION'
      ? selectForumComments(state, postId)
      : selectForumAnswerComments(state, postId),
  );
  const loading = useSelector(selectForumLoading);
  const error = useSelector(selectForumError);
  const hasMore = comments.length < total;

  useEffect(() => {
    if (type === 'FORUM_QUESTION') {
      dispatch(fetchCommentsForQuestion({ questionId: postId, cursorId: null, pageSize: 10 }));
    } else {
      dispatch(fetchCommentsForAnswer({ answerId: postId, cursorId: null, pageSize: 10 }));
    }
  }, [dispatch, postId, type]);

  const loadMore = () => {
    if (hasMore && nextCursorId) {
      if (type === 'FORUM_QUESTION') {
        dispatch(
          fetchCommentsForQuestion({ questionId: postId, cursorId: nextCursorId, pageSize: 10 }),
        );
      } else {
        dispatch(
          fetchCommentsForAnswer({ answerId: postId, cursorId: nextCursorId, pageSize: 10 }),
        );
      }
    }
  };

  const refresh = () => {
    if (type === 'FORUM_QUESTION') {
      dispatch(fetchCommentsForQuestion({ questionId: postId, cursorId: null, pageSize: 10 }));
    } else {
      dispatch(fetchCommentsForAnswer({ answerId: postId, cursorId: null, pageSize: 10 }));
    }
  };

  useImperativeHandle(ref, () => ({
    refresh,
  }));

  if (loading && comments.length === 0) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator />
      </View>
    );
  }

  if (error) {
    return (
      <View className="flex-1 justify-center items-center">
        <Text className="text-red-500">{error}</Text>
      </View>
    );
  }

  return (
    <View className="flex-1 px-2">
      <FlatList
        data={comments as ForumCommentTypeI[]}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <CommentItem comment={item} onReply={() => onReply(item)} postId={postId} type={type} />
        )}
        showsVerticalScrollIndicator={false}
        onRefresh={refresh}
        refreshing={loading}
        ListEmptyComponent={<NotFound />}
        onEndReached={loadMore}
        onEndReachedThreshold={0.7}
        scrollEnabled={true}
        ListFooterComponent={() => <>{loading && <ActivityIndicator className="my-4" />}</>}
        contentContainerStyle={{
          flexGrow: 1,
          backgroundColor: 'white',
        }}
      />
    </View>
  );
});

export default CommentsList;
