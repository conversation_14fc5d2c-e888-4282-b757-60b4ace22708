import { ClientCargoI } from '@/src/redux/slices/experience/types';
import { ShipCreateEditPayloadI } from '../EditShipItem/types';

export type ShipCargoI = {
  id: string;
  name: string;
  description: string;
  fromDate: string;
  toDate: string;
};

export type CargoPropsI = {
  profileId: string;
  shipId: string;
  isAddVisible: boolean;
  handleAdd: () => ShipCreateEditPayloadI[];
  fromProfileExperience: boolean;
  clientCargos?: ClientCargoI[];
  shipTempId?: string;
  shipData: {
    fromDate: string;
    toDate: string | null;
  };
};

export type UseCargoI = {
  handleAdd: () => void;
};
