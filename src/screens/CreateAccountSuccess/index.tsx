/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import LottieView from 'lottie-react-native';
import Button from '@/src/components/Button';
import SafeArea from '@/src/components/SafeArea';
import TextView from '@/src/components/TextView';
import { AppStackParamListI } from '@/src/navigation/types';

type CreateAccountSuccessNavigationProp = StackNavigationProp<AppStackParamListI>;

const CreateAccountSuccess = () => {
  const navigation = useNavigation<CreateAccountSuccessNavigationProp>();

  const handleBackToSignIn = () => {
    navigation.navigate('UserLogin');
  };

  return (
    <SafeArea>
      <View className="flex-1 px-4 py-5">
        <View className="flex-1 items-center justify-center">
          <LottieView
            source={require('@/src/assets/animations/ship.json')}
            autoPlay
            loop
            style={{ width: 230, height: 230 }}
          />
          <View className="mt-8">
            <TextView
              title="Your account has been created!"
              subtitle="You are now ready to sign in!"
              subtitleClassName="text-center"
              titleClassName="text-center"
            />
          </View>
        </View>
        <Button label="Back to Sign in" onPress={handleBackToSignIn} variant="primary" />
      </View>
    </SafeArea>
  );
};

export default CreateAccountSuccess;
