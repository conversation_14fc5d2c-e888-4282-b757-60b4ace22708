/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const Close: React.FC<FilledIconPropsI> = ({
  width = 2.976,
  height = 2.976,
  fill = '#343330',
  color,
  disabled,
  accessibilityLabel = 'Close',
  ...props
}) => {
  const fillColor = color || fill;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 32 32"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        d="M25.7074 24.2926C25.8004 24.3855 25.8741 24.4958 25.9243 24.6172C25.9746 24.7386 26.0005 24.8687 26.0005 25.0001C26.0005 25.1315 25.9746 25.2616 25.9243 25.383C25.8741 25.5044 25.8004 25.6147 25.7074 25.7076C25.6145 25.8005 25.5042 25.8742 25.3828 25.9245C25.2614 25.9747 25.1313 26.0006 24.9999 26.0006C24.8686 26.0006 24.7384 25.9747 24.6171 25.9245C24.4957 25.8742 24.3854 25.8005 24.2924 25.7076L15.9999 17.4138L7.70745 25.7076C7.5198 25.8952 7.26531 26.0006 6.99995 26.0006C6.73458 26.0006 6.48009 25.8952 6.29245 25.7076C6.1048 25.5199 5.99939 25.2654 5.99939 25.0001C5.99939 24.7347 6.10481 24.4802 6.29245 24.2926L14.5862 16.0001L6.29245 7.70757C6.1048 7.51993 5.99939 7.26543 5.99939 7.00007C5.99939 6.7347 6.1048 6.48021 6.29245 6.29257C6.48009 6.10493 6.73458 5.99951 6.99995 5.99951C7.26531 5.99951 7.5198 6.10493 7.70745 6.29257L15.9999 14.5863L24.2924 6.29257C24.4801 6.10493 24.7346 5.99951 24.9999 5.99951C25.2653 5.99951 25.5198 6.10493 25.7074 6.29257C25.8951 6.48021 26.0005 6.7347 26.0005 7.00007C26.0005 7.26543 25.8951 7.51993 25.7074 7.70757L17.4137 16.0001L25.7074 24.2926Z"
        fill={fillColor}
      />
    </Svg>
  );
};

export default Close;
