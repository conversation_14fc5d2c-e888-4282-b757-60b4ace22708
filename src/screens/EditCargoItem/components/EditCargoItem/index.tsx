import { useEffect, useRef, useState } from 'react';
import { ActivityIndicator, Pressable, Text, View, ScrollView } from 'react-native';
import { useNavigation, type NavigationAction } from '@react-navigation/native';
import { Controller } from 'react-hook-form';
import BackButton from '@/src/components/BackButton';
import DatePicker from '@/src/components/DatePicker';
import CustomModal from '@/src/components/Modal';
import SafeArea from '@/src/components/SafeArea';
import TextInput from '@/src/components/TextInput';
import { validateFromDate, validateToDate } from '../utils';
import type { EditCargoItemPropsI } from './types';
import { useEditCargoItem } from './useHook';

const EditCargoItem = ({
  onBack,
  cargoId,
  preFilledData,
  refetch,
  shipData,
  isClientMode = false,
  onClientSave,
  shipTempId,
}: EditCargoItemPropsI & {
  isClientMode?: boolean;
  onClientSave?: (cargo: any) => void;
  shipTempId?: string;
}) => {
  const { methods, onSubmit, isSubmitting, loading, hasChanges, handleClientSave, isSaving } =
    useEditCargoItem(cargoId, preFilledData, refetch, isClientMode, onClientSave, shipTempId);

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors },
  } = methods;
  const fromDate = watch('fromDate');
  const toDate = watch('toDate');

  const navigation = useNavigation();
  const [showDiscardModal, setShowDiscardModal] = useState(false);
  const exitActionRef = useRef<NavigationAction | null>(null);

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', (e) => {
      if (hasChanges && !isSubmitting && !isSaving) {
        e.preventDefault();
        exitActionRef.current = e.data.action;
        setShowDiscardModal(true);
      }
    });
    return unsubscribe;
  }, [navigation, hasChanges, isSubmitting, isSaving]);

  const handleDiscardConfirm = () => {
    setShowDiscardModal(false);
    setTimeout(() => {
      if (exitActionRef.current) {
        navigation.dispatch(exitActionRef.current);
      }
    }, 300);
  };

  const handleDiscardCancel = () => {
    setShowDiscardModal(false);
  };

  const handleSave = () => {
    if (isClientMode) {
      handleSubmit(handleClientSave)();
    } else {
      handleSubmit(onSubmit)();
    }
  };

  if (loading) {
    return (
      <SafeArea>
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="small" color="#448600" />
          <Text className="mt-4 text-gray-600">Loading cargo details...</Text>
        </View>
      </SafeArea>
    );
  }

  return (
    <ScrollView
      className="flex-1 bg-white px-4"
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{ flexGrow: 1 }}
    >
      <View className="flex-row items-center justify-between py-4">
        <BackButton onBack={onBack} label={cargoId ? 'Edit Cargo' : 'Create Cargo'} />
        <Pressable onPress={handleSave} disabled={isSubmitting || !hasChanges}>
          <Text
            className={`text-lg font-medium ${isSubmitting || !hasChanges ? 'text-gray-400' : 'text-[#448600]'}`}
          >
            {isSubmitting ? 'Saving...' : 'Save'}
          </Text>
        </Pressable>
      </View>

      <Controller
        control={control}
        name="name"
        rules={{ required: 'Cargo Name is required' }}
        render={({ field: { onChange, value }, fieldState: { error } }) => (
          <TextInput
            label="Cargo Name"
            value={value}
            onChangeText={onChange}
            placeholder="Enter cargo name"
            error={error?.message}
            className="py-3"
          />
        )}
      />

      <Controller
        control={control}
        name="description"
        rules={{ required: 'Description is required' }}
        render={({ field: { onChange, value }, fieldState: { error } }) => (
          <TextInput
            label="Description"
            value={value}
            onChangeText={onChange}
            placeholder="Enter description"
            error={error?.message}
            className="py-3"
          />
        )}
      />

      <View className="flex-row mb-6">
        <View className="flex-1 mr-2">
          <Controller
            control={control}
            name="fromDate"
            rules={{
              required: 'Start date is required',
              validate: (value) =>
                validateFromDate(value, toDate, shipData.fromDate, shipData.toDate),
            }}
            render={({ field: { onChange }, fieldState: { error } }) => (
              <>
                <DatePicker
                  title="From"
                  selectedDate={fromDate}
                  onDateChange={(date) => {
                    if (date instanceof Date) {
                      onChange(date.toISOString().split('T')[0]);
                    }
                  }}
                  showMonthYear={true}
                  className={error ? 'border-red-500' : ''}
                />
                {error && <Text className="text-red-500 text-xs mt-1">{error.message}</Text>}
              </>
            )}
          />
        </View>
        <View className="flex-1 ml-2">
          <Controller
            control={control}
            name="toDate"
            rules={{
              required: 'End date is required',
              validate: (value) =>
                validateToDate(value, false, fromDate, shipData.fromDate, shipData.toDate),
            }}
            render={({ field: { onChange }, fieldState: { error } }) => (
              <>
                <DatePicker
                  title="To"
                  selectedDate={toDate}
                  onDateChange={(date) => {
                    if (date instanceof Date) {
                      onChange(date.toISOString().split('T')[0]);
                    }
                  }}
                  showMonthYear={true}
                  className={error ? 'border-red-500' : ''}
                />
                {error && <Text className="text-red-500 text-xs mt-1">{error.message}</Text>}
              </>
            )}
          />
        </View>
      </View>

      <CustomModal
        isVisible={showDiscardModal}
        title="Discard changes?"
        description="You have unsaved changes. Are you sure you want to discard them and leave this screen?"
        onCancel={handleDiscardCancel}
        onConfirm={handleDiscardConfirm}
        cancelText="Cancel"
        confirmText="Discard"
        confirmButtonVariant="danger"
      />
    </ScrollView>
  );
};

export default EditCargoItem;
