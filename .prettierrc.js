/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
module.exports = {
  printWidth: 100,
  tabWidth: 2,
  semi: true,
  singleQuote: true,
  bracketSpacing: true,
  trailingComma: 'all',
  plugins: ['@trivago/prettier-plugin-sort-imports'],
  importOrder: [
    '^(react|react-native)$',
    '^@react-navigation/(.*)$',
    '^react-native-(.*)$',
    '^redux-(.*)$',
    '<THIRD_PARTY_MODULES>',
    '^@/src/components/(.*)$',
    '^@/src/redux/(.*)$',
    '^@/src/utilities/(.*)$',
    '^@/src/consts/(.*)$',
    '^@/src/types/(.*)$',
    '^@/src/errors/(.*)$',
    '^@/src/navigation(.*)$',
    '^@/src/screens/(.*)$',
    '^@/src/services/(.*)$',
    '^@/src/assets/(.*)$',
    '^@/(.*)$',
    '^[./]',
    '.css$',
  ],
  importOrderCaseInsensitive: true,
};
