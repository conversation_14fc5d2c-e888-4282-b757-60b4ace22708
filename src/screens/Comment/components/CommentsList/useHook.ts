/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  selectCommentsByPostId,
  selectScrapbookCommentsByPostId,
} from '@/src/redux/selectors/content';
import {
  fetchCommentsForPost,
  fetchScrapbookCommentsForPost,
} from '@/src/redux/slices/content/contentSlice';
import { AppDispatch, RootState } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';

export const useCommentsListResult = (postId: string, type: 'USER_POST' | 'SCRAPBOOK_POST') => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const dispatch = useDispatch<AppDispatch>();

  const comments = useSelector((state: RootState) => {
    if (type === 'SCRAPBOOK_POST') {
      return selectScrapbookCommentsByPostId(state, postId);
    }
    return selectCommentsByPostId(state, postId);
  });

  const hasMore = comments?.total !== undefined && comments.comments.length < comments.total;

  const fetchComments = async () => {
    if (loading) return;
    setLoading(true);
    try {
      if (type === 'SCRAPBOOK_POST') {
        await dispatch(
          fetchScrapbookCommentsForPost({
            scrapBookPostId: postId,
            cursorId: null,
            pageSize: 10,
          }),
        ).unwrap();
      } else {
        await dispatch(fetchCommentsForPost({ postId, cursorId: null, pageSize: 10 })).unwrap();
      }
    } catch (error) {
      if (!comments || !comments.comments?.length) {
        triggerErrorBoundary(
          new Error(
            `Failed to load comments: ${error instanceof Error ? error.message : 'Unknown error'}`,
          ),
        );
      } else {
        showToast({
          message: 'Error',
          type: 'error',
          description: 'Failed to load comments',
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleLoadMore = async () => {
    if (loading || !comments || !comments.comments?.length || !hasMore) {
      return;
    }

    setLoading(true);
    try {
      if (type === 'SCRAPBOOK_POST') {
        await dispatch(
          fetchScrapbookCommentsForPost({
            scrapBookPostId: postId,
            cursorId: comments.cursorId,
            pageSize: 10,
          }),
        ).unwrap();
      } else {
        if (!comments.cursorId) return;

        await dispatch(
          fetchCommentsForPost({
            cursorId: comments.cursorId,
            postId,
            pageSize: 10,
          }),
        ).unwrap();
      }
    } catch (error) {
      showToast({
        message: 'Error',
        type: 'error',
        description: 'Failed to load more comments',
      });
    } finally {
      setLoading(false);
    }
  };

  return {
    comments,
    loading,
    fetchComments,
    handleLoadMore,
    hasMore,
  };
};
