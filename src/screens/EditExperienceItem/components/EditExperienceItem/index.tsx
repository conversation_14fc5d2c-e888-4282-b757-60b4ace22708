import type React from 'react';
import { useState, useRef, useEffect } from 'react';
import { ActivityIndicator, Pressable, ScrollView, Text, View, Animated } from 'react-native';
import { useNavigation, type NavigationAction } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import Checkbox from '@/src/components/Checkbox';
import DatePicker from '@/src/components/DatePicker';
import EntitySearch from '@/src/components/EntitySearch';
import CustomModal from '@/src/components/Modal';
import NotFound from '@/src/components/NotFound';
import TextView from '@/src/components/TextView';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { showToast } from '@/src/utilities/toast';
import AddItem from '@/src/assets/svgs/AddItem';
import EditPencil from '@/src/assets/svgs/EditPencil';
import DeleteIcon from '@/src/assets/svgs/TrashBin';
import { toMonthYear } from '../utils';
import type {
  EditExperienceItemPropsI,
  FieldTypeI,
  ShipItemProps,
  UseEditExperienceItemReturn,
} from './types';
import useEditExperienceItem from './useHook';

const ShipItem: React.FC<ShipItemProps> = ({
  field,
  onAddEdit,
  deleteShip,
  isDeleting,
  disableAddShips,
}) => {
  const [shipDeleteId, setShipDeleteId] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const buttonScales = useRef({
    add: new Animated.Value(1),
    edit: new Animated.Value(1),
    delete: new Animated.Value(1),
  }).current;

  const animateButton = (scale: Animated.Value) => {
    Animated.sequence([
      Animated.timing(scale, { toValue: 1.1, duration: 100, useNativeDriver: true }),
      Animated.timing(scale, { toValue: 1, duration: 100, useNativeDriver: true }),
    ]).start();
  };

  const handleAddPress = () => {
    animateButton(buttonScales.add);
    onAddEdit(field);
  };

  const handleEditPress = (shipId: string) => {
    animateButton(buttonScales.edit);
    onAddEdit(field, shipId);
  };

  const handleDeletePress = (shipId: string) => {
    animateButton(buttonScales.delete);
    setShipDeleteId(shipId);
    setIsVisible(true);
  };

  const handleConfirmDelete = () => {
    if (shipDeleteId) {
      deleteShip(field, shipDeleteId);
      setIsVisible(false);
    }
  };

  const handleCancelDelete = () => {
    setShipDeleteId(null);
    setIsVisible(false);
  };

  const ships = field.ships ?? [];

  return (
    <>
      <View className="px-4 py-2 bg-[#F5F5F5] rounded-lg">
        <View className="flex-row items-center justify-between">
          <TextView subtitle="Ship details" subtitleClassName="font-bold text-black" />
          <Animated.View style={{ transform: [{ scale: buttonScales.add }] }}>
            <Pressable
              onPress={handleAddPress}
              disabled={disableAddShips}
              className={disableAddShips ? 'opacity-50' : ''}
            >
              <AddItem color={disableAddShips ? '#9CA3AF' : undefined} />
            </Pressable>
          </Animated.View>
        </View>

        {ships.length === 0 && (
          <NotFound
            imageStyle={{ width: '50%', height: 100 }}
            fullScreen={false}
            className="py-5"
          />
        )}

        {ships.map((ship: any) => (
          <View className="py-3" key={ship.id}>
            <TextView subtitle={ship.name} subtitleClassName="font-bold" />
            <View className="flex-row items-center justify-between">
              <TextView
                subtitle={`${toMonthYear(ship.fromDate)} - ${
                  ship.isPresent || !ship.toDate ? 'Present' : toMonthYear(ship.toDate)
                }`}
              />
              <View className="flex-row items-center gap-4">
                <Animated.View style={{ transform: [{ scale: buttonScales.edit }] }}>
                  <Pressable onPress={() => handleEditPress(ship.id)} disabled={disableAddShips}>
                    <EditPencil
                      width={2.3}
                      height={2.3}
                      color={disableAddShips ? '#9CA3AF' : undefined}
                    />
                  </Pressable>
                </Animated.View>
                <Animated.View style={{ transform: [{ scale: buttonScales.delete }] }}>
                  <Pressable onPress={() => handleDeletePress(ship.id)} disabled={disableAddShips}>
                    <DeleteIcon
                      width={2.3}
                      height={2.3}
                      color={disableAddShips ? '#9CA3AF' : undefined}
                    />
                  </Pressable>
                </Animated.View>
              </View>
            </View>
          </View>
        ))}
      </View>

      <CustomModal
        isVisible={isVisible}
        onCancel={handleCancelDelete}
        title="Are you sure you want to delete this ship?"
        description="This action cannot be undone."
        cancelText="Cancel"
        confirmText="Delete"
        confirmButtonVariant="danger"
        isConfirming={isDeleting}
        onConfirm={handleConfirmDelete}
      />
    </>
  );
};

const EditExperienceItem: React.FC<EditExperienceItemPropsI> = ({ onBack, experienceId }) => {
  const [removingIndex, setRemovingIndex] = useState<number | null>(null);
  const [isSubmitted, setIsSubmitted] = useState<boolean>(false);
  const [rerenderTrigger, setRerenderTrigger] = useState<number>(0);
  const [showDiscardModal, setShowDiscardModal] = useState(false);

  const scrollViewRef = useRef<ScrollView>(null);
  const exitActionRef = useRef<NavigationAction | null>(null);
  const addDesignationScale = useRef(new Animated.Value(1)).current;
  const removeButtonScales = useRef<{ [key: number]: Animated.Value }>({}).current;

  const {
    isSubmitting,
    loading,
    isAddingDesignation,
    handleAddDesignation,
    handleAddEditShip,
    handleDeleteShip,
    isDeleting,
    localDesignations,
    setLocalDesignations,
    getDateChangeHandler,
    localEntity,
    handleSubmit,
    getFieldError,
    handlePresentCheckbox,
    designationSelections,
    hasChanges,
    clearAndReindexSelections,
    clearFields,
  }: UseEditExperienceItemReturn = useEditExperienceItem(experienceId);

  const entitySelection = useSelector(selectSelectionByKey('entity'));
  const navigation = useNavigation();

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', (e) => {
      if (hasChanges && !isSubmitting) {
        e.preventDefault();
        exitActionRef.current = e.data.action;
        setShowDiscardModal(true);
      }
    });
    return unsubscribe;
  }, [navigation, hasChanges, isSubmitting]);

  const animateButton = (scale: Animated.Value) => {
    Animated.sequence([
      Animated.timing(scale, { toValue: 1.1, duration: 100, useNativeDriver: true }),
      Animated.timing(scale, { toValue: 1, duration: 100, useNativeDriver: true }),
    ]).start();
  };

  const isDesignationComplete = (designation: any, index: number) => {
    const designationSelection = designationSelections[index];
    return (
      (designationSelection?.id || designation?.designation?.id) &&
      designation?.fromDate &&
      (designation?.isPresent || designation?.toDate)
    );
  };

  const canAddDesignation = () => {
    if (localDesignations.length === 0) return true;
    const lastIndex = localDesignations.length - 1;
    const lastDesignation = localDesignations[lastIndex];
    return isDesignationComplete(lastDesignation, lastIndex);
  };

  const handleAddDesignationClick = () => {
    if (!canAddDesignation()) {
      showToast({
        message: 'Incomplete designation',
        description: 'Please complete the incomplete designation before adding new',
        type: 'error',
      });
      return;
    }
    animateButton(addDesignationScale);
    handleAddDesignation();
    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, 100);
  };

  const handleRemoveDesignation = async (index: number) => {
    if (!removeButtonScales[index]) {
      removeButtonScales[index] = new Animated.Value(1);
    }

    animateButton(removeButtonScales[index]);
    setRemovingIndex(index);

    const newDesignations = localDesignations.filter((_: any, i: number) => i !== index);
    await clearAndReindexSelections(localDesignations, newDesignations);
    await setLocalDesignations(newDesignations);

    setRerenderTrigger((prev) => prev + 1);
    setRemovingIndex(null);

    setTimeout(() => {
      scrollViewRef.current?.scrollTo({ y: 0, animated: true });
    }, 100);

    showToast({
      message: 'Designation Removed',
      description:
        'This designation has been removed. To undo, go back. If you proceed to save, the change will be permanent.',
      type: 'success',
    });
  };

  const handleDiscardConfirm = async () => {
    setShowDiscardModal(false);
    setTimeout(async () => {
      if (exitActionRef.current) {
        navigation.dispatch(exitActionRef.current);
        await clearFields();
      }
    }, 300);
  };

  const handleSubmitForm = () => {
    setIsSubmitted(true);
    handleSubmit();
  };

  const getRemoveButtonScale = (index: number) => {
    if (!removeButtonScales[index]) {
      removeButtonScales[index] = new Animated.Value(1);
    }
    return removeButtonScales[index];
  };

  const hasIncompleteDesignation = localDesignations.some(
    (item, index) => !isDesignationComplete(item, index),
  );

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#448600" />
      </View>
    );
  }

  return (
    <ScrollView
      ref={scrollViewRef}
      className="flex-1 bg-white"
      contentContainerStyle={{ paddingBottom: 20 }}
      showsVerticalScrollIndicator={false}
    >
      <View className="px-4">
        <View className="flex-row items-center justify-between py-4">
          <BackButton
            onBack={onBack}
            label={experienceId ? 'Edit Experience' : 'Create Experience'}
          />
          <Pressable onPress={handleSubmitForm} disabled={isSubmitting || !hasChanges}>
            <Text
              className={`text-lg font-medium ${
                isSubmitting || !hasChanges ? 'text-gray-400' : 'text-[#448600]'
              }`}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </Text>
          </Pressable>
        </View>

        <EntitySearch
          title="Company"
          placeholder="Enter Company ..."
          selectionKey="entity/organisation"
          data={entitySelection ? entitySelection.name : localEntity?.name || ''}
          error={
            isSubmitted && !entitySelection && !localEntity?.id ? 'Company is required' : undefined
          }
        />

        <Pressable onPress={handleAddDesignationClick} disabled={isAddingDesignation}>
          <View className="flex-row justify-between mt-4 items-center">
            <TextView subtitle="Designation Details" subtitleClassName="font-bold text-black" />
            <View className="flex-row items-center gap-2">
              {isAddingDesignation && <ActivityIndicator size="small" color="#448600" />}
              <Animated.View style={{ transform: [{ scale: addDesignationScale }] }}>
                <AddItem color={canAddDesignation() ? '#448600' : '#9CA3AF'} />
              </Animated.View>
            </View>
          </View>
        </Pressable>

        {localDesignations.map((item: any, actualIndex: number) => {
          const fromDateError = getFieldError('fromDate', actualIndex);
          const toDateError = getFieldError('toDate', actualIndex);
          const designationError = getFieldError('designation', actualIndex);
          const isRemoving = removingIndex === actualIndex;
          const designationSelection = designationSelections[actualIndex];
          const removeScale = getRemoveButtonScale(actualIndex);

          return (
            <View key={`${actualIndex}-${rerenderTrigger}-${item.id || 'new'}`}>
              <EntitySearch
                key={`search-${actualIndex}-${rerenderTrigger}`}
                title={`Designation ${actualIndex + 1}`}
                placeholder="Enter Designation"
                selectionKey={`designation-${actualIndex}`}
                data={designationSelection?.name || item?.designation?.name || ''}
                error={
                  isSubmitted && !designationSelection && !item?.designation?.id
                    ? 'Designation is required'
                    : designationError
                }
              />

              <View className="flex-row mb-6 mt-4">
                <View className="flex-1 mr-2">
                  <DatePicker
                    title="From"
                    selectedDate={item.fromDate || ''}
                    onDateChange={getDateChangeHandler(actualIndex, 'fromDate')}
                    showMonthYear={true}
                    className={fromDateError ? 'border-red-500' : ''}
                  />
                  {fromDateError && (
                    <Text className="text-red-500 text-xs mt-1">{fromDateError}</Text>
                  )}
                </View>

                <View className="flex-1 mr-2">
                  <DatePicker
                    title="To"
                    selectedDate={item.isPresent ? '' : item.toDate || ''}
                    onDateChange={getDateChangeHandler(actualIndex, 'toDate')}
                    showMonthYear={true}
                    disabled={item.isPresent}
                    className={toDateError ? 'border-red-500' : ''}
                  />
                  {toDateError && <Text className="text-red-500 text-xs mt-1">{toDateError}</Text>}
                  <Checkbox
                    label="Present"
                    className="pt-3"
                    labelClassName="text-base text-sm"
                    onValueChange={(checked) => handlePresentCheckbox(actualIndex, checked)}
                    checked={item.isPresent || false}
                  />
                </View>
              </View>

              <ShipItem
                field={
                  { ...item, designation: designationSelection || item.designation } as FieldTypeI
                }
                onAddEdit={handleAddEditShip}
                deleteShip={handleDeleteShip}
                isDeleting={isDeleting}
                disableAddShips={hasIncompleteDesignation}
              />

              <Animated.View style={{ transform: [{ scale: removeScale }] }}>
                <Pressable
                  onPress={() => handleRemoveDesignation(actualIndex)}
                  disabled={isRemoving}
                  className={`flex-row items-center self-end mb-4 gap-2 mt-2 ${
                    isRemoving ? 'opacity-50' : ''
                  }`}
                >
                  <DeleteIcon color="red" width={1.75} height={1.75} />
                  <Text className="text-red-500">{isRemoving ? 'Removing...' : 'Remove'}</Text>
                </Pressable>
              </Animated.View>

              {actualIndex < localDesignations.length - 1 && (
                <View className="border-b border-gray-200 my-6" />
              )}
            </View>
          );
        })}
      </View>

      <CustomModal
        isVisible={showDiscardModal}
        title="Discard changes?"
        description="You have unsaved changes. Are you sure you want to discard them and leave this screen?"
        onCancel={() => setShowDiscardModal(false)}
        onConfirm={handleDiscardConfirm}
        cancelText="Cancel"
        confirmText="Discard"
        confirmButtonVariant="danger"
      />
    </ScrollView>
  );
};

export default EditExperienceItem;
