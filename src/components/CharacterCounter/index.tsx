import { Text, View } from 'react-native';
import { CharacterCounterPropsI } from './types';

const CharacterCounter = ({ currentLength, maxLength }: CharacterCounterPropsI) => {
  const warningThreshold = maxLength * 0.9;
  return (
    <View className="flex-row justify-end">
      <View className="bg-gray-100 px-2 py-1 rounded-full">
        <Text
          className={`text-xs ${currentLength > warningThreshold ? 'text-orange-500' : 'text-gray-500'}`}
        >
          {currentLength}/{maxLength}
        </Text>
      </View>
    </View>
  );
};

export default CharacterCounter;
