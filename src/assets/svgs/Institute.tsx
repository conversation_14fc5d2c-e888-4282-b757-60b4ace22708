/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { View } from 'react-native';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from './types';

const Institute: React.FC<OutlinedIconPropsI> = ({
  width = 2,
  height = 2,
  color = '#4B5563',
  secondaryColor,
  containerStyle,
  strokeWidth = 1.5,
  accessibilityLabel = 'Institute icon',
  active = false,
  disabled = false,
  fill,
  stroke,
  ...props
}) => {
  const actualColor = disabled ? '#9CA3AF' : stroke || color;
  const actualFill = disabled ? '#9CA3AF' : fill || color;
  const actualStrokeWidth = active ? strokeWidth + 0.5 : strokeWidth;

  return (
    <View style={containerStyle} accessibilityLabel={accessibilityLabel}>
      <Svg
        width={RFPercentage(width)}
        height={RFPercentage(height)}
        viewBox="0 0 24 24"
        fill="none"
        {...props}
      >
        <Path
          d="M12 3L2 9L12 15L22 9L12 3Z"
          stroke={actualColor}
          strokeWidth={actualStrokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <Path
          d="M19 13V17.5C19 18.3284 15.866 19 12 19C8.13401 19 5 18.3284 5 17.5V13"
          stroke={actualColor}
          strokeWidth={actualStrokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <Path
          d="M22 9V17"
          stroke={actualColor}
          strokeWidth={actualStrokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <Path
          d="M22 17C22 17.5523 21.5523 18 21 18C20.4477 18 20 17.5523 20 17C20 16.4477 20.4477 16 21 16C21.5523 16 22 16.4477 22 17Z"
          fill={actualFill}
        />
      </Svg>
    </View>
  );
};

export default Institute;
