import { useEffect, useState, useRef } from 'react';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { selectMultipleSelectionsByKey } from '@/src/redux/selectors/search';
import { clearSelection } from '@/src/redux/slices/entitysearch/searchSlice';
import type { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import {
  addShipExperience,
  clearClientEquipmentsByShip,
  clearClientCargosByShip,
} from '@/src/redux/slices/experience/experienceSlice';
import type { ClientCargoI, ClientEquipmentI } from '@/src/redux/slices/experience/types';
import type { AppDispatch, RootState } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import type { ProfileStackParamsListI } from '@/src/navigation/types';
import { fetchShipDetails, fetchSingleShipDetails } from '@/src/networks/experienceShip.ts/ship';
import type { EquipmentI } from '@/src/networks/experienceShip.ts/types';
import { formatDateToYMD, generateShipAddEditPayload } from '../utils';
import type {
  FieldTypeI,
  ShipDetailsFormDataI,
  ShipPreFilledDataTypeI,
  UseEditShipItemI,
} from './types';

export const useEditShipItem = (
  preFilledData: ShipPreFilledDataTypeI[],
  field: FieldTypeI,
  shipId?: string,
  fromProfileExperience?: {
    id: string;
    imo: string;
  },
  refetch?: () => void,
): UseEditShipItemI => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();
  const [localSkills, setLocalSkills] = useState<SearchResultI[]>([]);
  const [initialSkills, setInitialSkills] = useState<SearchResultI[]>([]);
  const skillsSelection = useSelector(selectMultipleSelectionsByKey('skill'));
  const [loading, setLoading] = useState(false);
  const [isAddVisible, setIsAddVisible] = useState(false);
  const [equipments, setEquipments] = useState<EquipmentI[]>([]);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [shipTempId] = useState(() => shipId || `ship_${Date.now()}_${Math.random()}`);
  const [initialFormData, setInitialFormData] = useState<ShipDetailsFormDataI | null>(null);
  const [hasChanges, setHasChanges] = useState(false);
  const dataLoaded = useRef(false);
  const formSnapshot = useRef<string>('');
  const skillsSnapshot = useRef<string>('');

  const clientEquipments: ClientEquipmentI[] = useSelector((state: RootState) =>
    state.experience.clientEquipments.filter((eq) => eq.shipTempId === shipTempId),
  );
  const clientCargos: ClientCargoI[] = useSelector((state: RootState) =>
    state.experience.clientCargos.filter((c) => c.shipTempId === shipTempId),
  );

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const dispatch = useDispatch<AppDispatch>();
  const MAX_SKILLS = 6;

  const methods = useForm<ShipDetailsFormDataI>({
    mode: 'onChange',
    defaultValues: {
      imoNumber: { imo: '', dataType: 'raw' },
      shipName: '',
      shipSize: '',
      subVesselType: { id: '', name: '', dataType: 'raw' },
      grossTonnage: '',
      deadWeight: '',
      power: '',
      capacity: '',
      fromDate: '',
      toDate: '',
      additionalDetails: '',
      department: { id: '', name: '', dataType: 'raw' },
    },
  });

  const isSkillLimitReached = localSkills.length > MAX_SKILLS;

  const calculateChanges = () => {
    if (!initialFormData) return false;
    const current = methods.getValues();
    const imoChanged =
      current.imoNumber?.imo !== initialFormData.imoNumber?.imo ||
      current.imoNumber?.dataType !== initialFormData.imoNumber?.dataType;
    const shipTypeChanged =
      current.subVesselType?.id !== initialFormData.subVesselType?.id ||
      current.subVesselType?.name !== initialFormData.subVesselType?.name;
    const departmentChanged =
      current.department?.id !== initialFormData.department?.id ||
      current.department?.name !== initialFormData.department?.name;

    const formChanged =
      imoChanged ||
      shipTypeChanged ||
      departmentChanged ||
      current.shipName !== initialFormData.shipName ||
      current.grossTonnage !== initialFormData.grossTonnage ||
      current.deadWeight !== initialFormData.deadWeight ||
      current.power !== initialFormData.power ||
      current.fromDate !== initialFormData.fromDate ||
      current.toDate !== initialFormData.toDate ||
      current.additionalDetails !== initialFormData.additionalDetails;

    const skillsChanged =
      localSkills.length !== initialSkills.length ||
      localSkills.some((skill) => !initialSkills.find((initial) => initial.id === skill.id)) ||
      initialSkills.some((initial) => !localSkills.find((skill) => skill.id === initial.id));

    return formChanged || skillsChanged;
  };

  const checkRequiredFields = (formData: any) => {
    const requiredFieldsFilled =
      (formData.imoNumber?.imo?.trim() ?? '').length > 0 &&
      (formData.shipName?.trim() ?? '').length > 0 &&
      (formData.subVesselType?.name?.trim() ?? '').length > 0 &&
      (formData.grossTonnage?.trim() ?? '').length > 0 &&
      (formData.department?.name?.trim() ?? '').length > 0;

    setIsAddVisible(!!requiredFieldsFilled);
  };

  useEffect(() => {
    if (shipId && !dataLoaded.current) {
      const fetchData = async () => {
        try {
          setLoading(true);
          await fetchShip(shipId);
          dataLoaded.current = true;
        } catch (error) {
          triggerErrorBoundary(
            new Error(
              'Failed to load ship details: ' +
                (error instanceof Error ? error.message : 'Unknown error'),
            ),
          );
        } finally {
          setLoading(false);
        }
      };
      fetchData();
    } else if (!shipId && !dataLoaded.current) {
      const defaultFormData = {
        imoNumber: { imo: '', dataType: 'raw' },
        shipName: '',
        shipSize: '',
        subVesselType: { id: '', name: '', dataType: 'raw' },
        grossTonnage: '',
        deadWeight: '',
        power: '',
        capacity: '',
        fromDate: '',
        toDate: '',
        additionalDetails: '',
        department: { id: '', name: '', dataType: 'raw' },
      };
      setInitialFormData(defaultFormData as ShipDetailsFormDataI);
      dataLoaded.current = true;
    }
  }, [shipId]);

  useEffect(() => {
    if (!initialFormData) return;
    const subscription = methods.watch((formData) => {
      const currentFormSnapshot = JSON.stringify(formData);
      const currentSkillsSnapshot = JSON.stringify(localSkills);

      if (
        currentFormSnapshot !== formSnapshot.current ||
        currentSkillsSnapshot !== skillsSnapshot.current
      ) {
        formSnapshot.current = currentFormSnapshot;
        skillsSnapshot.current = currentSkillsSnapshot;
        setHasChanges(calculateChanges());
      }

      checkRequiredFields(formData);
    });
    return () => subscription.unsubscribe();
  }, [initialFormData, localSkills, methods]);

  const fetchShip = async (shipId: string) => {
    const response = await fetchSingleShipDetails(shipId);
    if (fromProfileExperience !== undefined) {
      const fetchedShip = {
        imoNumber: { imo: fromProfileExperience.imo, dataType: 'raw' },
        shipName: response.name,
        shipSize: '',
        subVesselType: response.subVesselType,
        grossTonnage: response.sizeGt,
        deadWeight: response?.dwt ? response?.dwt?.toString() : '',
        power: response.powerKw,
        capacity: '',
        fromDate: response.fromDate,
        toDate: response.toDate || null,
        additionalDetails: response?.details || '',
        department: response.department,
      };
      setLocalSkills((response.skills as unknown as SearchResultI[]) || []);
      setInitialSkills(response.skills);
      setInitialFormData(fetchedShip);
      methods.reset(fetchedShip);
      formSnapshot.current = JSON.stringify(fetchedShip);
      skillsSnapshot.current = JSON.stringify(response.skills || []);
      checkRequiredFields(fetchedShip);
    } else {
      const matchedShip = field.ships.find(
        (ship) =>
          ship.id === response.id &&
          formatDateToYMD(ship.fromDate) === formatDateToYMD(response.fromDate) &&
          formatDateToYMD(ship.toDate) === formatDateToYMD(response.toDate),
      );
      const imoNumber = matchedShip?.ship ?? null;
      const fetchedShip = {
        imoNumber: imoNumber!,
        shipName: response.name,
        shipSize: '',
        subVesselType: response.subVesselType,
        grossTonnage: response.sizeGt,
        deadWeight: response?.dwt ? response?.dwt.toString() : '',
        power: response.powerKw,
        capacity: '',
        fromDate: response.fromDate,
        toDate: response.toDate || null,
        additionalDetails: response?.details || '',
        department: response.department,
      };
      setLocalSkills(response.skills?.length > 0 ? response.skills : []);
      setInitialSkills(response.skills?.length > 0 ? response.skills : []);
      setInitialFormData(fetchedShip);
      methods.reset(fetchedShip);
      formSnapshot.current = JSON.stringify(fetchedShip);
      skillsSnapshot.current = JSON.stringify(response.skills?.length > 0 ? response.skills : []);
      checkRequiredFields(fetchedShip);
    }
    setEquipments(response.equipments);
  };

  const shipReFetch = () => {
    fetchShip(shipId!);
  };

  useEffect(() => {
    if (!skillsSelection) return;
    setLocalSkills((prev) => {
      const existingIds = new Set(prev.map((s) => s.id));
      const merged = [...prev, ...skillsSelection.filter((s) => !existingIds.has(s.id))];
      return merged;
    });
  }, [skillsSelection, dispatch]);

  useEffect(() => {
    if (!initialFormData) return;
    const currentSkillsSnapshot = JSON.stringify(localSkills);
    if (currentSkillsSnapshot !== skillsSnapshot.current) {
      skillsSnapshot.current = currentSkillsSnapshot;
      setHasChanges(calculateChanges());
    }
  }, [localSkills, initialFormData]);

  const onSubmit = async (data: ShipDetailsFormDataI) => {
    if (isSkillLimitReached) {
      showToast({
        message: 'Skills Limit Reached',
        description: `Max ${MAX_SKILLS} skills allowed`,
        type: 'error',
      });
      return;
    }
    const payload = generateShipAddEditPayload(
      preFilledData,
      data,
      shipId,
      field,
      localSkills,
      initialSkills,
      clientEquipments,
      clientCargos,
    );
    try {
      setIsSubmitting(true);
      const response = (await dispatch(
        addShipExperience({ payload }),
      ).unwrap()) as unknown as string[];
      showToast({
        message: 'Success',
        description: `${shipId ? 'Updated' : 'Added'} Ship successfully`,
        type: 'success',
      });
      clearFields();
      if (payload[0].opr === 'CREATE') {
        navigation.popTo('EditExperienceItem', { experienceId: response[0] });
      } else {
        navigation.goBack();
      }
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Failed to Save Ship',
            description: 'Unable to save Ship',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
          });
        },
      });
    } finally {
      setIsSubmitting(false);
      if (refetch) {
        refetch();
      }
      setIsSubmitted(false);
    }
  };

  const populateData = async (imoData: { imo: string; dataType: string }) => {
    try {
      const response = await fetchShipDetails({
        imo: imoData.imo,
        dataType: imoData.dataType,
      });
      if (response.subVesselType.id) {
        methods.setValue('subVesselType', response.subVesselType);
      }
      methods.setValue('grossTonnage', response.gt.toString());
      methods.setValue('deadWeight', response.dwt.toString());
      const currentFormData = methods.getValues();
      checkRequiredFields(currentFormData);
    } catch (error) {}
  };

  const handlePresentCheckbox = () => {
    const currentToDate = methods.watch('toDate');
    if (currentToDate === null) {
      methods.setValue('toDate', '');
    } else {
      methods.setValue('toDate', null);
    }
    methods.trigger('toDate');
  };

  const handleAddEquipmentCargo = () => {
    const payload = generateShipAddEditPayload(
      preFilledData,
      methods.watch(),
      shipId,
      field,
      localSkills,
      initialSkills,
      clientEquipments,
      clientCargos,
    );
    return payload;
  };

  const clearFields = () => {
    dispatch(clearSelection('ship'));
    dispatch(clearSelection('subVesselType'));
    dispatch(clearSelection('department'));
    dispatch(clearSelection('skill'));
    setLocalSkills([]);
    if (!shipId) {
      dispatch(clearClientEquipmentsByShip(shipTempId));
      dispatch(clearClientCargosByShip(shipTempId));
    }
  };

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', () => {
      clearFields();
    });
    return unsubscribe;
  }, [navigation]);

  return {
    methods,
    isSubmitting,
    onSubmit,
    navigation,
    localSkills,
    setLocalSkills,
    handlePresentCheckbox,
    populateData,
    isAddVisible,
    clearFields,
    handleAddEquipmentCargo,
    equipments,
    setEquipments,
    shipReFetch,
    loading,
    isSubmitted,
    setIsSubmitted,
    hasChanges,
    clientEquipments,
    clientCargos,
    shipTempId,
    isSkillLimitReached,
    MAX_SKILLS,
  };
};
