/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import EncryptedStorage from 'react-native-encrypted-storage';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { PrimitiveI, StringUndefinedNullI } from '@/src/types/common/data';
import { StorageKeyI } from './types';

const useStorage = () => {
  const getStorage = async (key: StorageKeyI): Promise<StringUndefinedNullI> =>
    await EncryptedStorage.getItem(key);

  const setStorage = async (key: StorageKeyI, value: PrimitiveI): Promise<void> =>
    await EncryptedStorage.setItem(key, typeof value === 'string' ? value : String(value));

  const removeStorage = async (key: StorageKeyI): Promise<boolean> => {
    if (await EncryptedStorage.getItem(key)) {
      await EncryptedStorage.removeItem(key);
      return true;
    }
    return false;
  };
  const clearAllStorage = async (): Promise<void> => {
    await Promise.all((['deviceToken', 'token'] as StorageKeyI[]).map(removeStorage));

    await AsyncStorage.clear();
  };

  const hasVisitedOnboarding = async (): Promise<boolean> => {
    const visitedOnboarding = await getStorage('visitedOnboarding');
    return visitedOnboarding === 'true';
  };
  return {
    getStorage,
    setStorage,
    clearAllStorage,
    hasVisitedOnboarding,
  };
};
export default useStorage;
