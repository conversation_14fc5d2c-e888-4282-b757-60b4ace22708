import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../store';

export const selectForumState = (state: RootState) => state.forum;

export const selectForumQuestion = createSelector(
  [selectForumState],
  (forumState) => forumState.question,
);

export const selectForumLoading = createSelector(
  [selectForumState],
  (forumState) => forumState.loading,
);

export const selectForumError = createSelector(
  [selectForumState],
  (forumState) => forumState.error,
);

export const selectForumAnswers = createSelector(
  [selectForumState],
  (forumState) => forumState.answers,
);

export const selectForumAnswersTotal = createSelector(
  [selectForumState],
  (forumState) => forumState.total,
);

export const selectForumAnswersNextCursorId = createSelector(
  [selectForumState],
  (forumState) => forumState.nextCursorId,
);

export const selectForumAnswerLoading = createSelector(
  [selectForumState],
  (forumState) => forumState.answerLoading,
);

export const selectForumAnswerError = createSelector(
  [selectForumState],
  (forumState) => forumState.answerError,
);

export const selectForumAnswerDetail = createSelector(
  [selectForumState],
  (forumState) => forumState.answerDetail,
);

export const selectAnswerVotes = (state: RootState) => state.forum.answerVotes;
export const selectAnswerVotesTotal = (state: RootState) => state.forum.answerVotesTotal;
export const selectAnswerVotesNextCursorId = (state: RootState) =>
  state.forum.answerVotesNextCursorId;
export const selectAnswerVoteLoading = (state: RootState) => state.forum.answerVoteLoading;
export const selectAnswerVoteError = (state: RootState) => state.forum.answerVoteError;

export const selectForumComments = createSelector(
  [selectForumState, (state: RootState, questionId: string) => questionId],
  (forumState, questionId) => {
    return forumState.comments[questionId] || { comments: [], total: 0, nextCursorId: null };
  },
);

export const selectForumCommentReplies = createSelector(
  [selectForumState, (state: RootState, parentCommentId: string) => parentCommentId],
  (forumState, parentCommentId) => {
    return (
      forumState.commentReplies[parentCommentId] || { comments: [], total: 0, nextCursorId: null }
    );
  },
);

export const selectForumAnswerComments = createSelector(
  [selectForumState, (state: RootState, answerId: string) => answerId],
  (forumState, answerId) => {
    return forumState.answerComments[answerId] || { comments: [], total: 0, nextCursorId: null };
  },
);

export const selectForumAnswerCommentReplies = createSelector(
  [selectForumState, (state: RootState, parentCommentId: string) => parentCommentId],
  (forumState, parentCommentId) => {
    return (
      forumState.answerCommentReplies[parentCommentId] || {
        comments: [],
        total: 0,
        nextCursorId: null,
      }
    );
  },
);
