import { ActivityIndicator, Pressable, Text, View } from 'react-native';
import CustomModal from '@/src/components/Modal';
import AddItem from '@/src/assets/svgs/AddItem';
import EditPencil from '@/src/assets/svgs/EditPencil';
import DeleteIcon from '@/src/assets/svgs/TrashBin';
import { ShipEquipmentI } from '../EditShipItem/types';
import { EquipmentPropsI } from './types';
import { useEquipment } from './useHook';

const Equipment = ({
  isAddVisible,
  handleAdd,
  equipments,
  fromProfileExperience,
  refetch,
  shipId,
  clientEquipments = [],
  shipTempId,
}: EquipmentPropsI & {
  clientEquipments?: any[];
  shipTempId?: string;
}) => {
  const {
    handleAddEditEquipment,
    handleDelete,
    shipEquipments,
    isVisible,
    isDeleting,
    setIsVisible,
    loading,
    setDeleteEquimentId,
    handleClientAdd,
    handleClientEdit,
    handleClientDelete,
    deleteEquipmentId,
  } = useEquipment(equipments, refetch, shipId, shipTempId);

  const displayEquipments = shipId ? shipEquipments : clientEquipments;

  const renderEquipmentItem = (item: ShipEquipmentI | any) => {
    return (
      <View key={item.id || item.tempId} className="py-3">
        <Text className="text-base font-inter font-medium">{`${item.category?.name || item.equipmentCategory?.name} | ${item.manufacturerName}`}</Text>
        <View className="flex-row item-center justify-between">
          <Text>{`${item.model}`}</Text>
          {!fromProfileExperience && (
            <View className="flex-row items-center gap-4">
              <Pressable
                onPress={() => {
                  if (shipId) {
                    const payload = handleAdd();
                    handleAddEditEquipment(payload, item.id);
                  } else {
                    handleClientEdit(item.tempId);
                  }
                }}
              >
                <EditPencil width={2} height={2} />
              </Pressable>
              <Pressable
                onPress={() => {
                  setDeleteEquimentId(item.id || item.tempId);
                  setIsVisible(true);
                }}
              >
                <DeleteIcon width={2} height={2} />
              </Pressable>
            </View>
          )}
        </View>
      </View>
    );
  };

  if (loading) {
    return <ActivityIndicator size="small" color={'#448600'} className="pt-3" />;
  }

  return (
    <>
      {displayEquipments && displayEquipments.map((item) => renderEquipmentItem(item))}
      {isAddVisible && !fromProfileExperience && (
        <Pressable
          className="flex-row items-center gap-5 py-5"
          onPress={() => {
            if (shipId) {
              const payload = handleAdd();
              handleAddEditEquipment(payload);
            } else {
              handleClientAdd();
            }
          }}
        >
          <AddItem />
          <Text className="text-[#448600] text-lg font-medium">New Equipment</Text>
        </Pressable>
      )}
      {!isAddVisible && !fromProfileExperience && (
        <View className="py-8">
          <Text className="text-center text-gray-500 text-base mb-2">
            Complete ship details to add equipment
          </Text>
          <Text className="text-center text-gray-400 text-sm">
            Fill in IMO Number, Ship Name, Ship Type, Gross Tonnage, and Department
          </Text>
        </View>
      )}
      <CustomModal
        isVisible={isVisible}
        onCancel={() => setIsVisible(false)}
        title="Are you sure you want to delete this equipment?"
        confirmText="Delete"
        confirmButtonVariant="danger"
        onConfirm={() => {
          if (shipId) {
            const payload = handleAdd();
            handleDelete(payload);
          } else {
            handleClientDelete(deleteEquipmentId!);
          }
        }}
        isConfirming={isDeleting}
      />
    </>
  );
};

export default Equipment;
