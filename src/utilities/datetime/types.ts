/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
export type DateFormat = 'short' | 'long' | string;
export type TimeFormat = 'short' | 'long' | string;
export interface RelativeTimeOptions {
  withoutSuffix?: boolean;
  threshold?: number;
}
export type DateRange = 'today' | 'yesterday' | 'week' | 'month' | 'year' | 'custom';
export interface CalendarConfig {
  sameDay: string;
  nextDay: string;
  nextWeek: string;
  lastDay: string;
  lastWeek: string;
  sameElse: string;
}
export interface DateTimeConfig {
  shortFormat: string;
  longFormat: string;
  timeFormat: string;
  dateTimeFormat: string;
  calendar: CalendarConfig;
}
