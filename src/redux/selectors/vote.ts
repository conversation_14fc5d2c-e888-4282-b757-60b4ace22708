/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { createSelector } from '@reduxjs/toolkit';
import type { VoteStateI } from '../slices/vote/types';
import type { RootState } from '../store';

export const selectVoteState = (state: RootState): VoteStateI => state.vote;

export const selectQuestionVotes = createSelector(
  [selectVoteState],
  (voteState) => voteState.questionVotes,
);

export const selectAnswerVotes = createSelector(
  [selectVoteState],
  (voteState) => voteState.answerVotes,
);

export const selectQuestionVoteLoading = createSelector(
  [selectVoteState],
  (voteState) => voteState.questionVoteLoading,
);

export const selectAnswerVoteLoading = createSelector(
  [selectVoteState],
  (voteState) => voteState.answerVoteLoading,
);

export const selectVoteError = createSelector([selectVoteState], (voteState) => voteState.error);

export const selectQuestionVoteByQuestionId = (questionId: string) =>
  createSelector([selectQuestionVotes], (questionVotes) => questionVotes[questionId] || null);

export const selectAnswerVoteByAnswerId = (answerId: string) =>
  createSelector([selectAnswerVotes], (answerVotes) => answerVotes[answerId] || null);

export const selectQuestionVoteLoadingByQuestionId = (questionId: string) =>
  createSelector([selectQuestionVoteLoading], (loading) => loading[questionId] || false);

export const selectAnswerVoteLoadingByAnswerId = (answerId: string) =>
  createSelector([selectAnswerVoteLoading], (loading) => loading[answerId] || false);
