import { ActivityIndicator, Pressable, Text, View } from 'react-native';
import CustomModal from '@/src/components/Modal';
import AddItem from '@/src/assets/svgs/AddItem';
import EditPencil from '@/src/assets/svgs/EditPencil';
import DeleteIcon from '@/src/assets/svgs/TrashBin';
import { toMonthYear } from '../utils';
import type { CargoPropsI, ShipCargoI } from './types';
import { useCargo } from './useHook';

const Cargo = ({
  shipId,
  isAddVisible,
  handleAdd,
  fromProfileExperience,
  shipData,
  clientCargos = [],
  shipTempId,
}: CargoPropsI) => {
  const {
    cargos,
    handleAddEditCargo,
    loading,
    handleDelete,
    isDeleting,
    isVisible,
    setIsVisible,
    setDeleteCargoId,
    handleClientAdd,
    handleClientEdit,
    handleClientDelete,
    deleteCargoId,
  } = useCargo(shipId, shipData, shipTempId);

  const displayCargos = shipId ? cargos : clientCargos;

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center py-10">
        <ActivityIndicator size="small" color={'#448600'} />
      </View>
    );
  }

  const renderCargoItem = (item: ShipCargoI | any) => (
    <View key={item.id || item.tempId} className="py-3 pb-3">
      <Text className="text-base font-inter font-medium">{item.name}</Text>
      <View className="flex-row item-center justify-between">
        <Text className="text-base font-inter ">{`${toMonthYear(item.fromDate)} - ${item.toDate ? toMonthYear(item.toDate) : 'Present'}`}</Text>
        {!fromProfileExperience && (
          <View className="flex-row items-center gap-4">
            <Pressable
              onPress={() => {
                if (shipId) {
                  const payload = handleAdd();
                  handleAddEditCargo(payload, item.id);
                } else {
                  handleClientEdit(item.tempId);
                }
              }}
            >
              <EditPencil width={2} height={2} />
            </Pressable>
            <Pressable
              onPress={() => {
                setDeleteCargoId(item.id || item.tempId);
                setIsVisible(true);
              }}
            >
              <DeleteIcon width={2} height={2} />
            </Pressable>
          </View>
        )}
      </View>
    </View>
  );

  return (
    <>
      {displayCargos && displayCargos.map((item) => renderCargoItem(item))}
      {isAddVisible && !fromProfileExperience && (
        <View>
          <Pressable
            className="flex-row items-center gap-5 py-5"
            onPress={() => {
              if (shipId) {
                const payload = handleAdd();
                handleAddEditCargo(payload);
              } else {
                handleClientAdd();
              }
            }}
          >
            <AddItem />
            <Text className="text-[#448600] text-lg font-medium">New Cargo</Text>
          </Pressable>
        </View>
      )}
      {!isAddVisible && !fromProfileExperience && (
        <View className="py-8">
          <Text className="text-center text-gray-500 text-base mb-2">
            Complete ship details to add cargo
          </Text>
          <Text className="text-center text-gray-400 text-sm">
            Fill in IMO Number, Ship Name, Ship Type, Gross Tonnage, and Department
          </Text>
        </View>
      )}
      <CustomModal
        isVisible={isVisible}
        onCancel={() => setIsVisible(false)}
        title="Are you sure you want to delete this cargo?"
        confirmText="Delete"
        confirmButtonVariant="danger"
        onConfirm={() => {
          if (shipId) {
            const payload = handleAdd();
            handleDelete(payload);
          } else {
            handleClientDelete(deleteCargoId!);
          }
        }}
        isConfirming={isDeleting}
      />
    </>
  );
};

export default Cargo;
