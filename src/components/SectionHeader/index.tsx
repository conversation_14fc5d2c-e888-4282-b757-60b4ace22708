import React from 'react';
import { View } from 'react-native';
import { twMerge } from 'tailwind-merge';
import TextView from '../TextView';
import { SectionHeaderProps } from './types';

export const SectionHeader: React.FC<SectionHeaderProps> = ({
  title,
  icon,
  containerClassName = '',
}) => {
  const Icon = icon;
  return (
    <View className={twMerge(`flex-row items-center gap-6`, containerClassName)}>
      {Icon && <Icon />}
      <TextView title={title} titleClassName="font-medium text-lg text-black" />
    </View>
  );
};
