/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { searchPeopleGlobalAPI } from '@/src/networks/connect/search';
import { searchPostGlobalAPI } from '@/src/networks/content/post';
import {
  searchInstituteGlobalAPI,
  searchOrganizationGlobalAPI,
} from '@/src/networks/entitySearch/optionSearch';
import { searchPortGlobalAPI } from '@/src/networks/port/search';
import { searchShipGlobalAPI } from '@/src/networks/ship/search';
import { GlobalSearchCategory, GlobalSearchParams, GlobalSearchResponse } from './types';

export const fetchGlobalSearchResults = async (
  category: GlobalSearchCategory,
  search: string,
  page: number,
  pageSize?: number,
): Promise<GlobalSearchResponse | undefined> => {
  const params: GlobalSearchParams = { search, page, pageSize };

  switch (category) {
    case 'people':
      return await searchPeopleGlobalAPI(params);
    case 'organization':
      return await searchOrganizationGlobalAPI(params);
    case 'institution':
      return await searchInstituteGlobalAPI(params);
    case 'post':
      return await searchPostGlobalAPI(params);
    case 'ship':
      return await searchShipGlobalAPI(params);
    case 'port':
      return await searchPortGlobalAPI(params);
    default:
      return;
  }
};
