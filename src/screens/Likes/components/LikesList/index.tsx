/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { View, ActivityIndicator } from 'react-native';
import UsersList from '@/src/components/UsersList';
import { ListItem } from '@/src/components/UsersList/types';
import { LikesListProps } from './types';
import useLikesList from './useHook';

const LikesList: React.FC<LikesListProps> = ({ postId, onUserPress }) => {
  const { reactions, loading, refreshing, handleRefresh, handleLoadMore } = useLikesList(postId);

  const formattedUsers: ListItem[] = reactions.reactions?.map((reaction) => ({
    Profile: {
      id: reaction.Profile.id,
      avatar: reaction.Profile.avatar,
      name: reaction.Profile.name,
      designation: reaction.Profile.designation,
      entity: reaction.Profile.entity,
    },
  }));

  if (loading && formattedUsers?.length === 0) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="small" />
      </View>
    );
  }

  return (
    <View className="flex-1">
      <UsersList
        data={formattedUsers}
        onPress={onUserPress}
        refreshing={refreshing}
        loading={loading}
        onRefresh={handleRefresh}
        onLoadMore={handleLoadMore}
      />
    </View>
  );
};

export default LikesList;
