import { useEffect, useState } from 'react';
import { showToast } from '@/src/utilities/toast';
import { fetchPrivacyPolicyAPI } from '@/src/networks/profile/privacyPolicy';

export const usePrivacyPolicy = () => {
  const [policyContent, setPolicyContent] = useState('');
  const [termsVisible, setTermsVisible] = useState(false);
  const [isValid, setIsValid] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchPrivacyPolicy = async () => {
      try {
        setLoading(true);
        const result = await fetchPrivacyPolicyAPI({ type: 'SIGNUP_PRIVACY_POLICY' });
        if (result) {
          setPolicyContent(result.content);
        }
      } catch (error) {
        showToast({
          message: 'Error',
          type: 'error',
        });
      } finally {
        setLoading(false);
      }
    };
    fetchPrivacyPolicy();
  }, []);

  return {
    policyContent,
    termsVisible,
    setTermsVisible,
    isValid,
    setIsValid,
    loading,
  };
};
