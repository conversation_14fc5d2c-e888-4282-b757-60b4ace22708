/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { createSlice } from '@reduxjs/toolkit';
import { RecentSearchesState } from './types';

const initialState: RecentSearchesState = {
  recentSearches: [],
};

const globalSearchSlice = createSlice({
  name: 'globalSearch',
  initialState,
  reducers: {
    addSearch: (state, action) => {
      const { category, searchText } = action.payload;
      if (!searchText) return;
      state.recentSearches = state.recentSearches.filter(
        (item) => !(item.searchText === searchText && item.category === category),
      );
      state.recentSearches.unshift({ category, searchText });
      if (state.recentSearches.length > 5) {
        state.recentSearches.pop();
      }
    },
  },
});

export const { addSearch } = globalSearchSlice.actions;
export default globalSearchSlice.reducer;
