/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const TopBarChat: React.FC<FilledIconPropsI> = ({
  width = 3.1,
  height = 3.1,
  color = '#404040',
  ...props
}) => {
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 25 25"
      fill="none"
      {...props}
    >
      <Path
        d="M17.5 11C17.5 11.2652 17.3946 11.5196 17.2071 11.7071C17.0196 11.8946 16.7652 12 16.5 12H8C7.73478 12 7.48043 11.8946 7.29289 11.7071C7.10536 11.5196 7 11.2652 7 11C7 10.7348 7.10536 10.4804 7.29289 10.2929C7.48043 10.1054 7.73478 10 8 10H16.5C16.7652 10 17.0196 10.1054 17.2071 10.2929C17.3946 10.4804 17.5 10.7348 17.5 11ZM16.5 14H8C7.73478 14 7.48043 14.1054 7.29289 14.2929C7.10536 14.4804 7 14.7348 7 15C7 15.2652 7.10536 15.5196 7.29289 15.7071C7.48043 15.8946 7.73478 16 8 16H16.5C16.7652 16 17.0196 15.8946 17.2071 15.7071C17.3946 15.5196 17.5 15.2652 17.5 15C17.5 14.7348 17.3946 14.4804 17.2071 14.2929C17.0196 14.1054 16.7652 14 16.5 14ZM25 12.5C24.9964 15.8141 23.6782 18.9914 21.3348 21.3348C18.9914 23.6782 15.8141 24.9964 12.5 25H2C1.46957 25 0.960859 24.7893 0.585786 24.4142C0.210714 24.0391 0 23.5304 0 23V12.5C4.94004e-08 9.18479 1.31696 6.00537 3.66117 3.66116C6.00537 1.31696 9.18479 0 12.5 0C15.8152 0 18.9946 1.31696 21.3388 3.66116C23.683 6.00537 25 9.18479 25 12.5ZM23 12.5C23 9.71523 21.8938 7.04451 19.9246 5.07538C17.9555 3.10625 15.2848 2 12.5 2C9.71523 2 7.04451 3.10625 5.07538 5.07538C3.10625 7.04451 2 9.71523 2 12.5V23H12.5C15.2839 22.997 17.9528 21.8898 19.9213 19.9213C21.8898 17.9528 22.997 15.2839 23 12.5Z"
        fill={color}
      />
    </Svg>
  );
};

export default TopBarChat;
