/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
module.exports = {
  presets: ['module:@react-native/babel-preset', 'nativewind/babel'],
  plugins: [
    [
      'module-resolver',
      {
        root: ['./src'],
        alias: {
          '@': './',
        },
        extensions: ['.js', '.jsx', '.ts', '.tsx', '.svg'],
      },
    ],
    'react-native-reanimated/plugin',
    ...(process.env.NODE_ENV === 'production' ? [['transform-remove-console']] : []),
    '@babel/plugin-transform-export-namespace-from',
  ],
};
