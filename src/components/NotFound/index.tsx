/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { Image, Text, View } from 'react-native';
import { twMerge } from 'tailwind-merge';
import { NotFoundProps } from './types';

const NotFound = ({
  title,
  subtitle,
  className,
  titleClassName = 'font-semibold',
  imageStyle,
  fullScreen = true,
}: NotFoundProps) => {
  return (
    <View
      className={twMerge(` ${fullScreen ? 'flex-1' : ''} items-center justify-center`, className)}
    >
      <Image
        source={require('@/src/assets/images/others/notfound.png')}
        style={[{ width: '50%', height: 192 }, imageStyle]}
        resizeMode="contain"
      />
      {title && (
        <Text className={twMerge('text-xl text-gray-800 mt-6', titleClassName)}>{title}</Text>
      )}
      {subtitle && (
        <Text className="text-gray-500 text-base mt-2 text-center px-6">{subtitle}</Text>
      )}
    </View>
  );
};

export default NotFound;
