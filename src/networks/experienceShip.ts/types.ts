import { SearchResultI } from '@/src/redux/slices/entitysearch/types';

export type fetchShipDetailsBodyI = {
  imo: string;
  dataType: string;
};

export type fetchShipDetailsResultI = {
  beam: number;
  dataType: string;
  dwt: number;
  gt: number;
  imo: string;
  length: number;
  name: string;
  subVesselType: SearchResultI;
};

export type FetchShipCargosResultI = {
  id: string;
  name: string;
  description: string;
  fromDate: string;
  toDate: string;
};

export type FetchShipCargosQueryI = {
  experienceShipId: string;
};

export type fetchSingleShipDetailsResultI = {
  department: SearchResultI;
  details: string | null;
  dwt: number;
  fromDate: string;
  id: string;
  name: string;
  powerKw: string;
  sizeGt: string;
  subVesselType: SearchResultI;
  toDate: string;
  skills: SearchResultI[];
  equipments: EquipmentI[];
};

export type EquipmentI = {
  category: SearchResultI;
  id: string;
  manufacturerName: string;
  model: string;
};

export type fetchSingleShipEquipmentCategoryResultI = {
  details?: string;
  equipmentCategory: SearchResultI & { hasFuelType: boolean };
  fuelTypes: SearchResultI[];
  id: string;
  manufacturerName: string;
  model: string;
  powerCapacity: string;
};

export type fetchSingleShipCargoResultI = {
  description: string;
  fromDate: string;
  id: string;
  name: string;
  toDate: string;
};
