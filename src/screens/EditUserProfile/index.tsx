import { useNavigation } from '@react-navigation/native';
import SafeArea from '@/src/components/SafeArea';
import { BottomTabNavigationI } from '@/src/navigation/types';
import EditUserProfileForm from './components/EditUserProfileForm';

const EditUserProfile = () => {
  const navigation = useNavigation<BottomTabNavigationI>();

  return (
    <SafeArea>
      <EditUserProfileForm onBack={() => navigation.goBack()} />
    </SafeArea>
  );
};

export default EditUserProfile;
