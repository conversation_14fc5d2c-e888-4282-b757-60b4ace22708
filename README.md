B2C Frontend is a React Native application designed as a networking platform for mariners

# Setup & Installation

1. Clone the repository:
   ```sh
   git clone https://github.com/navicater/b2c-frontend.git
   cd b2c-frontend
   ```
2. Install dependencies:
   ```sh
   npm install
   ```
3. Create a `.env` file. (Refer `.env.example`)
4. Start the metro bundler in a terminal
   ```sh
   npm run start
   ```
5. Start the application:

   i. For ios

   ```sh
      cd ios
      pod install
      cd ..
      npm run ios
   ```

   ii. For android

   ```sh
   npm run android
   ```

Contribute!

# Merge & Release

## Branches

| Branch        | Description                          |
| ------------- | ------------------------------------ |
| dev           | Active development by all developers |
| main          | Stable release-ready code            |
| android-alpha | Triggers Android Alpha CI/CD build   |
| ios-alpha     | Triggers iOS Alpha CI/CD build       |
| android-prod  | Triggers Android production release  |
| ios-prod      | Triggers iOS production release      |

### Alpha testing

Raise your PR from dev to the android-alpha or ios-alpha branch

### Production release

Raise your PR from android-alpha to the android-prod or from ios-alpha to the ios-prod

Once the app is stable on production then merge it with the main branch
