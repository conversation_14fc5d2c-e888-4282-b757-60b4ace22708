import { fetchDocumentsResultI } from '@/src/networks/career/types';

/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
export interface DocumentItemI {
  country: {
    name: string;
  };
  expiryStatus: string;
  fromDate: string;
  untilDate: string;
  id: string;
  fileUrl?: string;
  type?: string;
}

export interface EditDocumentsListPropsI {
  onBack: () => void;
  profileId: string;
  onAdd: (type: string) => void;
  editable: boolean;
  tab: 'identity' | 'visa';
}

export interface IdentityDocumentationI {
  country: {
    name: string;
  };
  expiryStatus: string;
  fromDate: string;
  untilDate: string;
  id: string;
  type: string;
}

export interface VisaDocumentationI {
  country: {
    name: string;
  };
  expiryStatus: string;
  fromDate: string;
  untilDate: string;
  id: string;
  fileUrl: string;
}

export interface fetchedDocumentations {
  identityDocuments: IdentityDocumentationI[];
  visaDocuments: VisaDocumentationI[];
}

export type IdentityI = {
  country: {
    name: string;
  };
  expiryStatus?: string;
  fromDate: string;
  untilDate: string;
  id: string;
  type: string;
};

export type VisaI = {
  country: {
    name: string;
  };
  expiryStatus: string;
  fromDate: string;
  untilDate: string;
  id: string;
  fileUrl: string;
  name: string;
  type?: string;
};

export type EditDocumentTabsI = {
  identity: React.ReactNode;
  visa: React.ReactNode;
};
