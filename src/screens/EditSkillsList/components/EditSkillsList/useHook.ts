import { useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import { selectMultipleSelectionsByKey } from '@/src/redux/selectors/search';
import { clearMultipleSelections } from '@/src/redux/slices/entitysearch/searchSlice';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import {
  addSkills,
  deleteSkillsAPI,
  fetchMaritimeSkillsAPI,
  fetchOtherSkillsAPI,
} from '@/src/networks/career/skills';
import type { SkillI, UseEditSkillsListI } from './types';

export const useEditSkillsList = (profileId: string, category: string): UseEditSkillsListI => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [maritimeSkills, setMaritimeSkills] = useState<SkillI[]>([]);
  const [otherSkills, setOtherSkills] = useState<SkillI[]>([]);
  const [initialMaritimeSkills, setInitialMaritimeSkills] = useState<SkillI[]>([]);
  const [initialOtherSkills, setInitialOtherSkills] = useState<SkillI[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [maritimePage, setMaritimePage] = useState(1);
  const [otherPage, setOtherPage] = useState(1);
  const [hasMoreMaritime, setHasMoreMaritime] = useState(true);
  const [hasMoreOther, setHasMoreOther] = useState(true);
  const [activeTab, setActiveTab] = useState(category ?? 'maritimeSkills');
  const [error, setError] = useState<Error | null>(null);
  const pageSize = 10;

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };
  if (error) {
    throw error;
  }

  const skillsSelection = useSelector(
    selectMultipleSelectionsByKey('skill'),
  ) as unknown as SkillI[];
  const dispatch = useDispatch();
  const navigation = useNavigation();

  const fetchInitialSkills = async () => {
    try {
      setLoading(true);
      const [maritimeResponse, otherResponse] = await Promise.all([
        fetchMaritimeSkillsAPI(profileId, 0, pageSize),
        fetchOtherSkillsAPI(profileId, 0, pageSize),
      ]);
      setMaritimeSkills(maritimeResponse.skills);
      setOtherSkills(otherResponse.skills);
      setInitialMaritimeSkills(maritimeResponse.skills);
      setInitialOtherSkills(otherResponse.skills);
      setHasMoreMaritime(maritimeResponse.hasMore);
      setHasMoreOther(otherResponse.hasMore);
      setMaritimePage(1);
      setOtherPage(1);
    } catch (error) {
      triggerErrorBoundary(
        new Error(
          'Failed to load skills: ' + (error instanceof Error ? error.message : 'Unknown error'),
        ),
      );
    } finally {
      setLoading(false);
    }
  };

  const loadMoreMaritime = async () => {
    if (!hasMoreMaritime || loadingMore) return;
    try {
      setLoadingMore(true);
      const nextPage = maritimePage + 1;
      const response = await fetchMaritimeSkillsAPI(profileId, nextPage, pageSize);
      setMaritimeSkills((prev) => [...prev, ...response.skills]);
      setHasMoreMaritime(response.hasMore);
      setMaritimePage(nextPage);
    } catch (error) {
      triggerErrorBoundary(
        new Error(
          'Failed to load more maritime skills: ' +
            (error instanceof Error ? error.message : 'Unknown error'),
        ),
      );
    } finally {
      setLoadingMore(false);
    }
  };

  const loadMoreOther = async () => {
    if (!hasMoreOther || loadingMore) return;
    try {
      setLoadingMore(true);
      const nextPage = otherPage + 1;
      const response = await fetchOtherSkillsAPI(profileId, nextPage, pageSize);
      setOtherSkills((prev) => [...prev, ...response.skills]);
      setHasMoreOther(response.hasMore);
      setOtherPage(nextPage);
    } catch (error) {
      triggerErrorBoundary(
        new Error(
          'Failed to load more other skills: ' +
            (error instanceof Error ? error.message : 'Unknown error'),
        ),
      );
    } finally {
      setLoadingMore(false);
    }
  };

  useEffect(() => {
    fetchInitialSkills();
  }, [profileId]);

  useEffect(() => {
    if (!skillsSelection) return;
    const maritimeSelections = skillsSelection.filter((s) => s.category === 'MARITIME');
    const otherSelections = skillsSelection.filter((s) => s.category === 'OTHER');

    setMaritimeSkills((prev) => {
      const existingIds = new Set(prev.map((s) => s.id));
      const merged = [...prev, ...maritimeSelections.filter((s) => !existingIds.has(s.id))];
      return merged;
    });
    setOtherSkills((prev) => {
      const existingIds = new Set(prev.map((s) => s.id));
      const merged = [...prev, ...otherSelections.filter((s) => !existingIds.has(s.id))];
      return merged;
    });
  }, [skillsSelection]);

  const maritimeSkillsChanged =
    maritimeSkills.length !== initialMaritimeSkills.length ||
    maritimeSkills.some(
      (skill) => !initialMaritimeSkills.some((initial) => initial.id === skill.id),
    ) ||
    initialMaritimeSkills.some(
      (initial) => !maritimeSkills.some((skill) => skill.id === initial.id),
    );

  const otherSkillsChanged =
    otherSkills.length !== initialOtherSkills.length ||
    otherSkills.some((skill) => !initialOtherSkills.some((initial) => initial.id === skill.id)) ||
    initialOtherSkills.some((initial) => !otherSkills.some((skill) => skill.id === initial.id));

  const hasChanges = maritimeSkillsChanged || otherSkillsChanged;

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      const allSkills = [...maritimeSkills, ...otherSkills];
      const skillsToAddOrUpdate = allSkills.filter(
        (skill) =>
          !initialMaritimeSkills.some((initial) => initial.id === skill.id) &&
          !initialOtherSkills.some((initial) => initial.id === skill.id),
      );

      const skillsToDelete = [...initialMaritimeSkills, ...initialOtherSkills]
        .filter((initialSkill) => !allSkills.some((skill) => skill.id === initialSkill.id))
        .map(({ id, dataType }) => ({ id, dataType }));

      if (skillsToAddOrUpdate.length > 0) {
        await addSkills(transformData(skillsToAddOrUpdate));
      }

      if (skillsToDelete.length > 0) {
        await deleteSkillsAPI(skillsToDelete);
      }

      if (skillsToAddOrUpdate.length > 0 || skillsToDelete.length > 0) {
        showToast({
          message: 'Success',
          description: `Skill(s) updated successfully`,
          type: 'success',
        });
      } else {
        showToast({
          message: 'No Changes',
          description: 'No skills were added or removed.',
          type: 'info',
        });
      }

      dispatch(clearMultipleSelections('skill'));
      navigation.goBack();
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Failed to Save Skill(s)',
            description: 'Unable to save Skills',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    maritimeSkills,
    otherSkills,
    isSubmitting,
    handleSubmit,
    setMaritimeSkills,
    setOtherSkills,
    loading,
    loadingMore,
    loadMoreMaritime,
    loadMoreOther,
    hasMoreMaritime,
    hasMoreOther,
    activeTab,
    setActiveTab,
    hasChanges,
  };
};

const transformData = (data: SkillI[]) => {
  return data.map((item: SkillI) => {
    return {
      id: item.id,
      dataType: item.dataType,
      category: item.category,
    };
  });
};
