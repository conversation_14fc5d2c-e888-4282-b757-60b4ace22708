import { useCallback, useState } from 'react';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import type { ExperienceFetchForClientResultI } from '@/src/redux/slices/experience/types';
import { showToast } from '@/src/utilities/toast';
import type { ProfileStackParamsListI } from '@/src/navigation/types';
import { deleteExperience, fetchAllExperiences } from '@/src/networks/experience/experience';
import type { UseEditExperienceListI } from './types';

const useEditExperienceList = (profileId: string): UseEditExperienceListI => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const pageSize = 10;
  const [experiences, setExperiences] = useState<ExperienceFetchForClientResultI[]>([]);
  const [error, setError] = useState<Error | null>(null);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();

  const fetchExperiences = async (pageNumber: number, isLoadMore = false) => {
    if (isLoading && !isLoadMore) return;

    try {
      if (isLoadMore) {
        setLoadingMore(true);
      } else {
        setIsLoading(true);
      }

      const response = await fetchAllExperiences(profileId, pageNumber, pageSize);

      if (isLoadMore) {
        setExperiences((prev) => [...prev, ...response.data]);
      } else {
        setExperiences(response.data);
      }
      setHasMore(response.data.length >= pageSize);
      setPage(pageNumber);
    } catch (error) {
      triggerErrorBoundary(
        new Error(
          'Failed to load experiences: ' +
            (error instanceof Error ? error.message : 'Unknown error'),
        ),
      );
    } finally {
      setIsLoading(false);
      setLoadingMore(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchExperiences(0);
    }, [profileId]),
  );

  const handleLoadMore = () => {
    if (!loadingMore && !isLoading && hasMore) {
      fetchExperiences(page + 1, true);
    }
  };

  const handleModal = () => {
    setIsModalVisible(!isModalVisible);
  };

  const onAddExperience = () => {
    navigation.navigate('EditExperienceItem', { profileId });
  };

  const onEditExperience = (experienceId: string) => {
    navigation.navigate('EditExperienceItem', { experienceId, profileId });
  };

  const onDeleteExperience = async (experienceId: string) => {
    setIsLoading(true);
    try {
      await deleteExperience([{ id: experienceId, opr: 'DELETE' }]);
      setExperiences((prev) => prev.filter((item) => item.id !== experienceId));
      showToast({
        message: 'Success',
        description: 'Experience deleted successfully',
        type: 'success',
      });
    } catch (err) {
      const errorMsg = 'Error deleting experience';
      showToast({
        message: 'Error',
        description: errorMsg,
        type: 'error',
      });
    } finally {
      handleModal();
      setIsLoading(false);
    }
  };

  return {
    experiences,
    isLoading,
    loadingMore,
    hasMore,
    handleLoadMore,
    isModalVisible,
    handleModal,
    onAddExperience,
    onEditExperience,
    onDeleteExperience,
    navigation,
  };
};

export default useEditExperienceList;
