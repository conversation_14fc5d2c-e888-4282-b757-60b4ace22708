/*
Copyright (c) 2025-present Navicater Solutions
This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path, Rect } from 'react-native-svg';
import { OutlinedIconPropsI } from './types';

const AddItem: React.FC<Omit<OutlinedIconPropsI, 'onPress'>> = ({
  width = 3.96,
  height = 3.96,
  color = '#448600',
  fill,
  stroke = '#D4D4D4',
  strokeWidth = 1,
  accessibilityLabel = 'Add Item',
  disabled,
  ...props
}) => {
  const fillColor = fill || color;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 32 33"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Rect
        x="0.5"
        y="1"
        width="31"
        height="31"
        rx="7.5"
        stroke={stroke}
        strokeWidth={strokeWidth}
      />
      <Path
        d="M24 16.5C24 16.6768 23.9298 16.8464 23.8047 16.9714C23.6797 17.0964 23.5101 17.1667 23.3333 17.1667H16.6667V23.8333C16.6667 24.0101 16.5964 24.1797 16.4714 24.3047C16.3464 24.4298 16.1768 24.5 16 24.5C15.8232 24.5 15.6536 24.4298 15.5286 24.3047C15.4036 24.1797 15.3333 24.0101 15.3333 23.8333V17.1667H8.66667C8.48986 17.1667 8.32029 17.0964 8.19526 16.9714C8.07024 16.8464 8 16.6768 8 16.5C8 16.3232 8.07024 16.1536 8.19526 16.0286C8.32029 15.9036 8.48986 15.8333 8.66667 15.8333H15.3333V9.16667C15.3333 8.98986 15.4036 8.82029 15.5286 8.69526C15.6536 8.57024 15.8232 8.5 16 8.5C16.1768 8.5 16.3464 8.57024 16.4714 8.69526C16.5964 8.82029 16.6667 8.98986 16.6667 9.16667V15.8333H23.3333C23.5101 15.8333 23.6797 15.9036 23.8047 16.0286C23.9298 16.1536 24 16.3232 24 16.5Z"
        fill={fillColor}
      />
    </Svg>
  );
};

export default AddItem;
