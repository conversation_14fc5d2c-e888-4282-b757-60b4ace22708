import type React from 'react';
import { View, Text, Pressable, Image } from 'react-native';
import BackButton from '@/src/components/BackButton';
import UserAvatar from '@/src/components/UserAvatar';
import AiBot from '@/src/assets/images/others/aibot.png';
import HorizontalEllipsis from '@/src/assets/svgs/HorizontalEllipsis';
import type { ChatHeaderProps } from './types';

export const ChatHeader: React.FC<ChatHeaderProps> = ({ onMenuPress, onBackPress }) => {
  return (
    <View className="flex-row items-center justify-between px-4 border-b border-gray-200 bg-white z-10 h-14">
      <View className="flex-row items-center gap-2">
        <BackButton label="" onBack={onBackPress} />
        <UserAvatar
          avatarUri={Image.resolveAssetSource(AiBot).uri}
          width={37}
          height={37}
          name="Navicater AI"
        />
        <Text className="text-base font-medium">Navicater AI (Beta)</Text>
      </View>
      <Pressable
        onPress={onMenuPress}
        className="p-2"
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <HorizontalEllipsis color="#6B7280" />
      </Pressable>
    </View>
  );
};
