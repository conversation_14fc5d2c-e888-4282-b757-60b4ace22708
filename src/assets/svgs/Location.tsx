/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from './types';

const Location: React.FC<OutlinedIconPropsI> = ({
  width = 2.48,
  height = 3.1,
  color = '#404040',
  ...props
}) => {
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 20 25"
      fill="none"
      {...props}
    >
      <Path
        d="M10 5.3572C9.10099 5.3572 8.22218 5.61903 7.47468 6.10957C6.72718 6.60012 6.14458 7.29735 5.80055 8.1131C5.45651 8.92885 5.3665 9.82648 5.54188 10.6925C5.71727 11.5585 6.15019 12.3539 6.78588 12.9783C7.42157 13.6026 8.23149 14.0278 9.11323 14.2001C9.99496 14.3723 10.9089 14.2839 11.7395 13.946C12.57 13.6081 13.2799 13.0359 13.7794 12.3018C14.2789 11.5676 14.5455 10.7045 14.5455 9.82153C14.5455 8.63751 14.0666 7.50199 13.2141 6.66477C12.3617 5.82754 11.2055 5.3572 10 5.3572ZM10 12.5001C9.4606 12.5001 8.93331 12.343 8.48481 12.0487C8.03631 11.7544 7.68675 11.336 7.48033 10.8466C7.27391 10.3571 7.2199 9.81856 7.32513 9.29896C7.43036 8.77936 7.69011 8.30208 8.07153 7.92747C8.45294 7.55286 8.9389 7.29775 9.46794 7.1944C9.99697 7.09104 10.5453 7.14409 11.0437 7.34682C11.542 7.54956 11.968 7.89288 12.2676 8.33338C12.5673 8.77387 12.7273 9.29175 12.7273 9.82153C12.7273 10.5319 12.4399 11.2132 11.9285 11.7156C11.417 12.2179 10.7233 12.5001 10 12.5001ZM10 0C7.34876 0.00295397 4.80697 1.03867 2.93225 2.87992C1.05754 4.72117 0.00300765 7.2176 0 9.82153C0 13.326 1.64886 17.0403 4.77273 20.5638C6.17639 22.156 7.7562 23.5897 9.48295 24.8384C9.63581 24.9436 9.81792 25 10.0045 25C10.1912 25 10.3733 24.9436 10.5261 24.8384C12.2497 23.5892 13.8265 22.1555 15.2273 20.5638C18.3466 17.0403 20 13.326 20 9.82153C19.997 7.2176 18.9425 4.72117 17.0677 2.87992C15.193 1.03867 12.6512 0.00295397 10 0ZM10 22.9913C8.12159 21.5404 1.81818 16.2111 1.81818 9.82153C1.81818 7.6903 2.68019 5.64637 4.21458 4.13936C5.74897 2.63236 7.83005 1.78573 10 1.78573C12.17 1.78573 14.251 2.63236 15.7854 4.13936C17.3198 5.64637 18.1818 7.6903 18.1818 9.82153C18.1818 16.2089 11.8784 21.5404 10 22.9913Z"
        fill={color}
      />
    </Svg>
  );
};

export default Location;
