import { StackNavigationProp } from '@react-navigation/stack';
import { IdNameI } from '@/src/types/common/data';
import { ProfileStackParamsListI } from '@/src/navigation/types';

export interface CertificationI {
  id: string;
  entity: IdNameI;
  certificateCourse: IdNameI;
  fromDate: string;
  untilDate: string;
}

export interface CertificationCategoryI {
  title: string;
  certifications: CertificationI[];
}

export interface UseEditCertificationListI {
  onAddCertification: (type: string) => void;
  navigation: StackNavigationProp<ProfileStackParamsListI>;
}

export interface EditCertificationListPropsI {
  onBack: () => void;
  profileId: string;
  editable: boolean;
  tab: 'statutory' | 'value-added';
}

export interface fetchedCertifications {
  statutoryCertifications: CertificationI[];
  valueAddedCertifications: CertificationI[];
}

export type EditCertificationTabsI = {
  statutory: React.ReactNode;
  'value-added': React.ReactNode;
};
