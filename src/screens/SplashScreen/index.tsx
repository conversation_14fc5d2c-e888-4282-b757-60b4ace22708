/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React, { useEffect } from 'react';
import { Dimensions, Image } from 'react-native';
import Animated, { useSharedValue, useAnimatedStyle, withTiming } from 'react-native-reanimated';
import SafeArea from '@/src/components/SafeArea';

const { width, height } = Dimensions.get('window');

const SplashScreen = () => {
  const logoOpacity = useSharedValue(0);

  useEffect(() => {
    logoOpacity.value = withTiming(1, { duration: 80 });
  }, []);

  const logoAnimatedStyle = useAnimatedStyle(() => ({
    opacity: logoOpacity.value,
  }));

  return (
    <SafeArea>
      <Animated.View className="absolute -top-[400px] right-0">
        <Image
          source={require('@/src/assets/images/onboarding/splashgradienttop.png')}
          resizeMode="contain"
        />
      </Animated.View>

      <Animated.View className="absolute -bottom-[400px] left-0">
        <Image
          source={require('@/src/assets/images/onboarding/splashgradientbottom.png')}
          resizeMode="contain"
        />
      </Animated.View>

      <Animated.View
        style={[
          logoAnimatedStyle,
          {
            position: 'absolute',
            top: height / 2 - 60,
            left: width / 2 - 60,
            width: 120,
            height: 120,
          },
        ]}
      >
        <Image
          source={require('@/src/assets/images/brand/logo.png')}
          className="w-[120px] h-[120px]"
          resizeMode="contain"
        />
      </Animated.View>
    </SafeArea>
  );
};

export default SplashScreen;
