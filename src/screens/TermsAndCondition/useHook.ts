import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { showToast } from '@/src/utilities/toast';
import { fetchPrivacyPolicyAPI } from '@/src/networks/profile/privacyPolicy';

export const useTermsAndConditions = () => {
  const { profileId } = useSelector(selectCurrentUser);
  const [termsContent, setTermsContent] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchTermsOfUse = async () => {
      try {
        setLoading(true);
        const result = await fetchPrivacyPolicyAPI({
          type: 'TERMS_OF_USE',
        });

        if (result) {
          setTermsContent(result.content);
        }
      } catch (error) {
        showToast({
          message: 'Error loading terms of service',
          type: 'error',
        });
      } finally {
        setLoading(false);
      }
    };

    if (profileId) {
      fetchTermsOfUse();
    }
  }, [profileId]);

  return {
    termsContent,
    loading,
  };
};
