import { Image, Pressable, Text, View } from 'react-native';
import UserAvatar from '@/src/components/UserAvatar';
import AiBot from '@/src/assets/images/others/aibot.png';
import AddItem from '@/src/assets/svgs/AddItem';
import Search from '@/src/assets/svgs/Search';

const ExploreHead = () => {
  return (
    <View className="flex-row justify-between items-center bg-white">
      <View className="flex-row items-center gap-2 px-2">
        <Text className="text-2xl font-semibold text-black px-2">Explore</Text>
      </View>
      <View className="flex-row items-center gap-2">
        <Pressable className="items-center justify-center pr-2">
          <Search width={3.5} height={3.5} color="black" />
        </Pressable>
        <Pressable className="items-center justify-center pr-2">
          <UserAvatar
            avatarUri={Image.resolveAssetSource(AiBot).uri}
            width={37}
            height={37}
            name="Navicater AI"
          />
        </Pressable>
      </View>
    </View>
  );
};

export default ExploreHead;
