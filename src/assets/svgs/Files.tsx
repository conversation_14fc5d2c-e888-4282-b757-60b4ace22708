/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Circle, Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const Files: React.FC<FilledIconPropsI> = ({
  width = 5.456,
  height = 5.456,
  fill = '#448600',
  color,
  disabled,
  accessibilityLabel = 'Files',
  ...props
}) => {
  const fillColor = color || fill;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 44 44"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Circle cx={22} cy={22} r={21.5} fill="#F2F9E5" stroke="#DDEFC8" />
      <Path
        d="M30.03 16.22l-3.75-3.75a.748.748 0 00-.53-.22h-7.5a1.5 1.5 0 00-1.5 1.5v1.5h-1.5a1.5 1.5 0 00-1.5 1.5v13.5a1.5 1.5 0 001.5 1.5h10.5a1.5 1.5 0 001.5-1.5v-1.5h1.5a1.5 1.5 0 001.5-1.5v-10.5a.75.75 0 00-.22-.53zm-4.28 14.03h-10.5v-13.5h7.19l3.31 3.31v10.19zm3-3h-1.5v-7.5a.75.75 0 00-.22-.53l-3.75-3.75a.748.748 0 00-.53-.22h-4.5v-1.5h7.19l3.31 3.31v10.19zm-5.25-3a.75.75 0 01-.75.75h-4.5a.75.75 0 110-1.5h4.5a.75.75 0 01.75.75zm0 3a.75.75 0 01-.75.75h-4.5a.75.75 0 110-1.5h4.5a.75.75 0 01.75.75z"
        fill={fillColor}
      />
    </Svg>
  );
};

export default Files;
