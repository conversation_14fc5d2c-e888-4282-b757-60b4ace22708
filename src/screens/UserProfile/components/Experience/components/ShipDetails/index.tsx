/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { Pressable, Text, View } from 'react-native';
import { navigate } from '@/src/utilities/navigation';
import Information from '@/src/assets/svgs/Information';
import ProfileShip from '@/src/assets/svgs/ProfileShip';
import { ShipDetailsI } from './types';
import { formatDateToMonthYear } from './utils';

const ShipDetails = ({ ship }: { ship: ShipDetailsI }) => {
  const handleShipExperienceDetails = () => {
    navigate('EditShipItem', {
      shipId: ship.id,
      fromProfileExperience: {
        id: ship.id,
        imo: ship.ship.imo,
      },
    });
  };

  const handleShipProfile = () => {
    navigate('ShipProfile', {
      imo: ship.ship.imo,
      dataType: ship.ship.dataType,
    });
  };

  return (
    <View className="my-2">
      <View className="flex-row items-center mt-1 gap-1 text-labelBlack">
        <Text className="font-medium text-sm">{ship.name}</Text>
        <Text className="text-base">|</Text>
        <Text className="text-sm">{ship.subVesselType.name}</Text>
      </View>
      <Text className="text-gray-500 text-sm mt-1 mb-2">
        {formatDateToMonthYear(ship.fromDate)} -{' '}
        {ship.toDate !== null ? formatDateToMonthYear(ship.toDate) : 'Present'}
      </Text>
      <View className="flex-row items-center gap-5 my-1.5">
        <Pressable onPress={handleShipProfile}>
          <ProfileShip width={2.7} height={2.7} />
        </Pressable>
        <Pressable onPress={handleShipExperienceDetails}>
          <Information width={2.7} height={2.7} />
        </Pressable>
      </View>
    </View>
  );
};

export default ShipDetails;
