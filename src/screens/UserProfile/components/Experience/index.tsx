/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ScrollView } from 'react-native';
import ProfileExperience from './components/ProfileExperience';
import { ExperienceScreenPropsI } from './types';

const ExperienceScreen = ({ isUserProfile, profileId }: ExperienceScreenPropsI) => {
  return (
    <ScrollView showsVerticalScrollIndicator={false} className="flex-1">
      <ProfileExperience isUserProfile={isUserProfile} profileId={profileId} />
    </ScrollView>
  );
};

export default ExperienceScreen;
