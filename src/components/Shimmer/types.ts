/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { DimensionValue, ViewStyle } from 'react-native';

export interface ShimmerBoxProps {
  width: DimensionValue;
  height: DimensionValue;
  borderRadius?: number;
  style?: ViewStyle;
  className?: string;
  variant?: 'rectangle' | 'sphere';
}
