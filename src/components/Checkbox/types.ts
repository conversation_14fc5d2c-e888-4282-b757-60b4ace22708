/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
export interface CheckboxProps {
  label: string;
  checked?: boolean;
  onValueChange?: (newValue: boolean) => void;
  disabled?: boolean;
  required?: boolean;
  error?: string;
  className?: string;
  labelClassName?: string;
  size?: number;
}
