import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { ObjUnknownI } from '@/src/types/common/data';
import { ReactionDetail, ReactionExternalClientI } from '../content/types';

export type RequestStatusTypeE = 'PENDING' | 'ACCEPTED' | 'REJECTED' | 'REVOKED' | 'DISCONNECTED';

export interface CityResult {
  name: string;
}

export interface FetchPortProfileQueryI extends ObjUnknownI {
  unLocode: string;
  dataType: string;
}

export interface CountryResult {
  name: string;
}

export interface TimezoneResult {
  timezone: string;
  utcOffset: number;
  dstOffset: number;
}

export interface LocationCoordinates {
  latitude?: string | null;
  longitude?: string | null;
}

export interface PortCapacity {
  noOfTerminals?: number | null;
  noOfBerths?: number | null;
  maxDraught?: number | null;
  maxDeadweight?: number | null;
  maxLength?: number | null;
  maxAirDraught?: number | null;
}

export interface PortProfileData extends LocationCoordinates, PortCapacity {
  unLocode?: string;
  name?: string;
  imageUrl?: string | null;
  city?: CityResult | null;
  country?: CountryResult | null;
  dataType: 'master' | 'raw';
  isVisited: boolean;
  timezone?: TimezoneResult | null;
}

export interface FetchPortVisitorQueryI extends ObjUnknownI {
  unLocode: string;
  dataType: 'master' | 'raw';
  page: number;
  pageSize: number;
}

export interface AddPortVisitorPayloadI {
  portUnLocode: string;
}

export interface VisitorProfile {
  id: string;
  name: string;
  avatar: string | null;
  designation: SearchResultI;
  entity: SearchResultI;
  isConnected: boolean;
  requestStatus: RequestStatusTypeE;
}

export interface FetchVisitorsResult {
  total: number;
  data: VisitorProfile[];
}

export interface ScrapbookPostCreateBodyI {
  text: string;
  portUnLocode: string;
}

export interface ScrapbookPostListQueryI extends ObjUnknownI {
  portUnLocode: string;
  page: number;
  pageSize?: number;
}

export interface ScrapbookReactionListQueryI extends ObjUnknownI {
  page: number;
  pageSize?: number;
  scrapBookPostId: string;
}

export interface ScrapbookReactionUpsertBodyI {
  scrapBookPostId: string;
  reactionType: 'LIKE';
}

export interface ScrapbookReactionDeleteBodyI {
  scrapBookPostId: string;
}

export interface ScrapbookReactionResultI {
  reactions: ReactionDetail[];
  totalCount: number;
}

export interface ScrapBookPostFetchForClientI {
  id: string;
  commentCount: number;
  reactionCount: number;
  textPreview: string;
  text: string;
  createdAt: string | Date;
  isLiked: boolean;
  isCaptionTruncated: boolean;
  profile: {
    id: string;
    name: string;
    avatar: string | null;
    designationText: string | null;
  };
}

export interface ScrapBookPostFetchForClientIResponse {
  data: ScrapBookPostFetchForClientI[];
  total: number;
}

export interface ScrapBookPostDeletePayloadI {
  id: string;
}

export interface ScrapBookCommentFetchManyQueryI extends ObjUnknownI {
  scrapBookPostId: string;
  cursorId: number | null;
  pageSize?: number;
}

export interface ScrapBookCommentFetchRepliesQueryI extends ObjUnknownI {
  scrapBookPostId: string;
  parentCommentId: string;
  cursorId: number | null;
  pageSize?: number;
}

export interface ScrapBookCommentCreateBodyI {
  scrapBookPostId: string;
  parentCommentId?: string;
  text: string;
}

export interface ScrapBookCommentFetchForClientI {
  id: string;
  text: string;
  cursorId: number | null;
  createdAt: Date;
  updatedAt: Date;
  Profile: {
    id: string;
    name: string;
    avatar: string | null;
    designation: {
      id: string;
      name: string;
      dataType: 'master' | 'raw';
    } | null;
    entity: {
      id: string;
      name: string;
      dataType: 'master' | 'raw';
    } | null;
  };
  replies: ScrapBookCommentFetchForClientI[];
  repliesCount: number;
}

export interface ScrapBookCommentFetchManyResultI {
  comments: ScrapBookCommentFetchForClientI[];
  total: number;
  cursorId: number | null;
}

export interface ScrapBookCommentCreateOneResultI {
  id: string;
  cursorId: number;
}

export interface ScrapBookPostCaptionResponse {
  caption: string;
}
