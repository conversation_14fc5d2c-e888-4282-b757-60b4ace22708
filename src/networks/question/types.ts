export type QuestionTypeE = 'NORMAL' | 'TROUBLESHOOT';

export type IdTypeI = {
  id: string;
  dataType: 'master' | 'raw';
};

export type QuestionMediaCreateItemI = {
  fileUrl: string;
  fileExtension: string;
};

export interface CreateQuestionPayloadI {
  title: string;
  description: string;
  type: QuestionTypeE;
  communityId: string;
  isLive: boolean;
  equipmentCategory?: IdTypeI;
  equipmentModel?: IdTypeI;
  equipmentManufacturer?: IdTypeI;
  topics?: IdTypeI[];
  department?: IdTypeI | null;
  isAnonymous?: boolean;
  files?: QuestionMediaCreateItemI[];
}

export interface QuestionCreateResponseI {
  id: string;
  title: string;
  description: string;
  type: QuestionTypeE;
}

export interface QuestionI {
  id: string;
  title: string;
  description: string;
  type: QuestionTypeE;
  communityId: string;
  isAnonymous?: boolean;
  createdAt: string;
  updatedAt: string;
  authorId: string;
}

export type ForumQuestionFetchManyPayloadI = {
  cursorDate?: string | null;
  pageSize?: string;
  type?: 'ALL' | 'NORMAL' | 'TROUBLESHOOT';
  isLive?: boolean;
  myRecommended?: boolean;
  myQuestion?: boolean;
  myAnswered?: boolean;
  myCommunity?: boolean;
  department?: IdTypeI;
  topics?: IdTypeI[];
  equipmentCategory?: IdTypeI;
  equipmentManufacturer?: IdTypeI;
  equipmentModel?: IdTypeI;
};

export type TopicI = {
  id?: string;
  name?: string;
  topicRawDataId?: string;
  topicRawDataName?: string;
};

export type ForumQuestionI = {
  id: string;
  title: string;
  description: string;
  type: QuestionTypeE;
  upvoteCount: number;
  downvoteCount: number;
  answerCount: number;
  commentCount: number;
  isSolved: boolean;
  isEdited: boolean;
  isAnonymous: boolean;
  liveStartedAt: string;
  equipmentCategory?: EquipmentCategoryI | null;
  equipmentModel?: (IdTypeI & { name: string }) | null;
  equipmentManufacturer?: (IdTypeI & { name: string }) | null;
  topics?: TopicI[] | null;
  userVote?: 'UPVOTE' | 'DOWNVOTE' | null;
  media: { fileExtension: string; fileUrl: string }[];
  canModify: boolean;
  vote?: 'UPVOTE' | 'DOWNVOTE' | null;
  isLive: boolean;
};

export type ForumQuestionFetchManyResultI = {
  data: ForumQuestionI[];
  nextCursorDate: string | null;
  total: number;
};

export type ForumQuestionResultI = {
  id: string;
  title: string;
  communityId: string;
  profile: ProfileForDataI;
  description: string;
  type: string;
  canModify: boolean;
  upvoteCount: number;
  downvoteCount: number;
  answerCount: number;
  commentCount: number;
  isSolved: boolean;
  isEdited: boolean;
  isAnonymous: boolean;
  liveStartedAt: string;
  equipmentCategory: EquipmentCategoryI | null;
  equipmentModel: EquipmentCategoryI | null;
  equipmentManufacturer: EquipmentCategoryI | null;
  topics: Topic[] | null;
  media: { fileUrl: string; fileExtension: string }[];
  isLive: boolean;
};

type Topic = {
  id: string;
  name: string;
  type: string;
};

type EquipmentCategoryI = {
  id: string;
  name: string;
  dataType: string;
};

export type DBDataTypeI = 'master' | 'raw';

export type EquipmentCategoryNestedClientDataI = {
  id: string;
  name: string;
  dataType: DBDataTypeI;
};

export type EquipmentModelNestedClientI = {
  id: string;
  name: string;
  dataType: DBDataTypeI;
} | null;

export type EquipmentManufacturerNestedClientI = {
  id: string;
  name: string;
  dataType: DBDataTypeI;
} | null;

export type ProfileForDataI = {
  id: string;
  name: string;
  avatar: string | null;
};

export type IdNameTypeI = {
  id: string;
  name: string;
  dataType: DBDataTypeI;
};

export type DepartmentNestedClientI = {
  id: string;
  name: string;
  dataType: DBDataTypeI;
};

export type ForumAnswerWithProfileForQuestionI = {
  id: string;
  cursorId: number;
  text: string;
  upvoteCount: number;
  downvoteCount: number;
  commentCount: number;
  status: string;
  isEdited: boolean;
  createdAt: string;
  profile: ProfileForDataI;
  userVote?: 'UPVOTE' | 'DOWNVOTE' | null;
  canModify?: boolean;
};

export type QuestionTypeI = 'NORMAL' | 'TROUBLESHOOT';

export type ForumQuestionDetailWithAnswersI = {
  id: string;
  cursorId: number;
  title: string;
  description: string | null;
  type: QuestionTypeI;
  upvoteCount: number;
  downvoteCount: number;
  answerCount: number;
  commentCount: number;
  isLive: boolean;
  liveStartedAt: string;
  shipImo: string | null;
  isSolved: boolean;
  equipmentCategory: EquipmentCategoryNestedClientDataI | null;
  equipmentModel: EquipmentModelNestedClientI | null;
  equipmentManufacturer: EquipmentManufacturerNestedClientI | null;
  createdAt: string;
  isEdited: boolean;
  profile: ProfileForDataI | null;
  topics: IdNameTypeI[] | null;
  departments: DepartmentNestedClientI[] | null;
  media:
    | {
        id: string;
        fileUrl: string;
        fileExtension: 'webp' | 'jpeg' | 'jpg' | 'pdf' | 'xls' | 'xlsx';
      }[]
    | null;
  answers: ForumAnswerWithProfileForQuestionI[] | null;
  canModify: boolean;
  isAnonymous?: boolean;
};

export interface QuestionDeleteOnePayloadI {
  questionId: string;
}
