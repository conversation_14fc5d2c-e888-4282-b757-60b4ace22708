import { useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { selectMultipleSelectionsByKey } from '@/src/redux/selectors/search';
import {
  clearMultipleSelections,
  clearSelection,
} from '@/src/redux/slices/entitysearch/searchSlice';
import type { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import type { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import type { IdTypeI } from '@/src/types/common/data';
import type { ProfileStackParamsListI } from '@/src/navigation/types';
import {
  addEducationAPI,
  editEducationAPI,
  fetchEducationAPI,
} from '@/src/networks/career/education';
import type { EducationFormDataI, UseEditEducationItemI } from './types';

export const useEditEducationItem = (
  profileId?: string,
  educationId?: string,
): UseEditEducationItemI => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();
  const [localSkills, setLocalSkills] = useState<SearchResultI[]>([]);
  const skillsSelection = useSelector(selectMultipleSelectionsByKey('skill'));
  const [initialSkills, setInitialSkills] = useState<SearchResultI[]>([]);
  const [loading, setLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const dispatch = useDispatch<AppDispatch>();
  const MAX_SKILLS = 6;

  const methods = useForm<EducationFormDataI>({
    mode: 'onChange',
    defaultValues: {
      institution: {},
      degree: {},
      fromDate: '',
      toDate: '',
      skills: [],
      isPresent: false,
    },
  });

  const isPresent = methods.watch('isPresent');
  const isSkillLimitReached = localSkills.length > MAX_SKILLS;

  const skillsChanged =
    localSkills.length !== initialSkills.length ||
    localSkills.some((skill) => !initialSkills.find((initial) => initial.id === skill.id)) ||
    initialSkills.some((initial) => !localSkills.find((skill) => skill.id === initial.id));

  const hasChanges = methods.formState.isDirty || skillsChanged;

  useEffect(() => {
    if (educationId) {
      const fetchEducation = async () => {
        try {
          setLoading(true);
          const response = await fetchEducationAPI(educationId);
          const fetchedEducation: EducationFormDataI = {
            institution: response.entity,
            degree: response.degree,
            fromDate: response.fromDate,
            toDate: response.toDate,
            skills: response.skills,
            isPresent: !response.toDate,
          };
          setLocalSkills((response.skills as unknown as SearchResultI[]) || []);
          setInitialSkills(response.skills);
          methods.reset(fetchedEducation);
        } catch (err) {
          triggerErrorBoundary(
            new Error(
              'Failed to load education details: ' +
                (err instanceof Error ? err.message : 'Unknown error'),
            ),
          );
        } finally {
          setLoading(false);
        }
      };
      fetchEducation();
    }
  }, [educationId, methods]);

  useEffect(() => {
    if (!skillsSelection) return;
    setLocalSkills((prev) => {
      const existingIds = new Set(prev.map((s) => s.id));
      const merged = [...prev, ...skillsSelection.filter((s) => !existingIds.has(s.id))];
      return merged;
    });
  }, [skillsSelection]);

  const onSubmit = async (data: EducationFormDataI) => {
    try {
      if (isSkillLimitReached) {
        showToast({
          message: 'Skills Limit Reached',
          description: `Max ${MAX_SKILLS} skills allowed`,
          type: 'error',
        });
        return;
      }
      setIsSubmitting(true);
      if (educationId) {
        const deletedSkills = initialSkills
          .filter((initial) => !localSkills.some((local) => local.id === initial.id))
          .map(({ id, dataType }) => ({ id, dataType }));
        const addedSkills = localSkills
          .filter((local) => !initialSkills.some((initial) => initial.id === local.id))
          .map(({ id, dataType }) => ({ id, dataType }));
        const payload = transformDataForEdit(data, addedSkills, deletedSkills);
        await editEducationAPI(educationId, payload);
      } else {
        const payload = transformDataForCreate(data, localSkills);
        await addEducationAPI(payload);
        setLocalSkills([]);
      }
      showToast({
        message: 'Success',
        description: educationId
          ? 'Education updated successfully'
          : 'Education added successfully',
        type: 'success',
      });
      methods.reset(methods.getValues());
      navigation.goBack();
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Failed to Save Education',
            description: 'Unable to save Education',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePresentCheckbox = (newValue: boolean) => {
    methods.setValue('isPresent', newValue, { shouldDirty: true });
    if (newValue) {
      methods.setValue('toDate', '', { shouldDirty: true });
    }
    methods.clearErrors('toDate');
  };

  const clearFields = () => {
    dispatch(clearSelection('entity'));
    dispatch(clearSelection('degree'));
    dispatch(clearMultipleSelections('skill'));
  };

  return {
    methods,
    isSubmitting,
    onSubmit,
    navigation,
    localSkills,
    setLocalSkills,
    loading,
    isPresent,
    handlePresentCheckbox,
    clearFields,
    isSubmitted,
    setIsSubmitted,
    hasChanges,
    isSkillLimitReached,
    MAX_SKILLS,
  };
};

const transformDataForCreate = (
  data: EducationFormDataI,
  skills: { id: string; dataType: 'raw' | 'master' }[],
) => {
  return {
    institute: {
      id: data.institution.id,
      dataType: data.institution.dataType,
    },
    degree: {
      id: data.degree.id,
      dataType: data.degree.dataType,
    },
    fromDate: data.fromDate,
    toDate: data.toDate?.length === 0 ? null : data.toDate,
    skills: skills,
  };
};

const transformDataForEdit = (
  data: EducationFormDataI,
  addedSkills: IdTypeI[],
  deletedSkills: IdTypeI[],
) => {
  return {
    institute: {
      id: data.institution.id,
      dataType: data.institution.dataType,
    },
    degree: {
      id: data.degree.id,
      dataType: data.degree.dataType,
    },
    fromDate: data.fromDate,
    toDate: data.toDate?.length === 0 ? null : data.toDate,
    skillsToAdd: addedSkills,
    skillsToDelete: deletedSkills,
  };
};
