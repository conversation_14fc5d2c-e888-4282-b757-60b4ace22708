import * as React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const Pdf: React.FC<FilledIconPropsI> = ({ width = 2, height = 2 }) => {
  return (
    <Svg width={RFPercentage(width)} height={RFPercentage(height)} viewBox="0 0 18 20" fill="none">
      <Path
        d="M17.03 4.22L13.28.47a.75.75 0 00-.53-.22h-7.5a1.5 1.5 0 00-1.5 1.5v1.5h-1.5a1.5 1.5 0 00-1.5 1.5v13.5a1.5 1.5 0 001.5 1.5h10.5a1.5 1.5 0 001.5-1.5v-1.5h1.5a1.5 1.5 0 001.5-1.5V4.75a.75.75 0 00-.22-.53zm-4.28 14.03H2.25V4.75h7.19l3.31 3.31v10.19zm3-3h-1.5v-7.5a.75.75 0 00-.22-.53l-3.75-3.75a.75.75 0 00-.53-.22h-4.5v-1.5h7.19l3.31 3.31v10.19zm-5.25-3a.75.75 0 01-.75.75h-4.5a.75.75 0 110-1.5h4.5a.75.75 0 01.75.75zm0 3a.75.75 0 01-.75.75h-4.5a.75.75 0 110-1.5h4.5a.75.75 0 01.75.75z"
        fill="#448600"
      />
    </Svg>
  );
};

export default Pdf;
