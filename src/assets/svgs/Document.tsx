/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Circle, Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const Document: React.FC<FilledIconPropsI> = ({
  width = 5.456,
  height = 5.58,
  fill = '#448600',
  color,
  disabled,
  accessibilityLabel = 'Document',
  ...props
}) => {
  const fillColor = color || fill;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 44 45"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Circle cx="22" cy="22.4707" r="21.5" fill="#F2F9E5" stroke="#DDEFC8" />
      <Path
        d="M30.0306 16.6901L26.2806 12.9401C26.2109 12.8705 26.1282 12.8152 26.0371 12.7776C25.9461 12.74 25.8485 12.7206 25.75 12.7207H18.25C17.8522 12.7207 17.4706 12.8787 17.1893 13.16C16.908 13.4413 16.75 13.8229 16.75 14.2207V15.7207H15.25C14.8522 15.7207 14.4706 15.8787 14.1893 16.16C13.908 16.4413 13.75 16.8229 13.75 17.2207V30.7207C13.75 31.1185 13.908 31.5001 14.1893 31.7814C14.4706 32.0627 14.8522 32.2207 15.25 32.2207H25.75C26.1478 32.2207 26.5294 32.0627 26.8107 31.7814C27.092 31.5001 27.25 31.1185 27.25 30.7207V29.2207H28.75C29.1478 29.2207 29.5294 29.0627 29.8107 28.7814C30.092 28.5001 30.25 28.1185 30.25 27.7207V17.2207C30.2501 17.1222 30.2307 17.0246 30.1931 16.9336C30.1555 16.8425 30.1003 16.7598 30.0306 16.6901ZM25.75 30.7207H15.25V17.2207H22.4397L25.75 20.531V28.4557C25.75 28.4613 25.75 28.466 25.75 28.4707C25.75 28.4754 25.75 28.4801 25.75 28.4857V30.7207ZM28.75 27.7207H27.25V20.2207C27.2501 20.1222 27.2307 20.0246 27.1931 19.9336C27.1555 19.8425 27.1003 19.7598 27.0306 19.6901L23.2806 15.9401C23.2109 15.8705 23.1282 15.8152 23.0371 15.7776C22.9461 15.74 22.8485 15.7206 22.75 15.7207H18.25V14.2207H25.4397L28.75 17.531V27.7207ZM23.5 24.7207C23.5 24.9196 23.421 25.1104 23.2803 25.251C23.1397 25.3917 22.9489 25.4707 22.75 25.4707H18.25C18.0511 25.4707 17.8603 25.3917 17.7197 25.251C17.579 25.1104 17.5 24.9196 17.5 24.7207C17.5 24.5218 17.579 24.331 17.7197 24.1904C17.8603 24.0497 18.0511 23.9707 18.25 23.9707H22.75C22.9489 23.9707 23.1397 24.0497 23.2803 24.1904C23.421 24.331 23.5 24.5218 23.5 24.7207ZM23.5 27.7207C23.5 27.9196 23.421 28.1104 23.2803 28.251C23.1397 28.3917 22.9489 28.4707 22.75 28.4707H18.25C18.0511 28.4707 17.8603 28.3917 17.7197 28.251C17.579 28.1104 17.5 27.9196 17.5 27.7207C17.5 27.5218 17.579 27.331 17.7197 27.1904C17.8603 27.0497 18.0511 26.9707 18.25 26.9707H22.75C22.9489 26.9707 23.1397 27.0497 23.2803 27.1904C23.421 27.331 23.5 27.5218 23.5 27.7207Z"
        fill={fillColor}
      />
    </Svg>
  );
};

export default Document;
