/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const ProfileChat: React.FC<FilledIconPropsI> = ({
  width = 2.976,
  height = 3.1,
  fill = 'black',
  ...props
}) => {
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 24 25"
      fill="none"
      {...props}
    >
      <Path
        d="M20.3384 17.0642C20.7946 16.18 21.0211 15.2032 20.9985 14.217C20.9758 13.2309 20.7048 12.2646 20.2084 11.4005C19.712 10.5365 19.0051 9.80032 18.1478 9.25481C17.2906 8.7093 16.3084 8.37062 15.2851 8.26764C14.9601 7.50762 14.4788 6.81814 13.8698 6.2403C13.2609 5.66247 12.5368 5.20813 11.7407 4.90437C10.9447 4.60061 10.0929 4.45366 9.23636 4.47228C8.37978 4.49089 7.53588 4.6747 6.755 5.01274C5.97412 5.35078 5.27224 5.83612 4.69123 6.43982C4.11022 7.04352 3.66199 7.75321 3.37325 8.52657C3.08451 9.29993 2.96119 10.1211 3.01064 10.9412C3.06008 11.7612 3.28129 12.5633 3.66106 13.2996L3.04832 15.2932C2.98923 15.483 2.98476 15.6845 3.03539 15.8765C3.08602 16.0685 3.18985 16.2437 3.33591 16.3838C3.48196 16.5238 3.66479 16.6233 3.86504 16.6719C4.06528 16.7204 4.2755 16.7161 4.47341 16.6595L6.55624 16.072C7.23328 16.3924 7.96425 16.595 8.71515 16.6704C9.04697 17.4477 9.54218 18.1511 10.1699 18.7369C10.7977 19.3227 11.5445 19.7783 12.3639 20.0753C13.1834 20.3724 14.0578 20.5045 14.9328 20.4634C15.8078 20.4223 16.6645 20.2089 17.4498 19.8366L19.526 20.424C19.724 20.4807 19.9342 20.485 20.1344 20.4364C20.3347 20.3879 20.5175 20.2883 20.6636 20.1483C20.8096 20.0083 20.9134 19.833 20.9641 19.641C21.0147 19.4491 21.0102 19.2475 20.9511 19.0578L20.3384 17.0642Z"
        fill={fill}
      />
    </Svg>
  );
};

export default ProfileChat;
