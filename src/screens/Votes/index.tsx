/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { View } from 'react-native';
import { useRoute, useNavigation, RouteProp } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import { ListItem } from '@/src/components/UsersList/types';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { BottomTabNavigationI, HomeStackParamListI } from '@/src/navigation/types';
import VotesList from './components/VotesList';

const VotesScreen = () => {
  const route = useRoute<RouteProp<HomeStackParamListI, 'Votes'>>();
  const navigation = useNavigation<BottomTabNavigationI>();
  const currentUser = useSelector(selectCurrentUser);

  const type = route.params?.type;

  const handleUserPress = (item: ListItem) => {
    if (currentUser.profileId === item.Profile.id) {
      navigation.navigate('ProfileStack', {
        screen: 'UserProfile',
        params: { profileId: item.Profile.id, fromTabPress: false },
      });
    } else {
      navigation.navigate('HomeStack', {
        screen: 'OtherUserProfile',
        params: { profileId: item.Profile.id, fromTabPress: false },
      });
    }
  };

  return (
    <SafeArea>
      <View className="flex-row items-center px-4">
        <BackButton
          onBack={() => navigation.goBack()}
          label={type === 'upvote' ? 'Upvotes' : 'Downvotes'}
          labelClassname="text-xl font-medium"
        />
      </View>
      <VotesList onUserPress={handleUserPress} />
    </SafeArea>
  );
};

export default VotesScreen;
