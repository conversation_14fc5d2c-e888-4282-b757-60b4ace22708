import type React from 'react';
import type { Dispatch, SetStateAction } from 'react';
import type { StackNavigationProp } from '@react-navigation/stack';
import type { UseFormReturn } from 'react-hook-form';
import type { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import type { ClientCargoI, ClientEquipmentI } from '@/src/redux/slices/experience/types';
import type { IdNameI, IdTypeI } from '@/src/types/common/data';
import type { ProfileStackParamsListI } from '@/src/navigation/types';
import type { EquipmentI } from '@/src/networks/experienceShip.ts/types';

export interface EditShipItemPropsI {
  onBack: () => void;
  profileId: string;
  experienceId?: string;
  shipId?: string;
  preFilledData: ShipPreFilledDataTypeI[];
  field: FieldTypeI;
  fromProfileExperience: {
    id: string;
    imo: string;
  };
  refetch: () => void;
}

export interface UseEditShipItemI {
  methods: UseFormReturn<ShipDetailsFormDataI>;
  isSubmitting: boolean;
  onSubmit: (data: ShipDetailsFormDataI) => Promise<void>;
  navigation: StackNavigationProp<ProfileStackParamsListI>;
  localSkills: IdNameI[];
  setLocalSkills: Dispatch<SetStateAction<SearchResultI[]>>;
  handlePresentCheckbox: () => void;
  populateData: (imoData: { imo: string; dataType: string }) => Promise<void>;
  isAddVisible: boolean;
  clearFields: () => void;
  handleAddEquipmentCargo: () => ShipCreateEditPayloadI[];
  equipments: ShipEquipmentI[];
  setEquipments: Dispatch<SetStateAction<EquipmentI[]>>;
  shipReFetch: () => void;
  loading: boolean;
  isSubmitted: boolean;
  setIsSubmitted: Dispatch<SetStateAction<boolean>>;
  hasChanges: boolean;
  clientEquipments: ClientEquipmentI[];
  clientCargos: ClientCargoI[];
  shipTempId: string;
  isSkillLimitReached: boolean;
  MAX_SKILLS: number;
}

export type ShipDetailsFormDataI = {
  imoNumber: ImoTypeI;
  shipName: string;
  shipSize: string;
  subVesselType: SearchResultI;
  power: string;
  capacity: string;
  fromDate: string;
  toDate: string | null;
  deadWeight: string;
  grossTonnage: string;
  additionalDetails: string | null;
  department: SearchResultI;
};

export type ShipTabsI = {
  equipment: React.ReactNode;
  cargo: React.ReactNode;
};

export type ShipEntitySearchResultI = {
  dataType: string;
  imageUrl: string;
  imo: string;
  matchedName: string;
  name: string;
};

type ImoTypeI = {
  imo: string;
  dataType: string;
};

export type ShipTypeEntitySearchResultI = {
  dataType: 'raw' | 'master';
  capacityUnitType: string;
  id: string;
  name: string;
};

export type FieldTypeI = {
  designation: SearchResultI;
  fromDate: string;
  id: string;
  ships: FieldShipType[];
  toDate?: string | null;
  experienceDesignationId?: string;
};

export type FieldShipType = {
  fromDate: string;
  id: string;
  name: string;
  ship: Omit<SearchResultI, 'id'> & { imo: string };
  subVesselType: SearchResultI;
  toDate?: string | null;
};

export type ShipEquipmentI = {
  category: SearchResultI;
  manufacturerName: string;
  model: string;
  id: string;
};

export type ShipPayloadI = {
  opr?: 'UPDATE' | 'CREATE' | 'DELETE' | 'NESTED_OPR';
  ship?: ImoTypeI;
  name?: string;
  sizeGt?: number;
  powerKw?: number;
  fromDate?: string;
  toDate?: string | null;
  details?: string;
  department?: IdTypeI;
  subVesselType?: IdTypeI;
  dwt?: number;
  id?: string;
  skills?: {
    opr: string;
    id: string;
    dataType: 'raw' | 'master';
  }[];
  equipmentCategories?: {
    opr: 'CREATE';
    equipmentCategory: {
      id: string;
      dataType: string;
    };
    manufacturerName: string;
    model: string;
    powerCapacity: number;
    details?: string;
    fuelTypes: {
      opr: 'CREATE';
      fuelType: {
        id: string;
        dataType: string;
      };
    }[];
  }[];
  cargos?: {
    opr: 'CREATE';
    name: string;
    description: string;
    fromDate: string;
    toDate: string | null;
  }[];
};

export type ShipCreateEditPayloadI = {
  opr: 'UPDATE' | 'CREATE' | 'DELETE' | 'NESTED_OPR';
  entity?: SearchResultI;
  id?: string;
  designations: {
    designation?: SearchResultI;
    fromDate?: string;
    id?: string;
    opr?: string;
    ships?: ShipPayloadI[];
    toDate?: string;
  }[];
};

export type ShipPreFilledDataTypeI = {
  opr: 'UPDATE' | 'CREATE' | 'DELETE' | 'NESTED_OPR';
  entity: SearchResultI;
  designations: {
    designation: SearchResultI;
    fromDate: string;
    id: string;
    opr: string;
    toDate: string;
  }[];
};
