/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import SafeArea from '@/src/components/SafeArea';
import { ProfileStackParamsListI } from '@/src/navigation/types';
import EditExperienceItem from './components/EditExperienceItem';

type RouteProps = RouteProp<ProfileStackParamsListI, 'EditExperienceItem'>;

const EditExperienceItemScreen = () => {
  const route = useRoute<RouteProps>();
  const { experienceId } = route.params || {};
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();

  return (
    <SafeArea>
      <EditExperienceItem onBack={navigation.goBack} experienceId={experienceId} />
    </SafeArea>
  );
};

export default EditExperienceItemScreen;
