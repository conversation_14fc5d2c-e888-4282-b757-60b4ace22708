import { apiCall } from '@/src/services/api';
import { NotificationUpdateReadI, NotificationUpdateReadResult } from './types';

export const updateNotificationRead = async (
  notificationIds: string[],
): Promise<NotificationUpdateReadResult> => {
  const result = await apiCall<NotificationUpdateReadI, NotificationUpdateReadResult>(
    `/communication/api/v1/notifications/read`,
    'PATCH',
    {
      isAuth: true,
      payload: {
        profileId: '',
        type: 'UPDATE_READ',
        ids: notificationIds,
      },
    },
  );

  return result;
};
