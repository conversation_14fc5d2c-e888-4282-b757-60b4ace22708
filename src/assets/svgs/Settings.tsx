/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const Settings: React.FC<FilledIconPropsI> = ({
  width = 3.968,
  height = 3.968,
  color,
  ...props
}) => {
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 32 32"
      fill="none"
      {...props}
    >
      <Path
        d="M16.001 10.588a5.455 5.455 0 00-3.02.912 5.417 5.417 0 00-2.003 2.43 5.388 5.388 0 001.179 5.897A5.46 5.46 0 0018.082 21a5.431 5.431 0 002.44-1.993c.597-.89.916-1.937.916-3.007a5.407 5.407 0 00-1.595-3.825 5.457 5.457 0 00-3.842-1.587zm0 9.412a4.032 4.032 0 01-2.232-.674 4.004 4.004 0 01-1.48-1.795 3.984 3.984 0 01.87-4.36 4.025 4.025 0 014.38-.866 4.015 4.015 0 011.803 1.473 3.986 3.986 0 01-.5 5.05A4.028 4.028 0 0116.003 20zm12.765-6.4a.705.705 0 00-.345-.47l-3.598-2.045-.05-.084-.014-4.036a.7.7 0 00-.248-.537 13.031 13.031 0 00-4.263-2.39.712.712 0 00-.572.054l-3.62 2.01h-.119l-3.61-2.012a.712.712 0 00-.575-.052 13.007 13.007 0 00-4.26 2.395.706.706 0 00-.251.535l-.019 4.04a1.965 1.965 0 00-.048.084L3.58 13.126a.71.71 0 00-.344.476 12.275 12.275 0 000 4.798.704.704 0 00.345.47l3.595 2.04.05.083.014 4.036a.703.703 0 00.25.543 13.033 13.033 0 004.264 2.39.71.71 0 00.572-.054l3.617-2.01h.119l3.611 2.013a.723.723 0 00.574.051 13.007 13.007 0 004.255-2.395.708.708 0 00.25-.535l.019-4.04c.017-.027.033-.056.049-.084l3.595-2.034a.706.706 0 00.345-.476c.317-1.584.319-3.214.005-4.798zm-1.33 4.21l-3.478 1.966a.716.716 0 00-.27.271c-.07.118-.143.248-.22.37a.703.703 0 00-.107.371l-.02 3.907a11.615 11.615 0 01-3.252 1.827l-3.497-1.948a.712.712 0 00-.346-.09h-.459a.713.713 0 00-.363.089l-3.506 1.948A11.596 11.596 0 018.66 24.7l-.014-3.902a.704.704 0 00-.107-.372 7.073 7.073 0 01-.22-.37.708.708 0 00-.268-.27l-3.479-1.973a10.891 10.891 0 010-3.623l3.473-1.965a.716.716 0 00.274-.271c.07-.118.143-.248.22-.37a.703.703 0 00.107-.371l.02-3.907a11.614 11.614 0 013.247-1.827l3.497 1.948a.69.69 0 00.363.09h.442a.712.712 0 00.363-.09l3.504-1.948A11.597 11.597 0 0123.34 7.3l.014 3.902c0 .132.037.26.107.372.076.118.15.245.22.37a.707.707 0 00.268.27l3.481 1.974c.204 1.2.205 2.423.004 3.623h.002z"
        fill={color || '#000'}
      />
    </Svg>
  );
};

export default Settings;
