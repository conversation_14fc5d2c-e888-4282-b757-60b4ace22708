/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const EyeOpen: React.FC<FilledIconPropsI> = ({
  width = 2.728,
  height = 1.364,
  fill = '#737373',
  color,
  disabled,
  accessibilityLabel = 'Eye Open',
  ...props
}) => {
  const fillColor = color || fill;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 22 11"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0.26408 0.166151C0.57529 -0.0873991 1.03769 -0.0461294 1.29687 0.258311C3.01035 2.27104 6.1184 4.7826 11 4.7826C15.8815 4.7826 18.9895 2.27106 20.703 0.258331C20.9622 -0.0461093 21.4246 -0.0873691 21.7358 0.166171C22.047 0.419721 22.0892 0.872061 21.83 1.17651C21.3016 1.79723 20.6469 2.46667 19.8577 3.10857C19.9001 3.15503 19.937 3.2077 19.9669 3.2661L21.9224 7.0921C22.1035 7.4465 21.9567 7.8774 21.5945 8.0546C21.2322 8.2318 20.7917 8.0881 20.6106 7.7338L18.6856 3.9674C17.4652 4.7689 15.9983 5.459 14.2651 5.8565L15.1412 10.1418C15.2206 10.5303 14.9631 10.9082 14.5659 10.9859C14.1688 11.0636 13.7825 10.8117 13.703 10.4232L12.8209 6.1086C12.2402 6.1793 11.6334 6.2173 11 6.2173C10.3665 6.2173 9.7597 6.1793 9.1789 6.1086L8.29684 10.4232C8.21741 10.8117 7.83107 11.0636 7.43393 10.9859C7.03679 10.9082 6.77924 10.5303 6.85866 10.1418L7.73478 5.8564C6.30485 5.5285 5.0562 5.0014 3.97768 4.3769L1.32003 7.8434C1.07703 8.1603 0.61738 8.2246 0.29337 7.9869C-0.03063 7.7491 -0.096295 7.2995 0.14671 6.9825L2.75501 3.5804C1.6858 2.80207 0.8295 1.95132 0.16987 1.17649C-0.089314 0.872041 -0.047134 0.419701 0.26408 0.166151Z"
        fill={fillColor}
      />
    </Svg>
  );
};

export default EyeOpen;
