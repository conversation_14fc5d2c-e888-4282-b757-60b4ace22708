import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { selectAboutProfileCertificationsValueAddedCount } from '@/src/redux/selectors/about';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import { fetchValueAddedCertificationsAPI } from '@/src/networks/profile/valueAddedCertifications';
import { ProfileValueAddedCertificationI } from './types';

export const useProfileValueAdded = (profileId: string) => {
  const [certifications, setCertifications] = useState<ProfileValueAddedCertificationI[]>([]);
  const count = useSelector(selectAboutProfileCertificationsValueAddedCount);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchCertifications = async () => {
      try {
        setLoading(true);
        const response = await fetchValueAddedCertificationsAPI(profileId);
        setCertifications(response);
      } catch (error) {
        handleError(error, {
          handle4xxError: () => {
            showToast({
              message: 'Failed to Fetch Certifications',
              type: 'error',
            });
          },
          handle5xxError: () => {
            showToast({
              message: 'Server Error',
              description: 'Please try again later',
              type: 'error',
            });
          },
        });
      } finally {
        setLoading(false);
      }
    };

    fetchCertifications();
  }, [profileId]);

  return {
    certifications,
    count,
    loading,
  };
};
