/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState } from 'react';
import { Pressable, Text } from 'react-native';
import BouncyCheckbox from 'react-native-bouncy-checkbox';
import { twMerge } from 'tailwind-merge';
import { CheckboxProps } from './types';

const Checkbox = ({
  label,
  className,
  checked,
  onValueChange,
  disabled,
  labelClassName,
  size = 16,
}: CheckboxProps) => {
  const [internalChecked, setInternalChecked] = useState(false);

  const isChecked = checked !== undefined ? checked : internalChecked;

  const handlePress = (newVal: boolean) => {
    if (onValueChange) {
      onValueChange(newVal);
    } else {
      setInternalChecked(newVal);
    }
  };

  return (
    <Pressable
      className={twMerge('flex-row', className)}
      onPress={() => handlePress(!isChecked)}
      disabled={disabled}
    >
      <BouncyCheckbox
        isChecked={isChecked}
        onPress={handlePress}
        disabled={disabled}
        iconStyle={{
          borderColor: '#737373',
          borderWidth: 2,
          borderRadius: 4,
        }}
        innerIconStyle={{
          borderColor: '#737373',
          borderWidth: 2,
          borderRadius: 4,
        }}
        fillColor="#448600"
        text=""
        size={size}
      />
      <Text className={twMerge('text-base text-subLabelGray', labelClassName)}>{label}</Text>
    </Pressable>
  );
};

export default Checkbox;
