import type React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import type { OutlinedIconPropsI } from './types';

const Download: React.FC<OutlinedIconPropsI> = ({
  width = 2.604,
  height = 2.48,
  stroke = '#1C274C',
  color,
  strokeWidth = 1.5,
  accessibilityLabel = 'Download',
  disabled,
  ...props
}) => {
  const strokeColor = color || stroke;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 21 20"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        d="M10.5 2V14M7 11L10.5 14L14 11"
        stroke={strokeColor}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M3.625 12.5C3.625 12.1548 3.34518 11.875 3 11.875C2.65483 11.875 2.375 12.1548 2.375 12.5V12.5458C2.37498 13.6854 2.37497 14.604 2.4721 15.3265C2.57295 16.0766 2.78869 16.7081 3.29029 17.2097C3.79189 17.7113 4.42345 17.9271 5.17354 18.0279C5.89602 18.125 6.81462 18.125 7.95428 18.125H13.0458C14.1854 18.125 15.104 18.125 15.8265 18.0279C16.5766 17.9271 17.2081 17.7113 17.7097 17.2097C18.2113 16.7081 18.4271 16.0766 18.5279 15.3265C18.625 14.604 18.625 13.6854 18.625 12.5458V12.5C18.625 12.1548 18.3452 11.875 18 11.875C17.6548 11.875 17.375 12.1548 17.375 12.5C17.375 13.6962 17.3737 14.5304 17.2891 15.1599C17.2068 15.7714 17.0565 16.0952 16.8258 16.3258C16.5952 16.5565 16.2714 16.7068 15.6599 16.7891C15.0304 16.8737 14.1962 16.875 13 16.875H8C6.80383 16.875 5.96956 16.8737 5.3401 16.7891C4.72863 16.7068 4.40481 16.5565 4.17418 16.3258C3.94354 16.0952 3.79317 15.7714 3.71096 15.1599C3.62633 14.5304 3.625 13.6962 3.625 12.5Z"
        fill={color || '#1C274C'}
      />
    </Svg>
  );
};

export default Download;
