/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from '@/src/assets/svgs/types';

type DownVoteProps = OutlinedIconPropsI & {
  isLiked?: boolean;
};

const DownVote: React.FC<DownVoteProps> = ({
  width = 2.0,
  height = 2.0,
  isLiked = false,
  color,
  strokeWidth = 1,
  accessibilityLabel = 'Down Vote',
  disabled,
  ...props
}) => {
  const opacity = disabled ? 0.5 : 1;
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 16 16"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        d="M.73 7.347A.562.562 0 011.25 7h2.812V1.375A1.125 1.125 0 015.187.25h5.625a1.125 1.125 0 011.125 1.125V7h2.813a.563.563 0 01.398.96l-6.75 6.75a.562.562 0 01-.796 0L.852 7.96a.562.562 0 01-.122-.613z"
        fill={isLiked ? '#448600' : '#525252'}
      />
    </Svg>
  );
};

export default DownVote;
