/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { Image, Text, View } from 'react-native';
import { OnboardingSlideProps } from './types';

const OnboardingSlide = ({ title, description, image }: OnboardingSlideProps) => {
  return (
    <View className="items-center px-6">
      <View className="w-full aspect-square bg-gray-100 rounded-lg mb-5">
        <Image source={image} className="w-full h-full" resizeMode="cover" />
      </View>
      <Text className="text-2xl font-semibold text-center text-gray-900 mb-2">{title}</Text>
      <Text className="text-base text-center text-gray-600 px-4">{description}</Text>
    </View>
  );
};

export default OnboardingSlide;
