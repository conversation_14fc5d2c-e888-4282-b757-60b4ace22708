import { useEffect, useRef, useState } from 'react';
import { Pressable, Text, View, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import { useNavigation, type NavigationAction } from '@react-navigation/native';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import EntitySearch from '@/src/components/EntitySearch';
import CustomModal from '@/src/components/Modal';
import { StatusLabel } from '@/src/components/StatusLabel';
import type { StatusVariantI } from '@/src/components/StatusLabel/types';
import TextInput from '@/src/components/TextInput';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import type { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { capitalizeFirstLetter } from '@/src/utilities/data/string';
import { STATUS_LABEL_MAP } from './const';
import type { PropsI } from './types';
import { useEditShipDetails } from './useHook';

const EditShipDetailForm = (props: PropsI) => {
  const { type, handleBack, action, text } = props;
  const { methods, onSubmit, categories, loading, isSubmitted, setIsSubmitted, hasChanges } =
    useEditShipDetails(type, text);
  const flagSelection = useSelector(selectSelectionByKey('country')) as SearchResultI;
  const subVesselTypeSelection = useSelector(
    selectSelectionByKey('subVesselType'),
  ) as SearchResultI;
  const { handleSubmit, control, watch } = methods;

  const isAddNewShip = action === 'add';
  const navigation = useNavigation();
  const [showDiscardModal, setShowDiscardModal] = useState(false);
  const exitActionRef = useRef<NavigationAction | null>(null);

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', (e) => {
      if (hasChanges && !loading) {
        e.preventDefault();
        exitActionRef.current = e.data.action;
        setShowDiscardModal(true);
      }
    });
    return unsubscribe;
  }, [navigation, hasChanges, loading]);

  const handleDiscardConfirm = () => {
    setShowDiscardModal(false);
    setTimeout(() => {
      if (exitActionRef.current) {
        navigation.dispatch(exitActionRef.current);
      }
    }, 300);
  };

  const handleDiscardCancel = () => {
    setShowDiscardModal(false);
  };

  const handleSave = () => {
    setIsSubmitted(true);
    handleSubmit(onSubmit)();
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1"
    >
      <ScrollView className="flex-1 bg-white">
        <View className="flex-row items-center justify-between p-3">
          <BackButton
            onBack={handleBack}
            label={`${capitalizeFirstLetter(type)} details`}
            labelClassname="text-lg leading-6 font-medium"
          />
          <Pressable onPress={handleSave} disabled={loading || !hasChanges}>
            <Text
              className={`text-lg font-medium ${loading || !hasChanges ? 'text-gray-400' : 'text-primaryGreen'}`}
            >
              {loading ? 'Saving...' : 'Save'}
            </Text>
          </Pressable>
        </View>
        <View className="flex flex-col gap-6 p-3">
          {categories.map((category) => {
            const showStatus = !isAddNewShip && !!category.status;
            if (category.key === 'flag') {
              return (
                <View key={category.id} className="flex gap-1">
                  <Controller
                    control={control}
                    name={category.key}
                    rules={{ required: 'Flag is required' }}
                    render={({ fieldState: { error } }) => (
                      <EntitySearch
                        title={category.title}
                        placeholder={`Search ${category.title.toLowerCase()}`}
                        selectionKey="country"
                        data={flagSelection ? flagSelection.name : watch('flag') || ''}
                        className="my-0"
                        error={
                          isSubmitted && !flagSelection?.id
                            ? `${category.title} is required`
                            : error?.message
                        }
                      />
                    )}
                  />
                  {showStatus && category.status ? (
                    <StatusLabel
                      label={STATUS_LABEL_MAP[category.status as keyof typeof STATUS_LABEL_MAP]}
                      variant={category.status as StatusVariantI}
                    />
                  ) : null}
                </View>
              );
            }
            if (category.key === 'subVesselType') {
              return (
                <View key={category.id} className="flex gap-1">
                  <Controller
                    control={control}
                    name={category.key}
                    rules={{ required: 'Ship Type is required' }}
                    render={({ fieldState: { error } }) => (
                      <EntitySearch
                        title={category.title}
                        placeholder={`Search ${category.title.toLowerCase()}`}
                        selectionKey="subVesselType"
                        className="my-0"
                        data={
                          subVesselTypeSelection
                            ? subVesselTypeSelection.name
                            : watch('subVesselType') || ''
                        }
                        error={
                          isSubmitted && !subVesselTypeSelection?.id
                            ? `${category.title} is required`
                            : error?.message
                        }
                      />
                    )}
                  />
                  {showStatus && category.status ? (
                    <StatusLabel
                      label={STATUS_LABEL_MAP[category.status as keyof typeof STATUS_LABEL_MAP]}
                      variant={category.status as StatusVariantI}
                    />
                  ) : null}
                </View>
              );
            }
            return (
              <View key={category.id} className="flex gap-1">
                <Controller
                  control={control}
                  name={category.key}
                  rules={{
                    required: ['imo', 'name'].includes(category.key)
                      ? `${category.title} is required`
                      : false,
                  }}
                  render={({ field: { onChange, value }, fieldState: { error } }) => (
                    <TextInput
                      label={category.title}
                      value={value}
                      onChangeText={onChange}
                      placeholder={`Enter ${category.title}`}
                      error={error?.message}
                    />
                  )}
                />
                {showStatus && category.status ? (
                  <StatusLabel
                    label={STATUS_LABEL_MAP[category.status as keyof typeof STATUS_LABEL_MAP]}
                    variant={category.status as StatusVariantI}
                  />
                ) : null}
              </View>
            );
          })}
        </View>
        <CustomModal
          isVisible={showDiscardModal}
          title="Discard changes?"
          description="You have unsaved changes. Are you sure you want to discard them and leave this screen?"
          onCancel={handleDiscardCancel}
          onConfirm={handleDiscardConfirm}
          cancelText="Cancel"
          confirmText="Discard"
          confirmButtonVariant="danger"
        />
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default EditShipDetailForm;
