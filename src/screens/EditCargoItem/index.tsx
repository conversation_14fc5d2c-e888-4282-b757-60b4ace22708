import { KeyboardAvoidingView, Platform } from 'react-native';
import { type RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import SafeArea from '@/src/components/SafeArea';
import type { ProfileStackParamsListI } from '@/src/navigation/types';
import EditCargoItem from './components/EditCargoItem';

type RouteProps = RouteProp<ProfileStackParamsListI, 'EditCargoItem'>;

const EditCargoItemScreen = () => {
  const route = useRoute<RouteProps>();
  const {
    profileId,
    experienceId,
    shipId,
    cargoId,
    data,
    refetch,
    shipData,
    isClientMode,
    shipTempId,
  } = route.params || {};
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();

  return (
    <SafeArea>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <EditCargoItem
          onBack={navigation.goBack}
          profileId={profileId}
          experienceId={experienceId}
          shipId={shipId!}
          cargoId={cargoId!}
          preFilledData={data}
          refetch={refetch}
          shipData={shipData}
          isClientMode={isClientMode}
          shipTempId={shipTempId}
        />
      </KeyboardAvoidingView>
    </SafeArea>
  );
};

export default EditCargoItemScreen;
