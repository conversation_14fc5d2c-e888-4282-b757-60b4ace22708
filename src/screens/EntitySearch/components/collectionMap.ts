type CertificateType = 'STATUTORY' | 'VALUE_ADDED' | 'INSTITUTION' | 'ORGANIZATION';

export const CERTIFICATE_TYPES_ROUTE_MAP: Record<string, CertificateType> = {
  statutory: 'STATUTORY',
  'value-added': 'VALUE_ADDED',
  institution: 'INSTITUTION',
  organisation: 'ORGANIZATION',
} as const;

export const TYPE_COLLECTIONS = new Set([
  'certificate-course',
  'entity',
  'skill',
  'equipmentCategory',
]);
