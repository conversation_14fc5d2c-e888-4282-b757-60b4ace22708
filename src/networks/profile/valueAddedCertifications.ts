import { apiCall } from '@/src/services/api';
import {
  FetchUserProfileValueAddedCertificationsBodyI,
  FetchUserProfileValueAddedCertificationsResultI,
} from './types';

export const fetchValueAddedCertificationsAPI = async (
  profileId: string,
): Promise<FetchUserProfileValueAddedCertificationsResultI[]> => {
  const result = await apiCall<
    FetchUserProfileValueAddedCertificationsBodyI,
    FetchUserProfileValueAddedCertificationsResultI[]
  >('/backend/api/v1/career/profile-certificates', 'GET', {
    isAuth: true,
    query: {
      profileId: profileId,
      type: 'VALUE_ADDED',
      pageSize: 3,
    },
  });

  return result;
};
