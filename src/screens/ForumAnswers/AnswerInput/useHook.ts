import { useState, useRef } from 'react';
import { Keyboard, TextInput } from 'react-native';
import { pick, types } from '@react-native-documents/picker';
import { showToast } from '@/src/utilities/toast';
import { uploadFileWithPresignedUrl } from '@/src/utilities/upload/upload';
import { fetchPresignedUrlAPI } from '@/src/networks/storage/presignedUrl';
import type { AttachmentI, MediaI } from './types';

export const MAX_ANSWER_LENGTH = 2000;
const MAX_FILES = 5;

export const useAnswerInput = (onSubmit: (text: string, files: AttachmentI[]) => void) => {
  const [answer, setAnswer] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [attachments, setAttachments] = useState<MediaI[]>([]);
  const inputRef = useRef<TextInput>(null);

  const canSubmit = (answer.trim().length > 0 || attachments.length > 0) && !isSubmitting;
  const isNearLimit = answer.length > MAX_ANSWER_LENGTH * 0.8;

  const handleAttachments = async () => {
    try {
      const remainingSlots = MAX_FILES - attachments.length;
      if (remainingSlots <= 0) {
        showToast({
          message: 'Limit reached',
          description: `You can only upload up to ${MAX_FILES} files.`,
          type: 'info',
        });
        return;
      }

      const files = await pick({
        allowMultiSelection: true,
        type: [types.images, types.pdf, types.plainText],
      });

      if (!files || files.length === 0) return;

      const limitedFiles = files.slice(0, remainingSlots);
      const allowedMimeTypes = [
        'image/jpeg',
        'image/png',
        'image/jpg',
        'application/pdf',
        'text/plain',
      ];
      const MAX_FILE_SIZE = 4 * 1024 * 1024;

      const validFiles = limitedFiles.filter((file) => {
        const mimeType = file.type ?? '';
        const fileSize = file.size ?? 0;
        if (!allowedMimeTypes.includes(mimeType)) return false;
        if (fileSize > MAX_FILE_SIZE) return false;
        return true;
      });

      const invalidTypeFiles = limitedFiles.filter(
        (file) => !allowedMimeTypes.includes(file.type ?? ''),
      );
      const oversizedFiles = limitedFiles.filter((file) => (file.size ?? 0) > MAX_FILE_SIZE);

      if (oversizedFiles.length > 0) {
        showToast({
          message: 'File too large',
          description: 'Files must be 4MB or smaller.',
          type: 'error',
        });
      }

      if (invalidTypeFiles.length > 0) {
        showToast({
          message: 'Some files skipped',
          description: 'Only images, PDFs, and text files are allowed.',
          type: 'info',
        });
      }

      const newAttachments: MediaI[] = validFiles.map((file) => ({
        uri: file.uri,
        type: file.type ?? 'application/octet-stream',
        filename: file.name ?? 'Unknown file',
      }));

      setAttachments((prev) => [...prev, ...newAttachments]);
    } catch (error) {
      if (
        error &&
        typeof error === 'object' &&
        'code' in error &&
        error.code !== 'DOCUMENT_PICKER_CANCELED'
      ) {
        showToast({
          message: 'Error',
          description: 'Failed to select files',
          type: 'error',
        });
      }
    }
  };

  const handleDeleteAttachment = (index: number) => {
    setAttachments((prev) => prev.filter((_, i) => i !== index));
  };

  const uploadAttachments = async (attachmentsToUpload: MediaI[]): Promise<AttachmentI[]> => {
    if (attachmentsToUpload.length === 0) return [];

    try {
      const extensions = attachmentsToUpload.map(
        (file) => file.filename.split('.').pop()?.toLowerCase() || 'unknown',
      );
      const response = await fetchPresignedUrlAPI(extensions, 'FORUM');

      if (!response || !Array.isArray(response) || response.length !== attachmentsToUpload.length) {
        throw new Error('Failed to get upload URLs');
      }

      const uploadPromises = attachmentsToUpload.map((file, index) => {
        const presignedData = response[index];
        return uploadFileWithPresignedUrl(file, presignedData.uploadUrl).then(() => ({
          ...file,
          fileUrl: presignedData.accessUrl,
          fileExtension: file.filename.split('.').pop()?.toLowerCase() || 'unknown',
        }));
      });

      return await Promise.all(uploadPromises);
    } catch (error) {
      showToast({
        message: 'Upload Failed',
        description:
          error instanceof Error ? error.message : 'An unknown error occurred during upload.',
        type: 'error',
      });
      return [];
    }
  };

  const handleSend = async () => {
    if (canSubmit) {
      setIsSubmitting(true);
      try {
        const uploadedFiles = await uploadAttachments(attachments);
        onSubmit(answer.trim(), uploadedFiles);
        setAnswer('');
        setAttachments([]);
        Keyboard.dismiss();
      } catch (error) {
        // Error is already handled in uploadAttachments
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  return {
    answer,
    setAnswer,
    isSubmitting,
    attachments,
    inputRef,
    canSubmit,
    isNearLimit,
    handleAttachments,
    handleDeleteAttachment,
    handleSend,
  };
};
