/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ImageStyle, StyleProp } from 'react-native';

export interface NotFoundProps {
  title?: string;
  subtitle?: string;
  className?: string;
  titleClassName?: string;
  imageStyle?: StyleProp<ImageStyle>;
  fullScreen?: boolean;
}
