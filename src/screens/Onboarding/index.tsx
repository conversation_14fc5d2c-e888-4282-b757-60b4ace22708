/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Carousel from '@/src/components/Carousel';
import Logo from '@/src/components/Logo';
import SafeArea from '@/src/components/SafeArea';
import { AppStackParamListI } from '@/src/navigation/types';
import useStorage from '@/src/hooks/storage';
import AuthButtons from './components/AuthButtons';
import OnboardingSlide from './components/OnboardingSlide';

const ONBOARDING_DATA = [
  {
    title: 'Connect With Community',
    description: 'Join a vibrant network of professionals and enthusiasts in your field.',
    image: require('@/src/assets/images/onboarding/connect.jpg'),
  },
  {
    title: 'Showcase Your Work',
    description: 'Create a portfolio that highlights your skills and accomplishments.',
    image: require('@/src/assets/images/onboarding/showcase.jpg'),
  },
  {
    title: 'Discover Opportunities',
    description: 'Find jobs, collaborations, growth possibilities tailored to your interests.',
    image: require('@/src/assets/images/onboarding/opportunities.jpg'),
  },
];

const OnboardingScreen = () => {
  const { setStorage } = useStorage();
  const navigation = useNavigation<StackNavigationProp<AppStackParamListI>>();

  const handleCreateAccount = async () => {
    await setStorage('visitedOnboarding', 'true');
    navigation.navigate('CreateAccount');
  };

  const handleSignIn = async () => {
    await setStorage('visitedOnboarding', 'true');
    navigation.navigate('UserLogin');
  };

  return (
    <SafeArea>
      <View className="mt-10 mx-auto">
        <Logo compact={false} />
      </View>
      <View className="flex-1 mt-8">
        <Carousel autoPlay={true} showArrows={false} duration={2000} dotClassName="w-12 h-2">
          {ONBOARDING_DATA.map(({ title, description, image }, index) => (
            <OnboardingSlide key={index} title={title} description={description} image={image} />
          ))}
        </Carousel>
      </View>
      <AuthButtons onCreateAccount={handleCreateAccount} onSignIn={handleSignIn} />
    </SafeArea>
  );
};

export default OnboardingScreen;
