import { useEffect, useRef, useState } from 'react';
import { ActivityIndicator, Pressable, ScrollView, Text, View } from 'react-native';
import { useNavigation, type NavigationAction } from '@react-navigation/native';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import ChipInput from '@/src/components/ChipInput';
import EntitySearch from '@/src/components/EntitySearch';
import CustomModal from '@/src/components/Modal';
import SafeArea from '@/src/components/SafeArea';
import TextInput from '@/src/components/TextInput';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import type { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import type { EditEquipmentItemPropsI, EquipmentDetailsFormDataI } from './types';
import { useEditEquipmentItem } from './useHook';

const EditEquipmentItem = ({
  onBack,
  equipmentId,
  preFilledData,
  refetch,
  isClientMode = false,
  onClientSave,
  shipTempId,
}: EditEquipmentItemPropsI & {
  isClientMode?: boolean;
  onClientSave?: (equipment: any) => void;
  shipTempId?: string;
}) => {
  const {
    methods,
    onSubmit,
    isSubmitting,
    localFuelTypes,
    setLocalFuelTypes,
    clearFields,
    isSubmitted,
    setIsSubmitted,
    loading,
    hasChanges,
    handleClientSave,
    isSaving,
    isFuelLimitReached,
    MAX_FUEL_TYPES,
  } = useEditEquipmentItem(
    equipmentId,
    preFilledData,
    refetch,
    isClientMode,
    onClientSave,
    shipTempId,
  );

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = methods;
  const categorySelection = useSelector(selectSelectionByKey('category')) as
    | (SearchResultI & { hasFuelType: boolean })
    | undefined;

  const [categorySet, setCategorySet] = useState(false);
  const [showDiscardModal, setShowDiscardModal] = useState(false);
  const exitActionRef = useRef<NavigationAction | null>(null);
  const navigation = useNavigation();

  const watchedCategory = watch('category');

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', (e) => {
      if (hasChanges && !isSubmitting && !isSaving) {
        e.preventDefault();
        exitActionRef.current = e.data.action;
        setShowDiscardModal(true);
      }
    });
    return unsubscribe;
  }, [navigation, hasChanges, isSubmitting, isSaving]);

  const handleDiscardConfirm = () => {
    setShowDiscardModal(false);
    setTimeout(() => {
      clearFields();
      if (exitActionRef.current) {
        navigation.dispatch(exitActionRef.current);
      }
    }, 300);
  };

  const handleDiscardCancel = () => {
    setShowDiscardModal(false);
  };

  const handleBackPress = () => {
    clearFields();
    onBack();
  };

  useEffect(() => {
    if (categorySelection && categorySelection.id !== watchedCategory?.id && !categorySet) {
      setValue('category', categorySelection);
      setCategorySet(true);
    }
  }, [categorySelection, setValue, watchedCategory, categorySet]);

  const handleFuelTypeRemove = (id: string | number) => {
    setLocalFuelTypes((prev) => prev.filter((s) => s.id !== id));
  };

  const renderField = (
    name: keyof Pick<
      EquipmentDetailsFormDataI,
      'manufacturerName' | 'model' | 'power' | 'additionalDetails'
    >,
    label: string,
    isRequired?: boolean,
  ) => (
    <Controller
      control={control}
      name={name}
      rules={isRequired ? { required: `${label} is required` } : {}}
      render={({ field: { onChange, value }, fieldState: { error } }) => (
        <TextInput
          label={label}
          value={value}
          onChangeText={onChange}
          placeholder={`Enter ${label.toLowerCase()}`}
          error={error?.message}
          className="py-3"
          key={label}
        />
      )}
    />
  );

  const handleSave = () => {
    if (isFuelLimitReached) {
      return;
    }
    setIsSubmitted(true);
    if (isClientMode) {
      handleSubmit(handleClientSave)();
    } else {
      handleSubmit(onSubmit)();
    }
  };

  const shouldShowFuelType = watchedCategory?.hasFuelType || categorySelection?.hasFuelType;
  const saveButtonText = isSubmitting || isSaving ? 'Saving...' : 'Save';
  const saveButtonDisabled = isSubmitting || isSaving || !hasChanges || isFuelLimitReached;
  const saveButtonStyle = `text-lg font-medium ${saveButtonDisabled ? 'text-gray-400' : 'text-[#448600]'}`;

  if (loading) {
    return (
      <SafeArea>
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="small" color="#448600" />
          <Text className="mt-4 text-gray-600">Loading equipment details...</Text>
        </View>
      </SafeArea>
    );
  }

  return (
    <ScrollView
      className="flex-1 bg-white px-4"
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{ flexGrow: 1 }}
    >
      <View className="flex-row items-center justify-between py-4">
        <BackButton
          onBack={handleBackPress}
          label={equipmentId ? 'Edit Equipment' : 'Create Equipment'}
        />
        <Pressable onPress={handleSave} disabled={saveButtonDisabled}>
          <Text className={saveButtonStyle}>{saveButtonText}</Text>
        </Pressable>
      </View>
      <EntitySearch
        title="Category"
        placeholder="Enter Category"
        selectionKey="category"
        data={watchedCategory?.name}
        error={
          isSubmitted && !watchedCategory?.id ? 'Category is required' : errors.category?.message
        }
      />
      {renderField('manufacturerName', 'Manufacturer Name', true)}
      {renderField('model', 'Model', true)}
      {renderField('power', 'Power/Capacity', false)}
      {shouldShowFuelType && (
        <>
          <ChipInput
            title="Fuel Type"
            chips={localFuelTypes}
            onRemove={handleFuelTypeRemove}
            disabled={isFuelLimitReached}
            error={
              isFuelLimitReached ? `Fuel type limit reached (max ${MAX_FUEL_TYPES})` : undefined
            }
          />
        </>
      )}
      {renderField('additionalDetails', 'Additional Details(Optional)', false)}
      <CustomModal
        isVisible={showDiscardModal}
        title="Discard changes?"
        description="You have unsaved changes. Are you sure you want to discard them and leave this screen?"
        onCancel={handleDiscardCancel}
        onConfirm={handleDiscardConfirm}
        cancelText="Cancel"
        confirmText="Discard"
      />
    </ScrollView>
  );
};

export default EditEquipmentItem;
