/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ActivityIndicator, FlatList, Pressable, Text, TextInput, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useDispatch } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import CustomModal from '@/src/components/Modal';
import { setSelection } from '@/src/redux/slices/entitysearch/searchSlice';
import { AppDispatch } from '@/src/redux/store';
import { BottomTabNavigationI } from '@/src/navigation/types';
import Close from '@/src/assets/svgs/Close';
import Search from '@/src/assets/svgs/Search';
import AddFields from '../AddFields';
import { TYPE_COLLECTIONS } from '../collectionMap';
import { HeaderComponentPropsI, OptionPropsI, SearchScreenProps } from './types';
import useSearchScreen from './useHook';

const Option = ({ item, select }: OptionPropsI) => {
  return (
    <Pressable
      onPress={() => select(item)}
      className="px-5 py-4 bg-white border-b border-gray-100 active:bg-gray-50"
    >
      <Text className="text-base font-inter text-gray-800">{item?.name}</Text>
    </Pressable>
  );
};

const HeaderComponent = ({
  handleAdd,
  text,
  title,
  navigation,
  selectionKey,
  dispatch,
  multipleSelection,
  selectedOptions,
  deSelect,
}: HeaderComponentPropsI) => {
  const isDesignation = title.toLowerCase() === 'designation';
  const isImoNumber =
    title.toLowerCase().includes('imo number') || title.toLowerCase().includes('imo');
  const handleAspirantSelect = () => {
    dispatch(
      setSelection({
        key: selectionKey,
        value: {
          id: '9a6f9c02-1b6c-4a13-8f7d-9e2c6d4f4a92',
          name: 'Aspirant',
          dataType: 'master',
        },
      }),
    );
    navigation.goBack();
  };

  const handleImoAdd = () => {
    if (isImoNumber && text) {
      navigation.navigate('EditDetail', { type: 'ship', action: 'add', text });
    } else {
      handleAdd();
    }
  };

  return (
    <View className="mb-2">
      {text && (
        <Pressable className="px-5 py-4 bg-white border-b border-gray-100" onPress={handleImoAdd}>
          <Text className="text-base font-medium text-[#5a8d3b]">Add (if not found)</Text>
          <Text className="text-sm font-normal text-gray-800">{` ${text}`}</Text>
        </Pressable>
      )}
      {isDesignation && (
        <Pressable
          className="px-5 py-4 bg-white border-b border-gray-100"
          onPress={handleAspirantSelect}
        >
          <Text className="text-base font-medium text-gray-800">Aspirant</Text>
        </Pressable>
      )}
      {multipleSelection && selectedOptions.length !== 0 && (
        <View className="px-5 py-4 flex-row flex-wrap gap-2 border-b border-gray-100">
          {selectedOptions?.map((option, index) => (
            <View
              key={option?.id ? `selected-${option.id}` : `selected-index-${index}`}
              className="bg-borderGrayExtraLight rounded-xl px-3 py-2 flex-row items-center"
            >
              <Text className="text-black  text-md">{option?.name}</Text>
              <Pressable className="pl-2" onPress={() => deSelect(option?.id)}>
                <Close width={2} height={2} />
              </Pressable>
            </View>
          ))}
        </View>
      )}
    </View>
  );
};

const EntitySearchForm = ({
  title,
  placeholder,
  selectionKey,
  onBack,
  multipleSelection,
  searchWithoutInput,
  showDoneButton = false,
}: SearchScreenProps) => {
  const navigation = useNavigation<BottomTabNavigationI>();
  const dispatch = useDispatch<AppDispatch>();

  const {
    text,
    isOpen,
    isVisible,
    loading,
    options,
    handleBack: defaultHandleBack,
    handleAddOption,
    handleTextChange,
    handleSetOption,
    toggleVisible,
    loadMoreOptions,
    setText,
    setIsOpen,
    setCollectionType,
    setEntityWebsite,
    collection,
    selectionMap,
    handleDeSelectOption,
    hasFuelType,
    setHasFuelType,
  } = useSearchScreen(selectionKey, multipleSelection, searchWithoutInput);

  const handleBack = onBack || defaultHandleBack;

  const handleClearText = () => {
    setIsOpen(false);
    setText(null);
  };

  const renderFooter = () => {
    if (!loading) return null;

    return (
      <View className="py-4 items-center">
        <ActivityIndicator size="small" color="#448600" />
        <Text className="text-gray-500 mt-2">Loading more results...</Text>
      </View>
    );
  };

  const deduplicatedOptions = Array.from(
    new Map(options?.map((item) => [item?.name, item])).values(),
  );

  const deduplicatedSelectedOptions = Array.from(
    new Map(Array.from(selectionMap.values())?.map((item) => [item?.name, item])).values(),
  );

  return (
    <View className="flex-1">
      <View className="bg-white px-4 pb-4 border-b border-gray-100">
        <View className="flex-row items-center justify-between mb-4">
          <View className="flex-row items-center">
            <BackButton onBack={handleBack} label="" />
            <Text className="text-xl font-semibold text-gray-800 ml-2">{title}</Text>
          </View>
          {showDoneButton && (
            <Pressable onPress={handleBack}>
              <Text className="text-primaryGreen text-lg font-medium">Done</Text>
            </Pressable>
          )}
        </View>

        <View className="border border-gray-200 h-14 rounded-xl px-4 flex-row items-center justify-between bg-white">
          <TextInput
            className="flex-1 text-base text-gray-800"
            placeholder={placeholder}
            placeholderTextColor="#A3A3A3"
            value={text || ''}
            onChangeText={handleTextChange}
            editable={true}
            autoFocus
          />
          {text ? (
            <Pressable className="pl-1" onPress={handleClearText}>
              <Close width={2.5} height={2.5} />
            </Pressable>
          ) : (
            <Search width={2.5} height={2.5} />
          )}
        </View>
      </View>

      {(isOpen || text || searchWithoutInput) && (
        <View className="flex-1">
          <FlatList
            data={deduplicatedOptions}
            keyExtractor={(item, index) =>
              item?.id ? `option-${item.id}` : `option-index-${index}`
            }
            renderItem={({ item }) => (
              <Option item={item} select={() => handleSetOption(item, dispatch)} />
            )}
            ListHeaderComponent={
              collection !== 'country' ? (
                <HeaderComponent
                  handleAdd={toggleVisible}
                  text={text}
                  title={title}
                  selectionKey={collection}
                  dispatch={dispatch}
                  navigation={navigation}
                  multipleSelection={multipleSelection}
                  selectedOptions={deduplicatedSelectedOptions}
                  deSelect={handleDeSelectOption}
                />
              ) : undefined
            }
            onEndReached={loadMoreOptions}
            onEndReachedThreshold={0.8}
            contentContainerStyle={{
              flexGrow: 1,
              backgroundColor: 'white',
              paddingVertical: 2,
            }}
            ListFooterComponent={renderFooter}
          />
        </View>
      )}

      <CustomModal
        isVisible={isVisible}
        title={`Are you sure you want to add new ${
          title?.startsWith('Designation') ? 'designation' : title?.toLowerCase()
        } "${text || ''}"?`}
        cancelText="Cancel"
        confirmText="Add"
        onCancel={toggleVisible}
        onConfirm={() => handleAddOption(dispatch)}
        bodyComponent={
          TYPE_COLLECTIONS.has(collection) && (
            <AddFields
              collection={collection}
              onSelect={setCollectionType}
              onChange={setEntityWebsite}
              onToggle={() => setHasFuelType(!hasFuelType)}
              hasFuelType={hasFuelType}
            />
          )
        }
      />
    </View>
  );
};

export default EntitySearchForm;
