import { Pressable, Text, View } from 'react-native';
import UserAvatar from '@/src/components/UserAvatar';
import { formatMemberCount } from '@/src/screens/Community/components/CommunityHomeHead/utils';
import { CommunityItemPropsI } from './types';

const CommunityItem: React.FC<CommunityItemPropsI> = ({ community, onPress, renderActions }) => {
  const memberCount = formatMemberCount(community.memberCount);

  return (
    <Pressable onPress={onPress} className="px-4 py-4 border-b border-gray-200">
      <View className="flex-row items-start gap-3">
        <UserAvatar avatarUri={community.avatar} name={community.name} width={44} height={45} />
        <View className="flex-1">
          <Text
            ellipsizeMode="tail"
            numberOfLines={2}
            className="text-base font-semibold text-gray-900 mb-1"
          >
            {community.name}
          </Text>
          <Text className="text-sm text-gray-600 mb-2">{memberCount}</Text>
        </View>
        {renderActions && renderActions()}
      </View>
      {community.description ? (
        <Text
          ellipsizeMode="tail"
          numberOfLines={1}
          className="text-sm text-gray-500 leading-5 mt-1"
        >
          {community.description}
        </Text>
      ) : (
        ''
      )}
    </Pressable>
  );
};

export default CommunityItem;
