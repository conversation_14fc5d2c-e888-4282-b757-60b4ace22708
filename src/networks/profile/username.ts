/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { apiCall } from '@/src/services/api';
import { CheckUsernameQueryI, UpdateUsernameBodyI, UpdateUsernameResultI } from './types';

export const checkUsernameAPI = async (query: CheckUsernameQueryI): Promise<unknown> => {
  const result = await apiCall<unknown, unknown>('/backend/api/v1/user/profile/username', 'GET', {
    isAuth: true,
    query,
  });

  return result;
};

export const updateUsernameAPI = async (
  payload: UpdateUsernameBodyI,
): Promise<UpdateUsernameResultI> => {
  const result = await apiCall<UpdateUsernameBodyI, UpdateUsernameResultI>(
    '/backend/api/v1/user/profile/username',
    'PATCH',
    { isAuth: true, payload },
  );

  return result;
};
