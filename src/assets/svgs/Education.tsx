/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Circle, Path } from 'react-native-svg';
import { OutlinedIconPropsI } from './types';

const Education: React.FC<OutlinedIconPropsI> = ({
  width = 5.456,
  height = 5.58,
  fill = '#448600',
  color,
  disabled,
  accessibilityLabel = 'Education',
  ...props
}) => {
  const mainFill = color || fill;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 44 45"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Circle cx="22" cy="22.4707" r="21.5" fill="#F2F9E5" stroke="#DDEFC8" />
      <Path
        d="M31.2372 15.7592L22.2372 12.7592C22.0832 12.7079 21.9168 12.7079 21.7628 12.7592L12.7628 15.7592C12.6135 15.809 12.4836 15.9045 12.3915 16.0322C12.2995 16.1599 12.25 16.3133 12.25 16.4708V23.9708C12.25 24.1697 12.329 24.3604 12.4697 24.5011C12.6103 24.6417 12.8011 24.7208 13 24.7208C13.1989 24.7208 13.3897 24.6417 13.5303 24.5011C13.671 24.3604 13.75 24.1697 13.75 23.9708V17.5114L16.8991 18.5604C16.0624 19.9121 15.7963 21.5406 16.1593 23.0883C16.5223 24.636 17.4846 25.9764 18.835 26.8151C17.1475 27.477 15.6888 28.6742 14.6219 30.3111C14.5664 30.3935 14.5278 30.4862 14.5085 30.5837C14.4891 30.6812 14.4894 30.7816 14.5091 30.879C14.5289 30.9764 14.5678 31.0689 14.6237 31.1511C14.6795 31.2333 14.7512 31.3036 14.8344 31.3579C14.9177 31.4122 15.0109 31.4494 15.1086 31.4674C15.2064 31.4854 15.3067 31.4837 15.4039 31.4625C15.501 31.4414 15.5929 31.4011 15.6743 31.3441C15.7557 31.2871 15.825 31.2145 15.8781 31.1304C17.2909 28.9629 19.5222 27.7208 22 27.7208C24.4778 27.7208 26.7091 28.9629 28.1219 31.1304C28.2319 31.2939 28.4018 31.4076 28.5949 31.4468C28.788 31.486 28.9888 31.4476 29.1539 31.34C29.3189 31.2323 29.435 31.064 29.4769 30.8715C29.5189 30.679 29.4834 30.4776 29.3781 30.3111C28.3112 28.6742 26.8469 27.477 25.165 26.8151C26.5141 25.9764 27.4755 24.6369 27.8384 23.0903C28.2013 21.5438 27.9361 19.9164 27.1009 18.5651L31.2372 17.187C31.3866 17.1373 31.5165 17.0418 31.6086 16.914C31.7006 16.7863 31.7502 16.6329 31.7502 16.4754C31.7502 16.318 31.7006 16.1646 31.6086 16.0368C31.5165 15.9091 31.3866 15.8136 31.2372 15.7639V15.7592ZM26.5 21.7208C26.5002 22.4322 26.3317 23.1335 26.0084 23.7672C25.6851 24.4009 25.2161 24.949 24.6399 25.3663C24.0638 25.7837 23.3969 26.0585 22.694 26.1682C21.9911 26.2779 21.2722 26.2194 20.5962 25.9975C19.9203 25.7756 19.3066 25.3965 18.8056 24.8915C18.3045 24.3865 17.9303 23.7698 17.7137 23.0922C17.4971 22.4145 17.4442 21.6952 17.5594 20.9932C17.6747 20.2911 17.9547 19.6264 18.3766 19.0536L21.7628 20.1786C21.9168 20.2299 22.0832 20.2299 22.2372 20.1786L25.6234 19.0536C26.1932 19.8261 26.5005 20.7609 26.5 21.7208ZM22 18.6804L15.3719 16.4708L22 14.2611L28.6281 16.4708L22 18.6804Z"
        fill={mainFill}
      />
    </Svg>
  );
};

export default Education;
