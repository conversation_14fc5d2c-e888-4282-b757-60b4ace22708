import { Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import ChatsList from './components/ChatsList';

const ChatsScreen = () => {
  const navigation = useNavigation();
  const onBack = () => {
    navigation.goBack();
  };
  return (
    <SafeArea>
      <View className="flex-1">
        <View className="flex-row items-center gap-1">
          <BackButton onBack={onBack} label="" />
          <Text className="text-xl font-medium">Chats</Text>
        </View>
        <ChatsList />
      </View>
    </SafeArea>
  );
};

export default ChatsScreen;
