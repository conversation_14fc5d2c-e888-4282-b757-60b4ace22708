import { createAsyncThunk, createSlice, type PayloadAction } from '@reduxjs/toolkit';
import type { SearchResultI, SearchState } from './types';

const initialState: SearchState = {
  selections: {},
  multipleSelections: {},
};

export const clearSelectionAsync = createAsyncThunk(
  'search/clearSelectionAsync',
  async (key: string, { dispatch }) => {
    dispatch(clearSelection(key));
  },
);

const searchSlice = createSlice({
  name: 'search',
  initialState,
  reducers: {
    setSelection: (state, action: PayloadAction<{ key: string; value: SearchResultI }>) => {
      const { key, value } = action.payload;
      state.selections[key] = value;
    },
    setMultipleSelections: (
      state,
      action: PayloadAction<{ key: string; value: SearchResultI[] }>,
    ) => {
      const { key, value } = action.payload;
      state.multipleSelections[key] = value;
    },
    clearSelection: (state, action: PayloadAction<string>) => {
      const key = action.payload;
      delete state.selections[key];
      if (key === 'skill') {
        delete state.multipleSelections[key];
      }
    },
    clearMultipleSelections: (state, action: PayloadAction<string>) => {
      const key = action.payload;
      delete state.multipleSelections[key];
      if (key === 'skill') {
        delete state.selections[key];
      }
    },
    addToMultipleSelection: (
      state,
      action: PayloadAction<{ key: string; value: SearchResultI }>,
    ) => {
      const { key, value } = action.payload;
      if (!state.multipleSelections[key]) {
        state.multipleSelections[key] = [];
      }
      state.multipleSelections[key].push(value);
    },
    removeFromMultipleSelection: (state, action: PayloadAction<{ key: string; id: string }>) => {
      const { key, id } = action.payload;
      if (state.multipleSelections[key]) {
        state.multipleSelections[key] = state.multipleSelections[key].filter(
          (item) => item.id !== id,
        );
      }
    },
    clearAllSelections: (state) => {
      state.selections = {};
      state.multipleSelections = {};
    },
    clearSkillSelections: (state) => {
      delete state.selections['skill'];
      delete state.multipleSelections['skill'];
      Object.keys(state.selections).forEach((key) => {
        if (key.startsWith('skill')) {
          delete state.selections[key];
        }
      });
      Object.keys(state.multipleSelections).forEach((key) => {
        if (key.startsWith('skill')) {
          delete state.multipleSelections[key];
        }
      });
    },
  },
});

export const {
  setSelection,
  clearSelection,
  setMultipleSelections,
  clearMultipleSelections,
  addToMultipleSelection,
  removeFromMultipleSelection,
  clearAllSelections,
  clearSkillSelections,
} = searchSlice.actions;

export default searchSlice.reducer;
