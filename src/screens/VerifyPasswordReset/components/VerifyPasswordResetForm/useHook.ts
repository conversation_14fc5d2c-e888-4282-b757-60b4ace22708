import { useState } from 'react';
import { useDispatch } from 'react-redux';
import {
  sendOTPForPasswordResetAsync,
  verifyOTPForPasswordResetAsync,
} from '@/src/redux/slices/user/userSlice';
import { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import APIResError from '@/src/errors/networks/APIResError';

export function useVerifyPasswordReset(email: string, onSuccess?: () => void) {
  const dispatch = useDispatch<AppDispatch>();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [otp, setOtp] = useState('');

  const handleVerify = async () => {
    if (otp.length !== 6) {
      return;
    }
    try {
      setIsSubmitting(true);
      const resultAction = await dispatch(
        verifyOTPForPasswordResetAsync({
          email,
          otp,
        }),
      );

      if (verifyOTPForPasswordResetAsync.fulfilled.match(resultAction)) {
        showToast({
          type: 'success',
          message: 'Verification Successful',
          description: 'Your code has been verified successfully',
        });
        onSuccess?.();
      } else {
        throw new Error('Invalid verification code');
      }
    } catch (error: unknown) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            type: 'error',
            message: 'Verification Failed',
            description: error instanceof APIResError ? error.message : 'Invalid verification code',
          });
        },
        handle5xxError: () => {
          showToast({
            type: 'error',
            message: 'Verification Failed',
            description: error instanceof APIResError ? error.message : 'Something went wrong',
          });
        },
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleResend = async () => {
    try {
      const resultAction = await dispatch(sendOTPForPasswordResetAsync({ email }));

      if (sendOTPForPasswordResetAsync.fulfilled.match(resultAction)) {
        showToast({
          type: 'success',
          message: 'Code Resent',
          description: 'A new verification code has been sent to your email',
        });
        return Promise.resolve();
        return Promise.resolve();
      } else {
        throw new Error('Failed to resend code');
      }
    } catch (error: unknown) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            type: 'error',
            message: 'Failed to Send Code',
            description: error instanceof APIResError ? error.message : 'Please try again later',
          });
        },
        handle5xxError: () => {
          showToast({
            type: 'error',
            message: 'Failed to Send Code',
            description: error instanceof APIResError ? error.message : 'Something went wrong',
          });
        },
      });
      return Promise.reject(error);
      return Promise.reject(error);
    }
  };

  return {
    isSubmitting,
    otp,
    setOtp,
    handleVerify,
    handleResend,
  };
}
