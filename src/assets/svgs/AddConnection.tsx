/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Circle, Path } from 'react-native-svg';
import { OutlinedIconPropsI } from './types';

const AddConnection: React.FC<OutlinedIconPropsI> = ({
  width = 2.48,
  height = 2.48,
  color = '#404040',
  stroke,
  strokeWidth = 1.5,
  accessibilityLabel = 'Add Connection',
  disabled,
  ...props
}) => {
  const strokeColor = stroke || color;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 20 20"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Circle cx={8.5} cy={7} r={2.75} stroke={strokeColor} strokeWidth={strokeWidth} />
      <Path
        d="M3 16.5v-2c0-1.5 1-2 1.5-2H12c.5 0 2 0 2 2v2"
        stroke={strokeColor}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
      />
      <Path
        d="M15 7.5v4.19m-2-2.095h4"
        stroke={strokeColor}
        strokeWidth={1.2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default AddConnection;
