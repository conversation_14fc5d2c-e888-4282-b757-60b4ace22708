import { useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { useDispatch, useSelector } from 'react-redux';
import {
  fetchPost,
  addReactionOptimistc,
  removeReactionOptimistic,
  addScrapbookReactionOptimistic,
  removeScrapbookReactionOptimistic,
  deletePostOptimistic,
  deleteScrapbookPostOptimistic,
  revertDeleteScrapbookPostOptimistic,
  revertDeletePostOptimistic,
} from '@/src/redux/slices/content/contentSlice';
import { AppDispatch, RootState } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import { BottomTabNavigationI } from '@/src/navigation/types';
import { deletePostAPI } from '@/src/networks/content/post';
import { upsertReactionAPI, deleteReactionAPI } from '@/src/networks/content/reaction';
import { PostExternalClientI } from '@/src/networks/content/types';
import {
  upsertScrapbookReaction,
  deleteScrapbookReaction,
  deleteScrapbookPost,
} from '@/src/networks/port/scrapbook';
import { ScrapBookPostFetchForClientI } from '@/src/networks/port/types';

const useCommentsHead = (postId: string, type?: string, onPostLoaded?: () => void) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const dispatch = useDispatch<AppDispatch>();
  const navigation = useNavigation<BottomTabNavigationI>();

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const post = useSelector((state: RootState) => {
    if (state.content.post?.id === postId) {
      return state.content.post;
    }

    const homePost = state.content.posts.find((p) => p.id === postId);
    if (homePost) return homePost;

    const ownPost = state.content.ownPosts.find((p) => p.id === postId);
    if (ownPost) return ownPost;

    for (const profileId in state.content.profilePosts) {
      const profilePost = state.content.profilePosts[profileId]?.posts.find((p) => p.id === postId);
      if (profilePost) return profilePost;
    }

    return null;
  });

  const fetchPostData = async () => {
    if (!postId) return;

    try {
      setLoading(true);

      await dispatch(fetchPost({ id: postId, type })).unwrap();

      if (onPostLoaded) {
        onPostLoaded();
      }
    } catch (_error) {
      if (!post) {
        triggerErrorBoundary(
          new Error(
            `Failed to load post data: ${_error instanceof Error ? _error.message : 'Unknown error'}`,
          ),
        );
      } else {
        showToast({
          type: 'error',
          message: 'Error',
          description: 'Failed to load post data',
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleLikePost = async (postData: PostExternalClientI | ScrapBookPostFetchForClientI) => {
    if (type === 'SCRAPBOOK_POST') {
      const scrapbookPost = postData as ScrapBookPostFetchForClientI;
      const isLiked = scrapbookPost.isLiked;

      if (isLiked) {
        dispatch(removeScrapbookReactionOptimistic({ scrapbookPostId: postId }));
      } else {
        dispatch(addScrapbookReactionOptimistic({ scrapbookPostId: postId }));
      }

      try {
        if (isLiked) {
          await deleteScrapbookReaction({
            scrapBookPostId: postId,
          });
        } else {
          await upsertScrapbookReaction({
            scrapBookPostId: postId,
            reactionType: 'LIKE',
          });
        }
      } catch (error) {
        if (isLiked) {
          dispatch(addScrapbookReactionOptimistic({ scrapbookPostId: postId }));
        } else {
          dispatch(removeScrapbookReactionOptimistic({ scrapbookPostId: postId }));
        }
        showToast({
          type: 'error',
          message: 'Error',
          description: 'Failed to update scrapbook reaction',
        });
      }
    } else {
      const regularPost = postData as PostExternalClientI;
      const isLiked = regularPost.isLiked;

      if (isLiked) {
        dispatch(removeReactionOptimistic({ postId }));
      } else {
        dispatch(addReactionOptimistc({ postId }));
      }

      try {
        if (isLiked) {
          await deleteReactionAPI({
            postId,
          });
        } else {
          await upsertReactionAPI({
            postId,
            reactionType: 'LIKE',
          });
        }
      } catch (error) {
        if (isLiked) {
          dispatch(addReactionOptimistc({ postId }));
        } else {
          dispatch(removeReactionOptimistic({ postId }));
        }
        showToast({
          type: 'error',
          message: 'Error',
          description: 'Failed to update post reaction',
        });
      }
    }
  };

  const handleDeletePost = async (postData: PostExternalClientI | ScrapBookPostFetchForClientI) => {
    navigation.goBack();
    if (type === 'SCRAPBOOK_POST') {
      dispatch(deleteScrapbookPostOptimistic({ scrapbookPostId: postId }));

      try {
        await deleteScrapbookPost({
          id: postId,
        });
        showToast({
          type: 'success',
          message: 'Post deleted successfully',
        });
      } catch (error) {
        const scrapbookPost = postData as ScrapBookPostFetchForClientI;
        dispatch(
          revertDeleteScrapbookPostOptimistic({
            postId: scrapbookPost.id,
          }),
        );
        showToast({
          type: 'error',
          message: 'Error',
          description: 'Failed to delete scrapbook post',
        });
      }
    } else {
      const regularPost = postData as PostExternalClientI;
      dispatch(deletePostOptimistic({ post: regularPost }));

      try {
        await deletePostAPI(postId);
      } catch (error) {
        dispatch(
          revertDeletePostOptimistic({
            postId,
          }),
        );
        showToast({
          type: 'error',
          message: 'Error',
          description: 'Failed to delete post',
        });
      }
    }
  };

  useEffect(() => {
    if (!post) {
      fetchPostData();
    } else {
      setLoading(false);
      if (onPostLoaded) {
        onPostLoaded();
      }
    }
  }, [postId, type]);

  return {
    loading: loading && !post,
    post,
    refetch: fetchPostData,
    handleLikePost,
    handleDeletePost,
  };
};

export default useCommentsHead;
