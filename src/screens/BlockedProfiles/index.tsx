import React from 'react';
import { useNavigation } from '@react-navigation/native';
import SafeArea from '@/src/components/SafeArea';
import { BottomTabNavigationI } from '@/src/navigation/types';
import BlockedUserProfilesList from './components/BlockedProfileList';

const BlockedProfiles = () => {
  const navigation = useNavigation<BottomTabNavigationI>();

  return (
    <SafeArea>
      <BlockedUserProfilesList onBack={() => navigation.goBack()} />
    </SafeArea>
  );
};

export default BlockedProfiles;
