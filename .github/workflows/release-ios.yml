name: 📦 iOS Build & Release

on:
  workflow_dispatch:
    inputs:
      version:
        description: '📝 App version (semantic version)'
        required: false
        default: ''
      build-number:
        description: '🔢 Build number'
        required: false
        default: ''
      track:
        description: '🚚 Release track (testflight, appstore)'
        required: false
        default: ''
  push:
    branches:
      - ios-prod-release
      - ios-alpha-release

concurrency:
  group: ios-build-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: write

jobs:
  prepare:
    name: 🛠️ Prepare Environment
    runs-on: macos-15
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
      version: ${{ steps.setup.outputs.version }}
      build-number: ${{ steps.setup.outputs.build-number }}

    steps:
      - name: 🔄 Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: ⚙️ Setup environment
        id: setup
        uses: ./.github/actions/setup-environment
        with:
          platform: 'ios'

      - name: 🧩 Set up build matrix
        id: set-matrix
        run: |
          cat << EOF > matrix.json
          {
            "include": [
              { "branch": "ios-prod-release", "track": "appstore", "configuration": "Release" },
              { "branch": "ios-alpha-release", "track": "testflight", "configuration": "Release" }
            ]
          }
          EOF

          FILTERED_INCLUDE=$(jq -c --arg branch "${{ github.ref_name }}" '.include | map(select(.branch == $branch))' matrix.json)
          MATRIX=$(jq -c --argjson include "$FILTERED_INCLUDE" '{"include": $include}' <<< '{}')

          echo "matrix=$MATRIX" >> "$GITHUB_OUTPUT"

  build:
    name: 🏗️ Build & Release
    runs-on: macos-15
    needs: prepare
    strategy:
      matrix: ${{ fromJSON(needs.prepare.outputs.matrix) }}
      fail-fast: false
    steps:
      - name: 🔄 Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🧭 Determine release track
        id: determine-track
        run: |
          TRACK="${{ github.event.inputs.track || matrix.track }}"
          echo "release_track=${TRACK}" >> "$GITHUB_OUTPUT"
          echo "📌 Using release track: ${TRACK}"

      - name: 🏗️ Build iOS app
        uses: ./.github/actions/build-ios
        with:
          version: ${{ github.event.inputs.version || needs.prepare.outputs.version }}
          build-number: ${{ github.event.inputs.build-number || needs.prepare.outputs.build-number }}
          configuration: ${{ matrix.configuration }}
          p12-certificate-base64: ${{ secrets.APPSTORE_CERTIFICATES_FILE_BASE64 }}
          p12-password: ${{ secrets.APPSTORE_CERTIFICATES_PASSWORD }}
          provisioning-profile-base64: ${{ secrets.APPSTORE_PROVISIONING_PROFILE_BASE64 }}
          team-id: ${{ secrets.APPSTORE_TEAM_ID }}
          provisioning-profile-name: 'navicater-profile'
        env:
          API_KEY: ${{ secrets.API_KEY }}
          BASE_URL: ${{ github.ref_name == 'android-prod-release' && secrets.BASE_URL || secrets.BASE_URL_DEV }}
          AI_URL: ${{ secrets.AI_URL }}
          FIREBASE_ANDROID_APP_ID: ${{ secrets.FIREBASE_ANDROID_APP_ID }}
          FIREBASE_API_KEY: ${{ secrets.FIREBASE_API_KEY }}
          FIREBASE_AUTH_DOMAIN: ${{ secrets.FIREBASE_AUTH_DOMAIN }}
          FIREBASE_IOS_APP_ID: ${{ secrets.FIREBASE_IOS_APP_ID }}
          FIREBASE_MESSAGING_SENDER_ID: ${{ secrets.FIREBASE_MESSAGING_SENDER_ID }}
          FIREBASE_PROJECT_ID: ${{ secrets.FIREBASE_PROJECT_ID }}
          FIREBASE_STORAGE_BUCKET: ${{ secrets.FIREBASE_STORAGE_BUCKET }}
          IOS_CLIENT_ID: ${{ secrets.IOS_CLIENT_ID }}
          SENTRY_DSN_URL: ${{ secrets.SENTRY_DSN_URL }}
          WEB_CLIENT_ID: ${{ secrets.WEB_CLIENT_ID }}
          ENV: ${{ secrets.ENV }}

      - name: ℹ️ Display build info
        run: |
          echo "✅ Build completed for:"
          echo "📦 Version: ${{ github.event.inputs.version || needs.prepare.outputs.version }}"
          echo "🔢 Build Number: ${{ github.event.inputs.build-number || needs.prepare.outputs.build-number }}"
          echo "🚚 Track: ${{ steps.determine-track.outputs.release_track }}"
          ls -la ios/

      - name: ☁️ Upload to App Store
        if: matrix.configuration == 'Release'
        uses: ./.github/actions/upload-app-store
        with:
          app-store-connect-api-key-id: ${{ secrets.APPSTORE_API_KEY_ID }}
          app-store-connect-api-issuer-id: ${{ secrets.APPSTORE_API_ISSUER_ID }}
          app-store-connect-api-key-content: ${{ secrets.APPSTORE_API_KEY_CONTENT }}
          bundle-id: 'com.navicater'
          ipa-path: 'ios/navicater.ipa'
          track: ${{ steps.determine-track.outputs.release_track }}

      - name: 🏷️ Create GitHub Release
        uses: ncipollo/release-action@v1
        with:
          tag: ios-v${{ github.event.inputs.version || needs.prepare.outputs.version }}-${{ github.event.inputs.build-number || needs.prepare.outputs.build-number }}-${{ steps.determine-track.outputs.release_track }}
          name: 🛠️ iOS Build v${{ github.event.inputs.version || needs.prepare.outputs.version }} (${{ steps.determine-track.outputs.release_track }})
          artifacts: ios/navicater.ipa
          token: ${{ secrets.PACKAGE_PUBLISH_GITHUB_TOKEN }}
          generateReleaseNotes: true
