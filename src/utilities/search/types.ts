/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ObjUnknownI } from '@/src/types/common/data';

export type GlobalSearchCategory =
  | 'people'
  | 'institution'
  | 'post'
  | 'organization'
  | 'port'
  | 'ship';
export interface GlobalSearchResponse<T = unknown> {
  data: T[];
  total: number;
}
export interface GlobalSearchParams extends ObjUnknownI {
  search: string;
  page: number;
  pageSize?: number;
}
