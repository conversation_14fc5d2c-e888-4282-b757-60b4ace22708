/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { apiCall } from '@/src/services/api';
import { AuthLoginBodyI, AuthLoginResultI } from './types';

export const loginAPI = async (payload: AuthLoginBodyI): Promise<AuthLoginResultI> => {
  const result = await apiCall<AuthLoginBodyI, AuthLoginResultI>(
    '/backend/api/v1/auth/login',
    'POST',
    {
      isAuth: false,
      payload,
    },
  );

  return result;
};
