/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import SafeArea from '@/src/components/SafeArea';
import { AppStackParamListI, AuthStackParamListI } from '@/src/navigation/types';
import ResetPasswordForm from './components/ResetPasswordForm';

type ResetPasswordRouteProp = RouteProp<AppStackParamListI, 'ResetPassword'>;

const ResetPasswordScreen = () => {
  const navigation = useNavigation<StackNavigationProp<AuthStackParamListI>>();
  const route = useRoute<ResetPasswordRouteProp>();
  const { email } = route.params;

  const handleSuccess = () => {
    navigation.navigate('UserLogin');
  };

  return (
    <SafeArea>
      <ResetPasswordForm email={email} onSuccess={handleSuccess} />
    </SafeArea>
  );
};

export default ResetPasswordScreen;
