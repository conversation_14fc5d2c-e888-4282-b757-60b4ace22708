import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { IdNameI, IdTypeI } from '@/src/types/common/data';

export interface AddDocumentBodyI {
  documentNo: string;
  fileUrl?: string;
  type: 'PASSPORT' | 'CDC' | 'SID';
  countryIso2: string;
  fromDate: string;
  untilDate: string | null;
  name?: string;
}

export interface AddDocumentResultI {
  id: string;
  fileUrl: string;
  expiryStatus: 'expired' | 'expires_soon' | 'valid';
}

export interface AddCertificationBodyI {
  institute: IdTypeI;
  certificateCourse: IdTypeI;
  fileUrl?: string;
  fromDate: string;
  untilDate: string | null;
  skills: IdTypeI[];
}

export interface AddCertificationResultI {
  id: string;
  fileUrl: string | null;
}

export interface CertificationI {
  id: string;
  entity: IdNameI;
  certificateCourse: IdNameI;
  fromDate: string;
  untilDate: string;
}

export interface FetchCertificationListResultI {
  statutoryCertifications: CertificationI[];
  valueAddedCertifications: CertificationI[];
}

export interface FetchCertificationListBodyI {
  type: 'VALUE_ADDED' | 'STATUTORY';
  pageSize: number;
}

export interface FetchCertificationResultI {
  certificateCourse: SearchResultI & { type: string };
  entity: SearchResultI & { type: string };
  fileUrl: string | null;
  fromDate: string;
  id: string;
  skills: SearchResultI & { category: string };
  untilDate: string;
}

export interface fetchDocumentsResultI {
  country: { name: string };
  expiryStatus: string;
  fromDate: string;
  untilDate: string;
  id: string;
}

export interface IdentityDocumentationI extends fetchDocumentsResultI {
  type: string;
}

export interface VisaDocumentationI extends fetchDocumentsResultI {
  fileUrl: string;
  name: string;
}

export interface fetchDocumentationListResultI {
  identityDocuments: IdentityDocumentationI[];
  visaDocuments: VisaDocumentationI[];
}

export interface FetchDocumentResultI {
  country: {
    iso2: string;
    name: string;
  };
  documentNo: string;
  expiryStatus: string;
  fileUrl: string;
  fromDate: string;
  id: string;
  type?: string;
  untilDate: string;
  name?: string;
}

export interface AddEducationResultI {
  institute: {
    id: string;
    dataType: 'raw' | 'master';
  };
  degree: {
    id: string;
    dataType: 'raw' | 'master';
  };
  fromDate: string;
  toDate: string | null;
  skills: IdTypeI[];
}

export interface fetchedEducationTypeI {
  createdAt: string;
  degree: SearchResultI;
  entity: SearchResultI;
  fromDate: string;
  id: string;
  toDate: string;
}
export interface fetchEducation extends fetchedEducationTypeI {
  profileId: string;
  skills: SearchResultI[];
}

export interface fetchSkillsResultI {
  id: string;
  name: string;
  dataType: string;
  category: string;
}

export type EditEducationBodyI = {
  institute: {
    id: string;
    dataType: 'raw' | 'master';
  };
  degree: {
    id: string;
    dataType: 'raw' | 'master';
  };
  fromDate: string;
  toDate: string | null;
  skillsToAdd: IdTypeI[];
  skillsToDelete: IdTypeI[];
};

export type EditCertificationBodyI = {
  institute: IdTypeI;
  certificateCourse: IdTypeI;
  fromDate: string;
  untilDate: string | null;
  skillsToAdd: IdTypeI[];
  skillsToDelete: IdTypeI[];
  file?: {
    opr: 'CREATE' | 'UPDATE' | 'DELETE';
    fileUrl?: string;
  };
};

export type EditDocumentBodyI = {
  documentNo: string;
  countryIso2: string;
  fromDate: string;
  untilDate: string | null;
  file?: {
    opr: 'CREATE' | 'UPDATE' | 'DELETE';
    fileUrl?: string;
  };
};

export type SkillsPayloadI = {
  id: string;
  dataType: string;
  category: string;
};

export type DeleteSkillPayloadI = {
  id: string;
  dataType: string;
};
