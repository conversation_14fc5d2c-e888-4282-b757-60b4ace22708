import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { ObjUnknownI } from '@/src/types/common/data';

export type RequestStatusTypeE = 'PENDING' | 'ACCEPTED' | 'REJECTED' | 'REVOKED' | 'DISCONNECTED';

export interface FetchShipProfileQueryI extends ObjUnknownI {
  imo: string;
  dataType: string;
}

export interface NamedEntityI {
  name: string;
}

export interface ShipNameHistoryI {
  name: string;
  fromDate: string | null;
  toDate: string | null;
}

export interface FetchShipProfileResultI {
  imo: string;
  mmsi: number;
  callSign: string;
  name: string;
  imageUrl: string;
  yearBuilt: number;
  country: NamedEntityI;
  mainVesselType: NamedEntityI;
  subVesselTypes: NamedEntityI[];
  dataType: string;
  shipNames: ShipNameHistoryI[];
}

export type FetchShipPeopleQueryI = {
  imo: string;
  dataType: string;
  page: number;
  pageSize: number;
};

export type FetchPeopleResult = {
  avatar: string;
  designation: SearchResultI;
  entity: SearchResultI;
  id: string;
  isConnected: boolean;
  requestStatus: RequestStatusTypeE;
  name: string;
};

export interface CreateShipPayloadI {
  imo: string;
  name: string;
  mmsi?: string;
  callSign?: string;
  flagCountryIso2?: string;
  mainVesselType?: {
    id: string;
    dataType: 'raw' | 'master';
  };
  subVesselType?: {
    id: string;
    dataType: 'raw' | 'master';
  };
  status: 'ACTIVE' | 'INACTIVE';
  portOfRegistry?: string;
  yearBuilt?: number;
}

export interface CreateShipResultI {
  imo: string;
  name: string;
  mmsi?: string;
  callSign?: string;
  dataType: 'raw' | 'master';
  id: string;
}
