/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { Text, View } from 'react-native';
import Accordion from '@/src/components/Accordion';
import { ExperienceFetchForClientResultI } from '@/src/redux/slices/experience/types';
import { formatDate, getYearsMonths } from '@/src/utilities/datetime';
import ShipDetails from '../ShipDetails';

const ExperienceContent = ({ data }: { data: ExperienceFetchForClientResultI }) => {
  return data.designations?.length ? (
    <>
      <View>
        {data?.designations?.map((item, index) => {
          if (!data?.designations) return;
          const isLast = index === data?.designations.length - 1;
          return (
            <View key={index} className="mt-3">
              <View className="flex-row items-center">
                <View className="flex-row">
                  <View className="flex-col items-center">
                    <View className="h-2 w-2 rounded-full bg-labelGray mb-1" />
                    <View className={`flex-1 w-px ${isLast ? '' : 'bg-borderGray'}`} />
                  </View>

                  <View className="ml-3 flex-1 gap-1.5">
                    <Text className="font-semibold text-sm leading-4">
                      {item?.designation?.name}
                    </Text>
                    <Text className="text-subLabelGrayDark text-sm leading-4">
                      {item.fromDate ? formatDate(item.fromDate, 'MMMM, YYYY') : ''} -{' '}
                      {item.toDate ? formatDate(item.toDate, 'MMMM, YYYY') : 'Present'}
                    </Text>
                    {item?.ships?.map((ship, shipIndex) => (
                      <ShipDetails key={shipIndex} ship={ship} />
                    ))}
                  </View>
                </View>
              </View>
            </View>
          );
        })}
      </View>
    </>
  ) : (
    <></>
  );
};

const CompanyExperience = ({
  data,
  isLast,
  isFirst,
}: {
  data: ExperienceFetchForClientResultI;
  isLast: boolean;
  isFirst: boolean;
}) => {
  return data ? (
    <Accordion
      title={data.entity.name}
      subTitle={getYearsMonths(data?.years as number, data?.months as number)}
      isLast={isLast}
      content={<ExperienceContent data={data} />}
      defaultExpanded={isFirst}
    />
  ) : (
    <></>
  );
};

export default CompanyExperience;
