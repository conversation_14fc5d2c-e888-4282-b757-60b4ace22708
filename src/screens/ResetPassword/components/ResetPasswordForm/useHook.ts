import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useDispatch } from 'react-redux';
import { resetPasswordAsync, clearForgotPasswordState } from '@/src/redux/slices/user/userSlice';
import { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import APIResError from '@/src/errors/networks/APIResError';
import { ResetPasswordFormData } from './types';

export function useResetPassword(email: string, onSuccess?: () => void) {
  const dispatch = useDispatch<AppDispatch>();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const methods = useForm<ResetPasswordFormData>({
    mode: 'onChange',
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
  });

  const handleSubmit = async (data: ResetPasswordFormData) => {
    if (data.password !== data.confirmPassword) {
      showToast({
        type: 'error',
        message: 'Error',
        description: 'Passwords do not match',
      });
      return;
    }

    try {
      setIsSubmitting(true);

      const resultAction = await dispatch(
        resetPasswordAsync({
          email,
          newPassword: data.password,
        }),
      );

      if (resetPasswordAsync.fulfilled.match(resultAction)) {
        dispatch(clearForgotPasswordState());
        showToast({
          type: 'success',
          message: 'Success',
          description: 'Your password has been reset successfully',
        });
        onSuccess?.();
      } else {
        throw new Error(resultAction.payload as string);
      }
    } catch (error: unknown) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            type: 'error',
            message: 'Error',
            description: error instanceof APIResError ? error.message : 'Something went wrong',
          });
        },
        handle5xxError: () => {
          showToast({
            type: 'error',
            message: 'Error',
            description: error instanceof APIResError ? error.message : 'Something went wrong',
          });
        },
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    methods,
    isSubmitting,
    handleSubmit,
  };
}
