import { useCallback, useState } from 'react';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { handleError } from '@/src/utilities/errors/errors';
import { navigate } from '@/src/utilities/navigation';
import { showToast } from '@/src/utilities/toast';
import { deleteDocumentAPI, fetchIdentityDocumentsAPI } from '@/src/networks/career/document';
import type { IdentityI } from '../EditDocumentationList/types';

export const useEditIdentityDocument = (profileId: string) => {
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [deleteDocumentId, setDeleteDocumentId] = useState('');
  const [isVisible, setIsVisible] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [documents, setDocuments] = useState<IdentityI[]>([]);
  const navigation = useNavigation();
  const pageSize = 10;

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const fetchDocuments = async (pageNumber: number, isLoadMore = false) => {
    try {
      if (isLoadMore) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }

      const result = await fetchIdentityDocumentsAPI(profileId, pageNumber, pageSize);

      if (isLoadMore) {
        setDocuments((prev) => [...prev, ...result]);
      } else {
        setDocuments(result);
      }

      setHasMore(result.length >= pageSize);
      setPage(pageNumber);
    } catch (error) {
      triggerErrorBoundary(
        new Error(
          'Failed to load identity documents: ' +
            (error instanceof Error ? error.message : 'Unknown error'),
        ),
      );
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchDocuments(0);
    }, [profileId]),
  );

  const handleLoadMore = () => {
    if (!loadingMore && !loading && hasMore) {
      fetchDocuments(page + 1, true);
    }
  };

  const onEditDocument = (documentId: string) => {
    navigate('EditDocumentItem', {
      profileId,
      documentId,
      type: 'identity',
    });
  };

  const onDeleteDocument = async () => {
    setIsDeleting(true);
    try {
      await deleteDocumentAPI(deleteDocumentId, 'identity');
      setDocuments((prev) => prev.filter((item) => item.id !== deleteDocumentId));
      showToast({
        message: 'Success',
        description: 'Document deleted successfully',
        type: 'success',
      });
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Error',
            description: 'Failed to Delete Document',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsDeleting(false);
    }
    navigation.goBack();
  };

  return {
    onEditDocument,
    onDeleteDocument,
    hasMore,
    isVisible,
    setIsVisible,
    setDeleteDocumentId,
    isDeleting,
    loading,
    loadingMore,
    handleLoadMore,
    documents,
  };
};
