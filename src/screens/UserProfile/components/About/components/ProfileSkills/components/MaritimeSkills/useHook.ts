import { useSelector } from 'react-redux';
import {
  selectAboutProfileMaritimeSkills,
  selectAboutProfileMaritimeSkillsCount,
} from '@/src/redux/selectors/about';

export const useMaritimeSkills = () => {
  const maritimeSkills = useSelector(selectAboutProfileMaritimeSkills);
  const count = useSelector(selectAboutProfileMaritimeSkillsCount);

  const getDeduplicatedSkills = () => {
    if (!maritimeSkills || !Array.isArray(maritimeSkills)) return [];
    const skillsSet = new Set();
    return maritimeSkills.filter((skill) => {
      const identifier = skill.id || skill.name || JSON.stringify(skill);
      if (skillsSet.has(identifier)) {
        return false;
      }
      skillsSet.add(identifier);
      return true;
    });
  };

  return {
    localSkills: getDeduplicatedSkills(),
    count,
  };
};
