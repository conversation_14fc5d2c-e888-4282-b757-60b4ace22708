import type { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import type { ShipDetailsI } from '@/src/screens/UserProfile/components/Experience/components/ShipDetails/types';

export type OprTypeE = 'CREATE' | 'UPDATE' | 'DELETE' | 'NESTED_OPR';

export type DesignationI = {
  id: string;
  name: string;
  dataType: 'raw' | 'master' | undefined;
};

export type ExperienceDesignationI = {
  dataType?: 'raw' | 'master' | undefined;
  experienceDesignationId?: string;
  fromDate?: string;
  toDate?: string;
  designation: DesignationI;
  id?: string;
  name: string;
  from?: string;
  to?: string;
  ships?: ShipDetailsI[];
};

export type ExperienceItemI = {
  id: string;
  entity: {
    dataType?: 'raw' | 'master' | undefined;
    id: string;
    name: string;
    logo?: string;
    website?: string;
    location?: string;
  };
  years: number;
  months: number;
  dataType?: 'raw' | 'master' | undefined;
  designations?: ExperienceDesignationI[];
};

type CountryI = {
  iso2: string;
  name: string;
};

export type PortI = {
  unLocode: string;
  name: string;
  country: CountryI;
};

export interface ExperienceFetchForClientResultI extends ExperienceItemI {}

export type ExperienceFetchReponseI = {
  data: ExperienceFetchForClientResultI[];
  total: number;
  portVisits: { data: PortI[]; total: number };
};

export interface ClientEquipmentI {
  tempId: string;
  shipTempId: string;
  category: {
    id: string;
    name: string;
    dataType: 'raw' | 'master';
    hasFuelType: boolean;
  };
  manufacturerName: string;
  model: string;
  power: string;
  additionalDetails?: string;
  fuelTypes: Array<{
    id: string;
    name: string;
    dataType: 'raw' | 'master';
  }>;
}

export interface ClientCargoI {
  tempId: string;
  shipTempId: string;
  name: string;
  description: string;
  fromDate: string;
  toDate: string | null;
}

export type ExperienceStateI = {
  experiences: ExperienceFetchReponseI;
  profileId?: string;
  loading: boolean;
  error: string | null;
  clientEquipments: ClientEquipmentI[];
  clientCargos: ClientCargoI[];
};

export type ExperienceModuleCreateOneParamsI = {
  opr: 'CREATE';
  entity: SearchResultI;
  designations?: Array<{
    opr: 'CREATE';
    fromDate: string;
    toDate: string | null;
    designation: {
      opr: OprTypeE;
      id?: string;
      fromDate?: string;
      toDate?: string | null;
      designation?: {
        id: string;
        dataType: 'raw' | 'master' | undefined;
      };
      ships?: Array<{
        id: string;
        opr: OprTypeE;
      }>;
    };
  }>;
};

export type ExperienceModuleUpdateOneParamsI = Array<{
  opr: 'UPDATE' | 'NESTED_OPR';
  id: string;
  entity?: {
    id: string;
    dataType: 'raw' | 'master' | undefined;
  };
  designations?: Array<{
    opr: OprTypeE | undefined;
    id?: string;
    fromDate?: string;
    toDate?: string | null;
    designation?: {
      id: string;
      dataType: 'raw' | 'master' | undefined;
    };
    ships?: Array<{
      id: string;
      opr: OprTypeE;
    }>;
  }>;
}>;
