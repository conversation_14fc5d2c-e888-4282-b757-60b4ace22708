/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from './types';

const HorizontalEllipsis: React.FC<OutlinedIconPropsI> = ({
  width = 2.48,
  height = 2.48,
  stroke = '#737373',
  ...props
}) => {
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 24 24"
      fill="none"
      stroke={stroke}
      strokeWidth="2"
      {...props}
    >
      <Path d="M12 13a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z" />
      <Path d="M19 13a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z" />
      <Path d="M5 13a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z" />
    </Svg>
  );
};

export default HorizontalEllipsis;
