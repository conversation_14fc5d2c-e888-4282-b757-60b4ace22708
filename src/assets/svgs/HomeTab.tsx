/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const HomeIcon: React.FC<FilledIconPropsI> = ({ width = 2.976, height = 3.348, ...props }) => {
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 24 27"
      fill="none"
      {...props}
    >
      <Path d="M4.17578 26.5V6.39648L10.0728 20.6413L4.17578 26.5Z" fill="#569E07" />
      <Path d="M18.3824 20.6413L4.17188 6.39648L10.073 20.6413L18.3824 20.6413Z" fill="#91CB4D" />
      <Path
        d="M19.5869 0.499962L19.5869 20.6035L13.6899 6.35871L19.5869 0.499962Z"
        fill="#91CB4D"
      />
      <Path d="M5.38025 6.35871L19.5908 20.6035L13.6897 6.35871L5.38025 6.35871Z" fill="#569E07" />
    </Svg>
  );
};

export default HomeIcon;
