import { useState } from 'react';
import { ActivityIndicator, Pressable, Text, View } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import CustomModal from '@/src/components/Modal';
import NotFound from '@/src/components/NotFound';
import UsersList from '@/src/components/UsersList';
import { ListItem } from '@/src/components/UsersList/types';
import BlockedListHeader from '../BlockedListHeader';
import { BlockedListNavigationI, BlockedListRouteI } from './types';
import useBlockedList from './useHook';

const BlockedList = () => {
  const navigation = useNavigation<BlockedListNavigationI>();
  const route = useRoute<BlockedListRouteI>();
  const { forumId } = route.params || {};

  const { data, loading, loadMore, refresh, refreshing, handleUnblock, isUnblocking } =
    useBlockedList({
      forumId: forumId || 'default-forum',
    });

  const [modalVisible, setModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState<ListItem | null>(null);
  const [isConfirming, setIsConfirming] = useState(false);

  const handleBack = () => {
    navigation.goBack();
  };

  const handleUserPress = (user: ListItem) => {
    //
  };

  const openUnblockModal = (user: ListItem) => {
    setSelectedUser(user);
    setModalVisible(true);
  };

  const closeUnblockModal = () => {
    setModalVisible(false);
    setSelectedUser(null);
  };

  const confirmUnblock = async () => {
    if (!selectedUser?.Profile?.id) return;

    setIsConfirming(true);
    try {
      await handleUnblock(selectedUser.Profile.id);
    } finally {
      setIsConfirming(false);
      closeUnblockModal();
    }
  };

  const renderUnblockButton = (item: ListItem) => {
    const profileId = item.Profile?.id;
    if (!profileId) return null;

    const isUnblockingUser = isUnblocking[profileId];

    return (
      <Pressable
        onPress={(e) => {
          e.stopPropagation();
          openUnblockModal(item);
        }}
        disabled={isUnblockingUser}
        className="py-2 px-4 bg-white text-black border-black border rounded-full"
      >
        {isUnblockingUser ? (
          <ActivityIndicator size="small" color={'#448600'} />
        ) : (
          <Text className="text-black text-base font-medium">Unblock</Text>
        )}
      </Pressable>
    );
  };

  if (loading && !data.length) {
    return (
      <View className="flex-1 bg-white">
        <BlockedListHeader handleBack={handleBack} />
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="small" color={'#448600'} />
        </View>
      </View>
    );
  }

  if (!data.length) {
    return (
      <View className="flex-1 bg-white">
        <BlockedListHeader handleBack={handleBack} />
        <NotFound title="No blocked users" subtitle="When you block users, they will appear here" />
      </View>
    );
  }
  return (
    <View className="flex-1 bg-white">
      <BlockedListHeader handleBack={handleBack} />
      <UsersList
        data={data}
        loading={loading}
        onLoadMore={loadMore}
        onPress={handleUserPress}
        onRefresh={refresh}
        refreshing={refreshing}
        renderActions={renderUnblockButton}
      />

      <CustomModal
        isVisible={modalVisible}
        title="Unblock User"
        description={`Are you sure you want to unblock ${selectedUser?.Profile?.name || 'this user'}?`}
        confirmText="Unblock"
        confirmButtonVariant="default"
        cancelText="Cancel"
        onConfirm={confirmUnblock}
        onCancel={closeUnblockModal}
        isConfirming={isConfirming}
      />
    </View>
  );
};

export default BlockedList;
