/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from '@/src/assets/svgs/types';

const Bulb: React.FC<OutlinedIconPropsI> = ({
  width = 2.0,
  height = 2.0,
  stroke = '#525252',
  color,
  strokeWidth = 1,
  accessibilityLabel = 'Bulb',
  disabled,
  ...props
}) => {
  const strokeColor = color || stroke;
  const opacity = disabled ? 0.5 : 1;
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 11 16"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        d="M3.143 14.4h4.714V16H3.143v-1.6zm6.212-5.03C10.164 8.405 11 7.407 11 5.6 11 2.513 8.532 0 5.5 0S0 2.513 0 5.6c0 1.828.838 2.822 1.65 3.784.282.334.574.68.853 1.08.113.164.298.796.464 1.536h-.622v1.6h6.31V12h-.62c.167-.742.353-1.375.466-1.54.276-.402.57-.752.854-1.09z"
        fill={strokeColor}
      />
    </Svg>
  );
};

export default Bulb;
