import { useEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Pressable,
  ScrollView,
  Text,
  View,
} from 'react-native';
import { useNavigation, type NavigationAction } from '@react-navigation/native';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import Checkbox from '@/src/components/Checkbox';
import DatePicker from '@/src/components/DatePicker';
import EntitySearch from '@/src/components/EntitySearch';
import CustomModal from '@/src/components/Modal';
import Select from '@/src/components/Select';
import TextInput from '@/src/components/TextInput';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { VisaNameR } from '@/src/consts/regEx';
import { validateDate } from '@/src/screens/EditEducationItem/components/utils';
import Download from '@/src/assets/svgs/Download';
import Pdf from '@/src/assets/svgs/Pdf';
import TrashBin from '@/src/assets/svgs/TrashBin';
import Upload from '@/src/assets/svgs/Upload';
import type { EditDocumentItemPropsI } from './types';
import { useEditDocumentItem } from './useHook';

export const EditDocumentItem = ({
  onBack,
  profileId,
  documentId,
  type,
}: EditDocumentItemPropsI) => {
  const countrySelection = useSelector(selectSelectionByKey('country'));
  const {
    methods,
    documentTypeOptions,
    documentNameOptions,
    isSubmitting,
    onSubmit,
    handleAttachment,
    selectedFile,
    loading,
    handleDownload,
    handleRemoveFile,
    isFileRemoved,
    clearFields,
    isPresent,
    handlePresentCheckbox,
    isSubmitted,
    setIsSubmitted,
    hasChanges,
  } = useEditDocumentItem(profileId, documentId, type);

  const { control, handleSubmit, watch } = methods;

  const navigation = useNavigation();
  const [showDiscardModal, setShowDiscardModal] = useState(false);
  const exitActionRef = useRef<NavigationAction | null>(null);

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', (e) => {
      if (hasChanges && !isSubmitting) {
        e.preventDefault();
        exitActionRef.current = e.data.action;
        setShowDiscardModal(true);
      }
    });
    return unsubscribe;
  }, [navigation, hasChanges, isSubmitting]);

  useEffect(() => {
    if (countrySelection) {
      methods.setValue('country', countrySelection, { shouldDirty: true });
    }
    return () => {
      clearFields();
    };
  }, [countrySelection, methods, clearFields]);

  const FromDate = watch('validFrom');
  const toDate = watch('validUntil');

  const getFileIcon = (fileName: string) => {
    const extension = fileName?.toLowerCase().split('.').pop();
    if (extension === 'pdf') {
      return <Pdf width={2} height={2} color="#DC2626" />;
    }
    return <Upload width={2} height={2} color="#6B7280" />;
  };

  const getFileTypeText = (fileName: string) => {
    const extension = fileName?.toLowerCase().split('.').pop();
    if (extension === 'pdf') {
      return 'PDF Document';
    }
    if (['jpg', 'jpeg', 'png'].includes(extension || '')) {
      return 'Image File';
    }
    return 'Document';
  };

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#448600" />
      </View>
    );
  }

  const handleDiscardConfirm = () => {
    setShowDiscardModal(false);
    setTimeout(() => {
      if (exitActionRef.current) {
        navigation.dispatch(exitActionRef.current);
      }
    }, 300);
  };

  const handleDiscardCancel = () => {
    setShowDiscardModal(false);
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1 bg-white"
    >
      <ScrollView
        className="flex-1 bg-white"
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ flexGrow: 1 }}
      >
        <View className="px-4">
          <View className="flex-row items-center justify-between py-4">
            <BackButton onBack={onBack} label="" />
            <Pressable
              onPress={() => {
                setIsSubmitted(true);
                handleSubmit(onSubmit)();
              }}
              disabled={isSubmitting || !hasChanges}
            >
              <Text
                className={`text-lg font-medium ${
                  isSubmitting || !hasChanges ? 'text-gray-400' : 'text-[#448600]'
                }`}
              >
                {isSubmitting ? 'Saving...' : 'Save'}
              </Text>
            </Pressable>
          </View>
          <View className="mb-6">
            <Text className="mb-2 text-base font-medium">Document type</Text>
            <Controller
              control={control}
              name="documentType"
              rules={{ required: 'Document type is required' }}
              render={({ field: { onChange, value }, fieldState: { error } }) => (
                <Select
                  options={documentTypeOptions}
                  value={value}
                  onChange={(val) => {
                    onChange(val);
                    methods.setValue('documentType', val, { shouldDirty: true });
                  }}
                  placeholder="Select document type"
                  error={error?.message}
                  disabled={!!documentId}
                />
              )}
            />
          </View>
          <View className="mb-6">
            {methods.watch('documentType')?.toLowerCase() !== 'identity' ? (
              <Controller
                control={control}
                name="documentName"
                rules={{
                  required: 'Visa type is required',
                  pattern: {
                    value: VisaNameR,
                    message:
                      'Only letters, numbers, spaces, hyphens, apostrophes, periods, parentheses, and forward slashes are allowed',
                  },
                }}
                render={({ field: { onChange, value }, fieldState: { error } }) => (
                  <TextInput
                    label="Visa Type"
                    placeholder="Enter type"
                    value={value}
                    onChangeText={(text) => {
                      onChange(text);
                    }}
                    editable={documentId ? false : true}
                    error={error?.message}
                  />
                )}
              />
            ) : (
              <>
                <Text className="mb-2 text-base font-medium">Document name</Text>
                <Controller
                  control={control}
                  name="documentName"
                  rules={{ required: 'Document name is required' }}
                  render={({ field: { onChange, value }, fieldState: { error } }) => (
                    <Select
                      options={documentNameOptions}
                      value={value}
                      onChange={(val) => {
                        onChange(val);
                      }}
                      placeholder="Select document name"
                      error={error?.message}
                      disabled={!!documentId}
                    />
                  )}
                />
              </>
            )}
          </View>
          <View className="mb-6">
            <Text className="mb-3 text-base font-semibold text-gray-900">
              Upload document (optional)
            </Text>
            {selectedFile || (methods.watch('documentFile') && !isFileRemoved) ? (
              <View className="border border-dashed border-[#E5E5E5] rounded-xl p-6 bg-gray-50/30">
                <View className="flex-row items-center justify-between">
                  <View className="flex-1 pr-4">
                    {selectedFile ? (
                      <View className="flex-row items-center">
                        <View className="mr-3">{getFileIcon(selectedFile)}</View>
                        <View className="flex-1">
                          <Text
                            className="text-sm font-semibold text-gray-900 mb-1"
                            numberOfLines={1}
                          >
                            {selectedFile}
                          </Text>
                          <Text className="text-xs text-gray-500">
                            {getFileTypeText(selectedFile)} • Uploaded
                          </Text>
                        </View>
                      </View>
                    ) : (
                      <View className="flex-row items-center">
                        <View className="mr-3">
                          <Pdf width={2} height={2} color="#DC2626" />
                        </View>
                        <View className="flex-1">
                          <Text className="text-sm font-semibold text-gray-900 mb-1">
                            Document uploaded
                          </Text>
                          <Text className="text-xs text-gray-500">Document file available</Text>
                        </View>
                      </View>
                    )}
                  </View>
                  <View className="flex-row items-center gap-x-2">
                    {methods.watch('documentFile') && !selectedFile && (
                      <Pressable onPress={handleDownload} className="p-2 bg-gray-50 rounded-lg">
                        <Download width={2} height={2} color="#6B7280" />
                      </Pressable>
                    )}
                    <Pressable
                      onPress={handleAttachment}
                      className="p-2 bg-gray-50 rounded-lg active:bg-gray-100"
                    >
                      <Upload width={2} height={2} color="#6B7280" />
                    </Pressable>
                    <Pressable
                      onPress={handleRemoveFile}
                      className="p-2 bg-gray-50 rounded-lg active:bg-red-100"
                    >
                      <TrashBin width={2} height={2} color="#DC2626" />
                    </Pressable>
                  </View>
                </View>
              </View>
            ) : isFileRemoved ? (
              <View className="border border-dashed border-red-300 rounded-xl p-6 bg-red-50/50">
                <View className="flex-row items-center justify-between">
                  <View className="flex-1 pr-4">
                    <View className="flex-row items-center">
                      <View className="mr-3">
                        <TrashBin width={2} height={2} color="#DC2626" />
                      </View>
                      <View className="flex-1">
                        <Text className="text-sm font-medium text-red-700 mb-1">
                          File marked for removal
                        </Text>
                        <Text className="text-xs text-red-600">
                          This file will be permanently removed when you save
                        </Text>
                      </View>
                    </View>
                  </View>
                  <Pressable
                    onPress={handleAttachment}
                    className="p-2 bg-white rounded-lg shadow-sm active:bg-gray-50 border border-gray-200"
                  >
                    <Upload width={2} height={2} color="#6B7280" />
                  </Pressable>
                </View>
              </View>
            ) : (
              <Pressable
                className="border border-dashed border-[#E5E5E5] rounded-xl p-8 items-center bg-gray-50/20 active:bg-gray-50/40"
                onPress={handleAttachment}
              >
                <View className="w-12 h-12 bg-gray-100 rounded-full items-center justify-center mb-4">
                  <Upload width={2} height={2} color="#6B7280" />
                </View>
                <Text className="text-base font-semibold text-gray-900 mb-2">Upload file</Text>
                <View className="items-center">
                  <Text className="text-sm text-[#6B7280] text-center leading-5">
                    Supported file types:
                  </Text>
                  <Text className="text-sm text-center leading-5 mt-1">
                    <Text className="font-semibold text-[#4B5563]">PDF</Text>
                    <Text className="text-[#6B7280]"> (Max 4MB), </Text>
                    <Text className="font-semibold text-[#4B5563]">JPG</Text>
                    <Text className="text-[#6B7280]"> and </Text>
                    <Text className="font-semibold text-[#4B5563]">PNG</Text>
                    <Text className="text-[#6B7280]"> (Max 500KB)</Text>
                  </Text>
                </View>
              </Pressable>
            )}
          </View>
          <View className="mb-3">
            <Controller
              control={control}
              name="documentNumber"
              rules={{
                required: 'Document number is required',
                minLength: { value: 3, message: 'Document number must be at least 3 characters' },
              }}
              render={({ field: { onChange, value }, fieldState: { error } }) => (
                <TextInput
                  label="Document number"
                  placeholder="Enter document number"
                  value={value}
                  onChangeText={(text) => {
                    onChange(text);
                    methods.setValue('documentNumber', text, { shouldDirty: true });
                  }}
                  error={error?.message}
                />
              )}
            />
          </View>
          <Controller
            control={control}
            name="country"
            rules={{ required: 'Country is required' }}
            render={({ fieldState: { error } }) => (
              <EntitySearch
                placeholder="Search country"
                selectionKey="country"
                title="Country"
                data={
                  countrySelection ? countrySelection.name : methods.watch('country')?.name || ''
                }
                error={
                  isSubmitted && !countrySelection && !methods.watch('country')?.id
                    ? `Country is required`
                    : error?.message
                }
              />
            )}
          />
          <View className="flex-row mb-6 mt-3">
            <View className="flex-1 mr-2">
              <Controller
                control={control}
                name="validFrom"
                rules={{
                  required: 'Valid From date is required',
                  validate: (value) => validateDate(value, toDate),
                }}
                render={({ field: { onChange }, fieldState: { error } }) => (
                  <>
                    <DatePicker
                      title="Valid from"
                      selectedDate={FromDate}
                      onDateChange={(date) => {
                        if (date instanceof Date) {
                          onChange(date.toISOString().split('T')[0]);
                        }
                      }}
                      showMonthYear={true}
                      className={error ? 'border-red-500' : ''}
                    />
                    {error && <Text className="text-red-500 text-xs mt-1">{error.message}</Text>}
                  </>
                )}
              />
            </View>
            <View className="flex-1 ml-2">
              <Controller
                control={control}
                name="validUntil"
                rules={{
                  required: !isPresent
                    ? 'Valid Until date is required when "Unlimited" is not checked'
                    : false,
                  validate: (value) => {
                    if (isPresent) return true;
                    if (!value) return 'Valid Until date is required';
                    return validateDate(FromDate, value);
                  },
                }}
                render={({ field: { onChange }, fieldState: { error } }) => (
                  <>
                    <DatePicker
                      title="Valid until"
                      selectedDate={isPresent ? null : toDate}
                      onDateChange={(date) => {
                        if (date instanceof Date && !isPresent) {
                          onChange(date.toISOString().split('T')[0]);
                        }
                      }}
                      showMonthYear={true}
                      disabled={isPresent}
                      className={error ? 'border-red-500' : ''}
                    />
                    {error && !isPresent && (
                      <Text className="text-red-500 text-xs mt-1">{error.message}</Text>
                    )}
                  </>
                )}
              />
              <Checkbox
                label="Unlimited"
                className="pt-3"
                labelClassName="text-base text-sm"
                onValueChange={handlePresentCheckbox}
                checked={isPresent}
              />
            </View>
          </View>
        </View>
        <CustomModal
          isVisible={showDiscardModal}
          title="Discard changes?"
          description="You have unsaved changes. Are you sure you want to discard them and leave this screen?"
          onCancel={handleDiscardCancel}
          onConfirm={handleDiscardConfirm}
          cancelText="Cancel"
          confirmText="Discard"
          confirmButtonVariant="danger"
        />
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default EditDocumentItem;
