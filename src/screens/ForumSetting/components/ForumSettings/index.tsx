import { Pressable, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import BackButton from '@/src/components/BackButton';
import UserAvatar from '@/src/components/UserAvatar';
import ChevronRight from '@/src/assets/svgs/ChevronRight';
import { ForumSettingNavigationI, ForumSettingOptionI } from './types';

const adminUsers = [
  {
    id: '1',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    avatar:
      'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=60&h=60&fit=crop&crop=face',
  },
  {
    id: '2',
    name: '<PERSON>',
    avatar:
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face',
  },
  {
    id: '3',
    name: 'long longlong name',
    avatar:
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face',
  },
];

const moderatorUsers = [
  {
    id: '3',
    name: '<PERSON>',
    avatar:
      'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=60&h=60&fit=crop&crop=face',
  },
  {
    id: '4',
    name: 'Ole Ullrich',
    avatar:
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face',
  },
];

const renderSettingOption = (option: ForumSettingOptionI) => (
  <Pressable
    key={option.id}
    onPress={option.onPress}
    className="flex-row items-center justify-between px-4 py-4 bg-white border border-borderGrayLight rounded-xl mb-6"
    android_ripple={{ color: '#F3F4F6' }}
  >
    <View className="flex-row items-center flex-1">
      <View className="flex-1">
        <Text className="text-sm">{option.title}</Text>
      </View>
    </View>
    {option.showChevron && <ChevronRight width={2.5} height={2.5} />}
  </Pressable>
);

const renderUserSection = (title: string, users: typeof adminUsers, onPress: () => void) => (
  <View className="bg-white rounded-lg mb-4">
    <Pressable onPress={onPress} className="p-4">
      <Text className="text-base font-normal leading-4 mb-2">{title}</Text>
      <View className="flex-row flex-wrap border border-borderGrayLight rounded-xl px-4 py-2 items-center">
        {users.map((user) => (
          <View
            key={user.id}
            className={`flex-row items-center border border-borderGrayExtraLight p-2 rounded-full gap-2 mb-2 mr-2`}
          >
            <UserAvatar avatarUri={user.avatar} name={user.name} width={20} height={20} />
            <Text className="text-sm font-medium text-gray-700">{user.name}</Text>
          </View>
        ))}
      </View>
    </Pressable>
  </View>
);

const ForumSettings = () => {
  const navigation = useNavigation<ForumSettingNavigationI>();

  const handleBack = () => {
    navigation.goBack();
  };

  const navigateToAdminModerator = () => {
    navigation.navigate('People');
  };

  const navigateToMemberList = () => {
    navigation.navigate('CommunityMembers', {});
  };

  const navigateToBlockedList = () => {
    navigation.navigate('CommunityBlocked', {});
  };

  const navigateToUserRequests = () => {
    navigation.navigate('CommunityRequests', {});
  };

  const settingOptions: ForumSettingOptionI[] = [
    {
      id: 'member-list',
      title: 'Member List',
      onPress: navigateToMemberList,
      showChevron: true,
    },
    {
      id: 'blocked-users',
      title: 'Blocked users',
      onPress: navigateToBlockedList,
      showChevron: true,
    },
    {
      id: 'user-requests',
      title: 'User requests',
      onPress: navigateToUserRequests,
      showChevron: true,
    },
  ];
  return (
    <View className="flex-1 bg-white">
      <View className="flex-row items-center justify-between px-4 py-3  border-b border-gray-100">
        <View className="flex-row items-center">
          <BackButton onBack={handleBack} label="" />
          <Text className="text-xl font-medium">Forum settings</Text>
        </View>
        <Pressable className="bg-primaryGreen px-4 py-2 rounded-full">
          <Text className="text-white font-medium">Apply</Text>
        </Pressable>
      </View>

      {renderUserSection('Admin', adminUsers, navigateToAdminModerator)}

      {renderUserSection('Moderator', moderatorUsers, navigateToAdminModerator)}

      <View className="mx-4">{settingOptions.map(renderSettingOption)}</View>
    </View>
  );
};

export default ForumSettings;
