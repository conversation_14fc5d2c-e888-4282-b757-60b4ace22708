/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { apiCall } from '@/src/services/api';
import {
  ReactionFetchManyI,
  ReactionPostIdI,
  ReactionUpsertI,
  ReactionUpsertResponseI,
  ReactionExternalClientI,
  ReactionDeleteResponseI,
} from './types';

export const upsertReactionAPI = async (
  payload: ReactionUpsertI,
): Promise<ReactionUpsertResponseI> => {
  const result = await apiCall<ReactionUpsertI, ReactionUpsertResponseI>(
    '/backend/api/v1/feed/reaction',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};

export const fetchReactionsAPI = async (
  query: ReactionFetchManyI,
): Promise<ReactionExternalClientI> => {
  const result = await apiCall<null, ReactionExternalClientI>(
    `/backend/api/v1/feed/reactions`,
    'GET',
    {
      isAuth: true,
      query,
    },
  );

  return result;
};

export const deleteReactionAPI = async (
  query: ReactionPostIdI,
): Promise<ReactionDeleteResponseI> => {
  const result = await apiCall<null, ReactionDeleteResponseI>(
    `/backend/api/v1/feed/reaction`,
    'DELETE',
    {
      isAuth: true,
      query,
    },
  );

  return result;
};
