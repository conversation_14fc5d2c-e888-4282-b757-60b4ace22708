/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { createSlice, createAsyncThunk, type PayloadAction } from '@reduxjs/toolkit';
import type { VoteTypeE } from '@/src/networks/forum/types';
import {
  createQuestionAPI,
  fetchForumQuestionsAPI,
  deleteQuestionAPI,
  updateLiveModeAPI as updateQuestionLiveStatusAPI,
} from '@/src/networks/question/question';
import {
  ForumQuestionFetchManyResultI,
  QuestionDeleteOnePayloadI,
  QuestionI,
} from '@/src/networks/question/types';
import { clearSelection, clearMultipleSelections } from '../entitysearch/searchSlice';
import { SearchResultI } from '../entitysearch/types';
import type {
  QuestionStateI,
  QuestionFormDataI,
  QuestionCreatePayloadI,
  QuestionMediaCreateItemI,
  IdTypeI,
  ForumQuestionFetchManyPayloadI,
  ForumQuestionsFiltersI,
  ImoTypeI,
  UpdateQuestionLiveStatusPayloadI,
  UpdateQuestionLiveStatusResponseI,
} from './types';

const initialFormData: QuestionFormDataI = {
  title: '',
  description: '',
  type: 'NORMAL',
  communityId: '',
  isAnonymous: false,
  isLive: false,
  topics: [],
  equipmentCategory: null,
  equipmentModel: null,
  equipmentManufacturer: null,
  department: null,
  imo: null,
  files: [],
  isGeneral: false,
};

const initialState: QuestionStateI = {
  formData: initialFormData,
  loading: false,
  error: null,
  forumQuestions: [],
  forumQuestionsLoading: false,
  forumQuestionsRefreshing: false,
  forumQuestionsPagination: {
    nextCursorDate: null,
    hasMore: true,
    total: 0,
  },
  forumQuestionsFilters: {
    type: 'ALL',
    isLive: false,
    myRecommended: false,
    myQuestion: false,
    myAnswered: false,
    myCommunity: false,
    department: null,
    topics: [],
    equipmentCategory: null,
    equipmentManufacturer: null,
    equipmentModel: null,
  },
};

const convertToIdType = (searchResult: SearchResultI): IdTypeI => ({
  id: searchResult.id,
  dataType: searchResult.dataType,
});

const convertToImoType = (searchResult: {
  imo: string;
  dataType: 'raw' | 'master';
  name: string;
}): ImoTypeI => ({
  imo: searchResult.imo,
  dataType: searchResult.dataType,
});

export const resetAllQuestionStates = createAsyncThunk(
  'question/resetAllQuestionStates',
  async (_, { dispatch }) => {
    dispatch(resetFormData());
    const selectionKeys = [
      'ship', // IMO
      'equipmentCategory',
      'equipmentManufacturer',
      'equipmentModel',
      'department',
    ];

    selectionKeys.forEach((key) => {
      dispatch(clearSelection(key));
    });

    dispatch(clearMultipleSelections('topic'));
  },
);

export const createQuestion = createAsyncThunk(
  'question/createQuestion',
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { question: QuestionStateI };
      const { formData } = state.question;

      const payload: QuestionCreatePayloadI = {
        title: formData.title,
        description: formData.description,
        type: formData.type,
        communityId: formData.communityId,
        isAnonymous: formData.isAnonymous,
        isLive: formData.isLive,
        ...(formData.files.length > 0 && { files: formData.files }),
      };

      if (formData.type === 'NORMAL') {
        if (formData.topics.length > 0) {
          payload.topics = formData.topics.map(convertToIdType);
        }
      } else {
        if (formData.equipmentCategory) {
          payload.equipmentCategory = convertToIdType(formData.equipmentCategory);
        }
        if (formData.equipmentManufacturer) {
          payload.equipmentManufacturer = convertToIdType(formData.equipmentManufacturer);
        }
        if (formData.equipmentModel) {
          payload.equipmentModel = convertToIdType(formData.equipmentModel);
        }
        if (formData.imo) {
          payload.ship = convertToImoType(
            formData.imo as unknown as { imo: string; dataType: 'master' | 'raw'; name: string },
          );
        }
      }

      if (formData.department) {
        payload.department = convertToIdType(formData.department);
      } else if (formData.isGeneral) {
        payload.department = null;
      }
      const response = await createQuestionAPI(payload);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to create question');
    }
  },
);

export const fetchForumQuestions = createAsyncThunk(
  'question/fetchForumQuestions',
  async (
    {
      refresh = false,
      ...overrideFilters
    }: Partial<ForumQuestionFetchManyPayloadI> & { refresh?: boolean },
    { getState, rejectWithValue },
  ) => {
    try {
      const state = getState() as { question: QuestionStateI };
      const { forumQuestionsFilters, forumQuestionsPagination } = state.question;

      // Convert SearchResultI to IdTypeI for API
      const convertToIdType = (searchResult: SearchResultI | null): IdTypeI | undefined => {
        if (!searchResult) return undefined;
        return {
          id: searchResult.id,
          dataType: searchResult.dataType,
        };
      };

      const convertTopicsToIdType = (topics: SearchResultI[]): IdTypeI[] => {
        return topics.map((topic) => ({
          id: topic.id,
          dataType: topic.dataType,
        }));
      };

      const query: ForumQuestionFetchManyPayloadI = {
        pageSize: '10',
        cursorDate: refresh ? null : forumQuestionsPagination.nextCursorDate,
        type: overrideFilters.type || forumQuestionsFilters.type,
        ...((overrideFilters.isLive !== undefined
          ? overrideFilters.isLive
          : forumQuestionsFilters.isLive) && {
          isLive:
            overrideFilters.isLive !== undefined
              ? overrideFilters.isLive
              : forumQuestionsFilters.isLive,
        }),
        ...((overrideFilters.myRecommended !== undefined
          ? overrideFilters.myRecommended
          : forumQuestionsFilters.myRecommended) && {
          myRecommended:
            overrideFilters.myRecommended !== undefined
              ? overrideFilters.myRecommended
              : forumQuestionsFilters.myRecommended,
        }),
        ...((overrideFilters.myQuestion !== undefined
          ? overrideFilters.myQuestion
          : forumQuestionsFilters.myQuestion) && {
          myQuestion:
            overrideFilters.myQuestion !== undefined
              ? overrideFilters.myQuestion
              : forumQuestionsFilters.myQuestion,
        }),
        ...((overrideFilters.myAnswered !== undefined
          ? overrideFilters.myAnswered
          : forumQuestionsFilters.myAnswered) && {
          myAnswered:
            overrideFilters.myAnswered !== undefined
              ? overrideFilters.myAnswered
              : forumQuestionsFilters.myAnswered,
        }),
        ...((overrideFilters.myCommunity !== undefined
          ? overrideFilters.myCommunity
          : forumQuestionsFilters.myCommunity) && {
          myCommunity:
            overrideFilters.myCommunity !== undefined
              ? overrideFilters.myCommunity
              : forumQuestionsFilters.myCommunity,
        }),
        ...((overrideFilters.department || convertToIdType(forumQuestionsFilters.department)) && {
          departmentId: overrideFilters.department?.id || forumQuestionsFilters.department?.id,
          departmentDataType:
            overrideFilters.department?.dataType || forumQuestionsFilters.department?.dataType,
        }),
        ...(forumQuestionsFilters.topics.length > 0 && {
          topics: convertTopicsToIdType(forumQuestionsFilters.topics),
        }),
        ...(convertToIdType(forumQuestionsFilters.equipmentCategory) && {
          equipmentCategory: convertToIdType(forumQuestionsFilters.equipmentCategory),
        }),
        ...(convertToIdType(forumQuestionsFilters.equipmentManufacturer) && {
          equipmentManufacturer: convertToIdType(forumQuestionsFilters.equipmentManufacturer),
        }),
        ...(convertToIdType(forumQuestionsFilters.equipmentModel) && {
          equipmentModel: convertToIdType(forumQuestionsFilters.equipmentModel),
        }),
      };

      const response = await fetchForumQuestionsAPI(query);
      return { response, refresh };
    } catch (error) {
      return rejectWithValue('Failed to fetch forum questions');
    }
  },
);

export const deleteQuestion = createAsyncThunk<
  void,
  QuestionDeleteOnePayloadI,
  { rejectValue: string }
>('question/deleteQuestion', async (payload, { rejectWithValue }) => {
  try {
    const response = await deleteQuestionAPI(payload);
    return response;
  } catch (error) {
    return rejectWithValue(error instanceof Error ? error.message : 'Failed to delete question');
  }
});

export const updateQuestionLiveStatus = createAsyncThunk<
  UpdateQuestionLiveStatusResponseI,
  UpdateQuestionLiveStatusPayloadI,
  { rejectValue: string }
>('question/updateLiveStatus', async (payload, { rejectWithValue, dispatch }) => {
  try {
    const result = await updateQuestionLiveStatusAPI(payload.questionId, payload.isLive);

    dispatch(
      updateQuestionLiveStatusOptimistic({
        questionId: payload.questionId,
        isLive: result.isLive,
        liveStartedAt: result.liveStartedAt,
      }),
    );

    return result;
  } catch (error) {
    return rejectWithValue(
      error instanceof Error ? error.message : 'Failed to update question live status',
    );
  }
});

const questionSlice = createSlice({
  name: 'question',
  initialState,
  reducers: {
    updateFormData: (state, action: PayloadAction<Partial<QuestionFormDataI>>) => {
      state.formData = { ...state.formData, ...action.payload };
    },
    setQuestionType: (state, action: PayloadAction<'NORMAL' | 'TROUBLESHOOT'>) => {
      state.formData.type = action.payload;
      if (action.payload === 'NORMAL') {
        state.formData.equipmentCategory = null;
        state.formData.equipmentModel = null;
        state.formData.equipmentManufacturer = null;
      } else {
        state.formData.topics = [];
      }
    },
    setTopics: (state, action: PayloadAction<SearchResultI[]>) => {
      state.formData.topics = action.payload;
    },
    setEquipmentCategory: (state, action: PayloadAction<SearchResultI | null>) => {
      state.formData.equipmentCategory = action.payload;
    },
    setEquipmentModel: (state, action: PayloadAction<SearchResultI | null>) => {
      state.formData.equipmentModel = action.payload;
    },
    setEquipmentManufacturer: (state, action: PayloadAction<SearchResultI | null>) => {
      state.formData.equipmentManufacturer = action.payload;
    },
    setImo: (state, action: PayloadAction<SearchResultI | null>) => {
      state.formData.imo = action.payload;
    },
    setDepartment: (state, action: PayloadAction<SearchResultI | null>) => {
      state.formData.department = action.payload;
    },
    setIsGeneral: (state, action: PayloadAction<boolean>) => {
      state.formData.isGeneral = action.payload;
    },
    setIsAnonymous: (state, action: PayloadAction<boolean>) => {
      state.formData.isAnonymous = action.payload;
    },
    setIsLive: (state, action: PayloadAction<boolean>) => {
      state.formData.isLive = action.payload;
    },
    setCommunityId: (state, action: PayloadAction<string>) => {
      state.formData.communityId = action.payload;
    },
    setFiles: (state, action: PayloadAction<QuestionMediaCreateItemI[]>) => {
      state.formData.files = action.payload;
    },
    resetFormData: (state) => {
      state.formData = initialFormData;
      state.error = null;
    },
    clearError: (state) => {
      state.error = null;
    },
    setForumQuestionsFilters: (state, action: PayloadAction<Partial<ForumQuestionsFiltersI>>) => {
      state.forumQuestionsFilters = { ...state.forumQuestionsFilters, ...action.payload };
    },
    resetForumQuestionsFilters: (state) => {
      state.forumQuestionsFilters = {
        type: 'ALL',
        isLive: false,
        myRecommended: false,
        myQuestion: false,
        myAnswered: false,
        myCommunity: false,
        department: null,
        topics: [],
        equipmentCategory: null,
        equipmentManufacturer: null,
        equipmentModel: null,
      };
    },
    clearForumQuestions: (state) => {
      state.forumQuestions = [];
      state.forumQuestionsPagination = {
        nextCursorDate: null,
        hasMore: true,
        total: 0,
      };
    },
    addQuestionVoteOptimistic: (
      state,
      action: PayloadAction<{ questionId: string; type: VoteTypeE }>,
    ) => {
      const { questionId, type } = action.payload;
      const question = state.forumQuestions.find((q) => q.id === questionId);
      if (question) {
        if (type === 'UPVOTE') {
          question.upvoteCount = (question.upvoteCount || 0) + 1;
          question.userVote = 'UPVOTE';
        } else {
          question.downvoteCount = (question.downvoteCount || 0) + 1;
          question.userVote = 'DOWNVOTE';
        }
      }
    },
    removeQuestionVoteOptimistic: (
      state,
      action: PayloadAction<{ questionId: string; type: VoteTypeE }>,
    ) => {
      const { questionId, type } = action.payload;
      const question = state.forumQuestions.find((q) => q.id === questionId);
      if (question) {
        if (type === 'UPVOTE') {
          question.upvoteCount = Math.max(0, (question.upvoteCount || 0) - 1);
        } else {
          question.downvoteCount = Math.max(0, (question.downvoteCount || 0) - 1);
        }
        question.userVote = null;
      }
    },
    switchQuestionVoteOptimistic: (
      state,
      action: PayloadAction<{ questionId: string; fromType: VoteTypeE; toType: VoteTypeE }>,
    ) => {
      const { questionId, fromType, toType } = action.payload;
      const question = state.forumQuestions.find((q) => q.id === questionId);
      if (question) {
        if (fromType === 'UPVOTE') {
          question.upvoteCount = Math.max(0, (question.upvoteCount || 0) - 1);
        } else {
          question.downvoteCount = Math.max(0, (question.downvoteCount || 0) - 1);
        }
        if (toType === 'UPVOTE') {
          question.upvoteCount = (question.upvoteCount || 0) + 1;
        } else {
          question.downvoteCount = (question.downvoteCount || 0) + 1;
        }
        question.userVote = toType;
      }
    },
    removeQuestionOptimistic: (state, action: PayloadAction<{ questionId: string }>) => {
      const { questionId } = action.payload;
      state.forumQuestions = state.forumQuestions.filter((question) => question.id !== questionId);
    },
    updateQuestionLiveStatusOptimistic: (
      state,
      action: PayloadAction<{ questionId: string; isLive: boolean; liveStartedAt: string | null }>,
    ) => {
      const { questionId, isLive, liveStartedAt } = action.payload;
      const question = state.forumQuestions.find((q) => q.id === questionId);
      if (question) {
        question.isLive = isLive;
        question.liveStartedAt = liveStartedAt || question.liveStartedAt;
      }
      if (state.forumQuestionsFilters.isLive) {
        if (!isLive) {
          state.forumQuestions = state.forumQuestions.filter(
            (question) => question?.id !== questionId,
          );
        }
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(createQuestion.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createQuestion.fulfilled, (state) => {
        state.loading = false;
        state.error = null;
        state.formData = initialFormData;
      })
      .addCase(createQuestion.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchForumQuestions.pending, (state, action) => {
        const { refresh } = action.meta.arg;
        if (refresh) {
          state.forumQuestionsRefreshing = true;
        } else {
          state.forumQuestionsLoading = true;
        }
      })
      .addCase(fetchForumQuestions.fulfilled, (state, action) => {
        const { response, refresh } = action.payload;
        state.forumQuestionsLoading = false;
        state.forumQuestionsRefreshing = false;

        const transformedData = response.data.map((question) => ({
          ...question,
          userVote: question.vote,
        })) as ForumQuestionFetchManyResultI['data'];

        if (refresh) {
          if (transformedData.length) {
            state.forumQuestions = transformedData;
          } else {
            state.forumQuestions = refresh ? state.forumQuestions : [];
          }
        } else {
          const existingIds = new Set(state.forumQuestions.map((q) => q.id));
          const newQuestions = transformedData.filter((q) => !existingIds.has(q.id));
          if (newQuestions.length) {
            state.forumQuestions.push(...newQuestions);
          }
        }

        state.forumQuestionsPagination = {
          hasMore: state.forumQuestions.length < response.total,
          nextCursorDate: response.nextCursorDate,
          total: response.total,
        };
      })
      .addCase(fetchForumQuestions.rejected, (state) => {
        state.forumQuestionsLoading = false;
        state.forumQuestionsRefreshing = false;
      })
      .addCase(deleteQuestion.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteQuestion.fulfilled, (state) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(deleteQuestion.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  updateFormData,
  setQuestionType,
  setTopics,
  setEquipmentCategory,
  setEquipmentModel,
  setEquipmentManufacturer,
  setImo,
  setDepartment,
  setIsGeneral,
  setIsAnonymous,
  setIsLive,
  setCommunityId,
  setFiles,
  resetFormData,
  clearError,
  setForumQuestionsFilters,
  resetForumQuestionsFilters,
  clearForumQuestions,
  addQuestionVoteOptimistic,
  removeQuestionVoteOptimistic,
  switchQuestionVoteOptimistic,
  removeQuestionOptimistic,
  updateQuestionLiveStatusOptimistic,
} = questionSlice.actions;

export default questionSlice.reducer;
