/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { CommentTypeI } from '../CommentItem/types';

export interface CommentsListProps {
  postId: string;
  type: 'USER_POST' | 'SCRAPBOOK_POST';
  portUnLocode?: string;
  onReplyPress?: (comment: CommentTypeI) => void;
}

export interface CommentsListRef {
  scrollToTop: () => void;
}
