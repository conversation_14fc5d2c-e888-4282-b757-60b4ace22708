/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { ClipPath, Defs, G, Path, Rect } from 'react-native-svg';
import { FilledIconPropsI } from './types';

interface LikeProps extends FilledIconPropsI {
  isLiked: boolean;
}

const Like: React.FC<LikeProps> = ({ isLiked, width = 1.984, height = 2.108, ...props }) => {
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 16 17"
      fill="none"
      {...props}
    >
      <G clipPath="url(#clip0)">
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M0 8.48674V14.582C0 15.4239 0.716005 16.0898 1.6 16.0898H3.2V6.94693H1.6C0.716005 6.94693 0 7.64484 0 8.48674ZM15.9432 9.50311L14.5528 14.4821C14.324 15.4277 13.44 16.0898 12.4208 16.0898H4.8V6.96293L6.1752 2.24293C6.3376 1.2395 7.43199 0.565969 8.5408 0.971302C9.20399 1.21435 9.59999 1.87186 9.59999 2.54919V6.20103C9.59999 6.6216 9.9584 6.94693 10.4 6.94693H13.8112C15.2184 6.94693 16.2592 8.19797 15.9432 9.50311Z"
          fill={isLiked ? '#448600' : '#525252'}
        />
      </G>
      <Defs>
        <ClipPath id="clip0">
          <Rect width={16} height={16} fill="white" transform="translate(0 0.470703)" />
        </ClipPath>
      </Defs>
    </Svg>
  );
};

export default Like;
