import { StackNavigationProp } from '@react-navigation/stack';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { ProfileStackParamsListI } from '@/src/navigation/types';

export type AllEducationsNavigationProp = StackNavigationProp<ProfileStackParamsListI>;

export type EducationI = {
  id: string;
  degree: SearchResultI;
  entity: SearchResultI;
  fromDate: string;
  toDate: string;
};

export type ProfileEducationPropsI = {
  isUserProfile: boolean;
  profileId: string;
};
