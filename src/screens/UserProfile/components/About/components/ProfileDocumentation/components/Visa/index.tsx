import React from 'react';
import { ActivityIndicator, Text, TouchableOpacity, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import NotFound from '@/src/components/NotFound';
import { BottomTabNavigationI } from '@/src/navigation/types';
import { VisaDocumentDetails } from '../DocumentDetails';
import { expiresIn } from '../utils';
import { ProfileVisaDocumentationI, ProfileVisaPropsI } from './types';
import { useProfileVisa } from './useHook';

const ProfileVisa: React.FC<ProfileVisaPropsI> = ({ profileId, isUserProfile }) => {
  const { documentations, count, loading } = useProfileVisa(profileId);
  const navigation = useNavigation<BottomTabNavigationI>();

  if (loading) {
    return <ActivityIndicator className="pt-14 pb-6" />;
  }

  return (
    <View>
      {documentations.map((item: ProfileVisaDocumentationI, index: number) => (
        <VisaDocumentDetails
          key={index}
          document={item}
          isLast={index === documentations.length - 1}
          expiresIn={expiresIn(item.untilDate)}
        />
      ))}
      {count > 3 && (
        <TouchableOpacity
          onPress={() =>
            navigation.navigate(isUserProfile ? 'ProfileStack' : 'HomeStack', {
              screen: 'EditDocumentList',
              params: {
                editable: false,
                tab: 'visa',
                profileId: profileId,
              },
            })
          }
        >
          <Text className="text-[#448600] text-base font-medium pt-4">
            {`View all ${count} visa documents`}
          </Text>
        </TouchableOpacity>
      )}
      {count === 0 && (
        <NotFound
          className="pt-10 pb-4"
          titleClassName="font-normal"
          imageStyle={{ height: 120, width: 120 }}
          fullScreen={false}
        />
      )}
    </View>
  );
};

export default ProfileVisa;
