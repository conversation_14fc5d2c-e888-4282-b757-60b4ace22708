/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const ChevronDown: React.FC<FilledIconPropsI> = ({
  width = 1.86,
  height = 1.24,
  fill = '#000000',
  color,
  disabled,
  accessibilityLabel = 'Chevron Down',
  ...props
}) => {
  const fillColor = color || fill;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 18 10"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        d="M16.8976 1.86833L9.39763 9.36833C9.29216 9.47367 9.14919 9.53284 9.00013 9.53284C8.85107 9.53284 8.7081 9.47367 8.60263 9.36833L1.10263 1.86833C1.00327 1.7617 0.949177 1.62067 0.951748 1.47494C0.954319 1.32922 1.01335 1.19018 1.11641 1.08712C1.21947 0.984057 1.35851 0.925022 1.50424 0.922451C1.64996 0.91988 1.791 0.973973 1.89763 1.07333L9.00013 8.17489L16.1026 1.07333C16.2093 0.973973 16.3503 0.91988 16.496 0.922451C16.6417 0.925022 16.7808 0.984057 16.8838 1.08712C16.9869 1.19018 17.0459 1.32922 17.0485 1.47494C17.0511 1.62067 16.997 1.7617 16.8976 1.86833Z"
        fill={fillColor}
      />
    </Svg>
  );
};

export default ChevronDown;
