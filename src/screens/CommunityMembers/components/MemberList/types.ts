/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { ListItem } from '@/src/components/UsersList/types';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';

export type MemberListNavigationI = StackNavigationProp<LearnCollabStackParamsListI>;
export type MemberListRouteI = RouteProp<LearnCollabStackParamsListI, 'CommunityMembers'>;

export type MemberListPropsI = {
  forumId: string;
};

export type UseMemberListPropsI = {
  forumId: string;
  pageSize?: number;
};

export type MemberListHookResultI = {
  data: ListItem[];
  loading: boolean;
  refreshing: boolean;
  hasMore: boolean;
  error: Error | null;
  loadMore: () => void;
  refresh: () => void;
};

export type ForumMemberI = {
  id: string;
  Profile: {
    id: string;
    avatar: string | null;
    name: string;
    designation: {
      id: string;
      name: string;
      dataType: 'raw' | 'master';
    } | null;
    entity: {
      id: string;
      name: string;
      dataType: 'raw' | 'master';
    } | null;
  };
  role?: 'MEMBER' | 'MODERATOR' | 'ADMIN';
  joinedAt?: string;
  status?: 'ACTIVE' | 'INACTIVE';
};
