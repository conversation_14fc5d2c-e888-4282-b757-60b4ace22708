/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ForumSearchCategory } from '../SearchBox/types';

export interface RecentSearchesProps {
  setSearchData: (text: string) => void;
  setShowRecent: (flag: boolean) => void;
  setLoading: (flag: boolean) => void;
  setActiveTab: (tab: ForumSearchCategory) => void;
  setLastSearchQuery: (query: string) => void;
  debouncedSearch?: (
    searchText: string,
    category: ForumSearchCategory,
    isLoadMore?: boolean,
  ) => Promise<void>;
  category: string;
}

export interface RecentSearchItem {
  category: string;
  searchText: string;
}
