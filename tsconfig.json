{"compilerOptions": {"baseUrl": "./", "paths": {"@/*": ["./*"]}, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "jsx": "react-jsx", "moduleResolution": "bundler", "resolveJsonModule": true, "strict": true, "target": "esnext", "lib": ["esnext"], "allowJs": true, "noEmit": true, "isolatedModules": true, "allowImportingTsExtensions": false, "verbatimModuleSyntax": false}, "extends": "@react-native/typescript-config/tsconfig.json", "include": ["src/**/*", "**/*.ts", "**/*.tsx", "nativewind-env.d.ts"], "exclude": ["node_modules"]}