/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { Image, Text, View } from 'react-native';
import { twMerge } from 'tailwind-merge';
import { LogoProps } from './types';

const Logo = ({ compact = false, className }: LogoProps) => {
  return (
    <View className={twMerge('flex-row items-center gap-2', className)}>
      <Image
        source={require('@/src/assets/images/brand/logo.png')}
        className="w-8 h-8"
        resizeMode="contain"
      />
      {!compact && (
        <View className="flex-row text-[#244203]">
          <Text className="text-xl tracking-widest font-manrope">NAVI</Text>
          <Text className="text-xl tracking-widest font-manrope-bold font-extrabold">CATER</Text>
        </View>
      )}
    </View>
  );
};

export default Logo;
