import React from 'react';
import { ActivityIndicator, Text, TouchableOpacity, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Accordion from '@/src/components/Accordion';
import NotFound from '@/src/components/NotFound';
import { BottomTabNavigationI } from '@/src/navigation/types';
import { CertificationExpand } from '../ExpandCertification';
import { expiresIn } from '../utils';
import { ValueAddedPropsI } from './types';
import { useProfileValueAdded } from './useHook';

const ProfileValueAdded: React.FC<ValueAddedPropsI> = ({ profileId, isUserProfile }) => {
  const { certifications, count, loading } = useProfileValueAdded(profileId);
  const navigation = useNavigation<BottomTabNavigationI>();

  if (loading) {
    return <ActivityIndicator className="pt-14 pb-6" />;
  }

  return (
    <View>
      {certifications.slice(0, 3).map((certification, index) => (
        <Accordion
          key={certification.id}
          title={certification.certificateCourse.name}
          subTitle={certification.entity.name}
          content={
            <CertificationExpand
              certification={certification}
              expiresIn={expiresIn(certification.untilDate)}
            />
          }
          isLast={index === certifications.length - 1}
        />
      ))}
      {count > 3 && (
        <TouchableOpacity
          onPress={() =>
            navigation.navigate(isUserProfile ? 'ProfileStack' : 'HomeStack', {
              screen: 'EditCertificationList',
              params: {
                editable: false,
                tab: 'value-added',
                profileId: profileId,
              },
            })
          }
        >
          <Text className="text-[#448600] text-base font-medium pt-4">
            {`View all ${count} value added certifications`}
          </Text>
        </TouchableOpacity>
      )}
      {count === 0 && (
        <NotFound
          className="pt-10 pb-4"
          titleClassName="font-normal"
          imageStyle={{ height: 120, width: 120 }}
          fullScreen={false}
        />
      )}
    </View>
  );
};

export default ProfileValueAdded;
