import { useState, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { findAllIndividualChats } from '@/src/networks/chat/individual';
import { fetchProfileAPI } from '@/src/networks/profile/userProfile';
import { ChatListItem, ProfileData } from '../ChatItem/types';
import { ChatsListState } from './types';

export const useChatsListHook = () => {
  const [state, setState] = useState<ChatsListState>({
    chats: [],
    loading: true,
    error: null,
    refreshing: false,
    loadingMore: false,
    hasMore: true,
    currentPage: 0,
    totalChats: 0,
  });
  const currentUser = useSelector(selectCurrentUser);
  const PAGE_SIZE = 10;
  const loadingRef = useRef(false);
  const mountedRef = useRef(true);

  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
    };
  }, []);

  const fetchProfileData = async (profileId: string): Promise<ProfileData> => {
    try {
      const result = (await fetchProfileAPI(profileId)) as ProfileData;
      return result;
    } catch (error) {
      return {
        email: '',
        name: 'Unknown User',
        username: '',
        avatar: null,
        profileId: profileId,
        designation: null,
        entity: null,
      };
    }
  };

  const loadChats = async (isRefresh = true) => {
    if (loadingRef.current && !isRefresh) return;
    if (!mountedRef.current) return;

    try {
      loadingRef.current = true;

      if (isRefresh) {
        setState((prev) => ({ ...prev, refreshing: true, error: null }));
      } else if (state.loadingMore) {
        return;
      } else if (!state.loading) {
        setState((prev) => ({ ...prev, loadingMore: true }));
      }

      const page = isRefresh ? 0 : state.currentPage + 1;

      const result = await findAllIndividualChats({
        page,
        pageSize: PAGE_SIZE,
      });

      if (!mountedRef.current) return;

      const chatsWithProfiles = await Promise.all(
        result.data.map(async (chat) => {
          const profileId =
            chat.senderId === currentUser.profileId ? chat.recieverId : chat.senderId;
          const profile = await fetchProfileData(profileId);
          return {
            ...chat,
            id: { $oid: chat.id.$oid },
            profile,
          } as ChatListItem;
        }),
      );

      if (!mountedRef.current) return;

      setState((prev) => {
        const existingIds = new Set(prev.chats.map((chat) => chat.id.$oid));
        const newChats = chatsWithProfiles.filter((chat) => !existingIds.has(chat.id.$oid));

        return {
          ...prev,
          chats: isRefresh ? chatsWithProfiles : [...prev.chats, ...newChats],
          currentPage: page,
          totalChats: result.total,
          hasMore: result.hasMore,
          loading: false,
          refreshing: false,
          loadingMore: false,
          error: null,
        };
      });
    } catch (err) {
      if (!mountedRef.current) return;

      setState((prev) => ({
        ...prev,
        error: 'Failed to load chats',
        loading: false,
        refreshing: false,
        loadingMore: false,
      }));
    } finally {
      loadingRef.current = false;
    }
  };

  const removeChat = (chatId: string) => {
    setState((prev) => ({
      ...prev,
      chats: prev.chats.filter((chat) => chat.id.$oid !== chatId),
      totalChats: prev.totalChats - 1,
    }));
  };

  const handleRefresh = () => {
    if (!loadingRef.current) {
      setState((prev) => ({ ...prev, currentPage: 0, hasMore: true }));
      loadChats(true);
    }
  };

  const handleLoadMore = () => {
    if (!state.loadingMore && state.hasMore && !loadingRef.current) {
      loadChats(false);
    }
  };

  useEffect(() => {
    loadChats();
  }, []);

  return {
    ...state,
    handleRefresh,
    handleLoadMore,
    removeChat,
  };
};
