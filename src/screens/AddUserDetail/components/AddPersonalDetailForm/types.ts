/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { UseFormReturn } from 'react-hook-form';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';

export type PersonalDetailsFormDataI = {
  fullName: string;
  country?: SearchResultI;
  gender: string;
};

export type UsePersonalDetailsFormI = {
  methods: UseFormReturn<PersonalDetailsFormDataI>;
  isSubmitting: boolean;
  onSubmit: (data: PersonalDetailsFormDataI) => Promise<void>;
};

export type PersonalDetailsPropsI = {
  onNext: (data: PersonalDetailsFormDataI) => void;
  onBack?: () => void;
  initialData?: PersonalDetailsFormDataI;
};
