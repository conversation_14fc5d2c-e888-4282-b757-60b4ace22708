import type { SearchResultI } from '@/src/redux/slices/entitysearch/types';

export type RequestStatusTypeE = 'PENDING' | 'ACCEPTED' | 'REJECTED' | 'REVOKED' | 'DISCONNECTED';

export interface VisitorItem {
  id: string;
  name: string;
  avatar: string | null;
  designation: SearchResultI | null;
  entity: SearchResultI | null;
  isConnected: boolean;
  requestStatus?: RequestStatusTypeE;
}

export interface FetchVisitorsResult {
  total: number;
  data: VisitorItem[];
}

export interface FetchVisitorsParams {
  unLocode: string;
  dataType: 'master' | 'raw';
  page: number;
  pageSize: number;
}

export interface PortVisitorsProps {
  unLocode: string;
  dataType: 'master' | 'raw';
}

export interface UsePortVisitorsParams {
  unLocode: string;
  dataType: 'master' | 'raw';
  pageSize?: number;
  onError?: (error: Error) => void;
}

export interface UsePortVisitorsResult {
  visitors: FetchVisitorsResult;
  loading: boolean;
  loadingMore: boolean;
  hasMore: boolean;
  refreshing: boolean;
  connectionStates: Record<string, 'loading' | 'success' | 'idle'>;
  handleRefresh: () => void;
  handleLoadMore: () => void;
  handleConnectRequest: (profileId: string) => Promise<void>;
  handleAcceptRequest: (profileId: string) => Promise<void>;
  handleRejectRequest: (profileId: string) => Promise<void>;
  handleRevokeRequest: (profileId: string) => Promise<void>;
  handleDisconnectRequest: (profileId: string) => Promise<void>;
}
