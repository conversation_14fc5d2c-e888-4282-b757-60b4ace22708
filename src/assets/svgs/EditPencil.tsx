/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from './types';

const EditPencil: React.FC<OutlinedIconPropsI> = ({
  width = 2.976,
  height = 2.976,
  fill = 'none',
  stroke = 'black',
  color,
  strokeWidth = 1.4,
  disabled,
  accessibilityLabel = 'Edit Pencil',
  ...props
}) => {
  const strokeColor = color || stroke;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 24 24"
      fill={fill}
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        d="M22.0797 5.91411L11.6491 16.3628C10.6104 17.4033 7.5271 17.8852 6.83829 17.1952C6.14948 16.5052 6.61962 13.4166 7.65831 12.3761L18.0999 1.91639C18.3573 1.63498 18.6691 1.40876 19.0163 1.25137C19.3634 1.09398 19.7389 1.00866 20.1199 1.00063C20.5009 0.992597 20.8796 1.06197 21.2331 1.20458C21.5866 1.3472 21.9076 1.56012 22.1766 1.83042C22.4457 2.10073 22.6574 2.42285 22.7988 2.77734C22.9401 3.13184 23.0085 3.51135 22.9994 3.89299C22.9903 4.27463 22.904 4.65051 22.7461 4.99788C22.588 5.34524 22.3613 5.65694 22.0797 5.91411Z"
        stroke={strokeColor}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M10.8402 3.2854H5.37341C4.2135 3.2854 3.10118 3.74696 2.281 4.56857C1.46084 5.39018 1 6.5045 1 7.66643V18.619C1 19.781 1.46084 20.8953 2.281 21.7168C3.10118 22.5385 4.2135 23 5.37341 23H17.4003C19.8166 23 20.6803 21.0286 20.6803 18.619V13.1427"
        stroke={strokeColor}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default EditPencil;
