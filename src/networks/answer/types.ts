import { AttachmentI } from '@/src/screens/ForumAnswers/AnswerInput/types';
import type { ProfileForDataI } from '@/src/networks/question/types';

export type ForumAnswerWithProfileI = {
  id: string;
  cursorId: number;
  text: string;
  isTextTruncated: boolean;
  upvoteCount: number;
  downvoteCount: number;
  commentCount: number;
  profile: ProfileForDataI;
  userVote?: 'UPVOTE' | 'DOWNVOTE' | null;
  canModify: boolean;
  canUpdateStatus: boolean;
  media?: AttachmentI[];
};

export type ForumAnswersResponseI = {
  data: ForumAnswerWithProfileI[];
  total: number;
  nextCursorId: number | null;
};

export type ForumAnswerDetailI = {
  id: string;
  canModify: boolean;
  cursorId: number;
  text: string;
  upvoteCount: number;
  downvoteCount: number;
  commentCount: number;
  questionId: string;
  communityId: string;
  profileId: string;
  status: string;
  isEdited: boolean;
  createdAt: string;
  profile: ProfileForDataI;
};

export type ForumAnswerCreateOnePayloadI = {
  text: string;
  questionId: string;
  files?: AttachmentI[];
};

export type ForumAnswerCreateOneResponseI = {
  id: string;
  cursorId: number;
};

export type ForumAnswerDeleteOnePayloadI = {
  answerId: string;
};

export enum AnswerStatusE {
  PROPOSED_SOLUTION = 'PROPOSED_SOLUTION',
  VERIFIED_SOLUTION = 'VERIFIED_SOLUTION',
}

export type AnswerUpdateStatusPayloadI = {
  id: string;
  status: AnswerStatusE;
};
