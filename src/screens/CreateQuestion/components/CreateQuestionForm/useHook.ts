import { useCallback, useRef, useState } from 'react';
import type { TextInput } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import { zodResolver } from '@hookform/resolvers/zod';
import { pick, types } from '@react-native-documents/picker';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { selectQuestionFormData } from '@/src/redux/selectors/question';
import { selectMultipleSelectionsByKey } from '@/src/redux/selectors/search';
import {
  createQuestion,
  updateFormData,
  setFiles,
  resetAllQuestionStates,
} from '@/src/redux/slices/question/questionSlice';
import type { QuestionMediaCreateItemI } from '@/src/redux/slices/question/types';
import { AppDispatch } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import { uploadFileWithPresignedUrl } from '@/src/utilities/upload/upload';
import { validateAskQuestionForm } from '@/src/screens/CommunityQuestion/components/AskQuestionForm';
import { fetchPresignedUrlAPI } from '@/src/networks/storage/presignedUrl';
import { createQuestionSchema, type QuestionFormData } from './schema';
import type { CreateQuestionFormProps, MediaI } from './types';

const useCreateQuestionForm = ({ onSuccess }: CreateQuestionFormProps) => {
  const dispatch = useDispatch<AppDispatch>();
  const questionFormData = useSelector(selectQuestionFormData);
  const [isLoading, setIsLoading] = useState(false);
  const [attachments, setAttachments] = useState<MediaI[]>([]);
  const titleInputRef = useRef<TextInput>(null);
  const descriptionInputRef = useRef<TextInput>(null);

  const topicsSelection = useSelector(selectMultipleSelectionsByKey('topic'));

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
    watch,
    reset,
  } = useForm<QuestionFormData>({
    resolver: zodResolver(createQuestionSchema),
    defaultValues: {
      title: questionFormData.title,
      description: questionFormData.description,
    },
    mode: 'onChange',
  });

  useFocusEffect(
    useCallback(() => {
      setValue('title', questionFormData.title);
      setValue('description', questionFormData.description);

      if (questionFormData.files.length > 0) {
        const formattedAttachments: MediaI[] = questionFormData.files.map((file) => ({
          uri: file.fileUrl,
          type: file.fileExtension,
          filename: file.fileUrl.split('/').pop() || 'file',
        }));
        setAttachments(formattedAttachments);
      }
    }, [questionFormData, setValue]),
  );

  const watchedValues = watch();

  const handleAttachments = async () => {
    try {
      setIsLoading(true);
      const remainingSlots = 5 - attachments.length;
      if (remainingSlots <= 0) {
        showToast({
          message: 'Limit reached',
          description: 'You can only upload up to 5 files.',
          type: 'info',
        });
        return;
      }

      const files = await pick({
        allowMultiSelection: true,
        type: [types.images, types.pdf, types.plainText],
      });

      if (!files || files.length === 0) return;

      const limitedFiles = files.slice(0, remainingSlots);
      const allowedMimeTypes = [
        'image/jpeg',
        'image/png',
        'image/jpg',
        'application/pdf',
        'text/plain',
      ];

      const MAX_FILE_SIZE = 4 * 1024 * 1024; // 4MB in bytes

      const validFiles = limitedFiles.filter((file) => {
        const mimeType = file.type ?? '';
        const fileSize = file.size ?? 0;

        if (!allowedMimeTypes.includes(mimeType)) {
          return false;
        }

        if (fileSize > MAX_FILE_SIZE) {
          return false;
        }

        return true;
      });

      // Check for file type issues
      const invalidTypeFiles = limitedFiles.filter((file) => {
        const mimeType = file.type ?? '';
        return !allowedMimeTypes.includes(mimeType);
      });

      // Check for file size issues
      const oversizedFiles = limitedFiles.filter((file) => {
        const mimeType = file.type ?? '';
        const fileSize = file.size ?? 0;
        return allowedMimeTypes.includes(mimeType) && fileSize > MAX_FILE_SIZE;
      });

      if (oversizedFiles.length > 0) {
        showToast({
          message: 'File too large',
          description: 'Files must be 4MB or smaller.',
          type: 'error',
        });
        return;
      }

      if (invalidTypeFiles.length > 0) {
        showToast({
          message: 'Some files skipped',
          description: 'Only images, PDFs, and text files are allowed.',
          type: 'info',
        });
      }

      const newAttachments: MediaI[] = validFiles.map((file) => ({
        uri: file.uri,
        type: file.type ?? 'application/octet-stream',
        filename: file.name ?? 'Unknown file',
      }));

      setAttachments((prev) => [...prev, ...newAttachments]);
    } catch (error) {
      if (
        error &&
        typeof error === 'object' &&
        'code' in error &&
        error.code !== 'DOCUMENT_PICKER_CANCELED'
      ) {
        showToast({
          message: 'Error',
          description: 'Failed to select files',
          type: 'error',
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteAttachment = (index: number) => {
    setAttachments((prev) => prev.filter((_, i) => i !== index));
  };

  const uploadAttachments = async (attachments: MediaI[]) => {
    if (attachments.length === 0) return [];

    try {
      const extensions = attachments.map((file) => {
        const extension = file.filename.split('.').pop()?.toLowerCase() || 'unknown';
        return extension;
      });
      const response = await fetchPresignedUrlAPI(extensions, 'FORUM');

      if (!response) {
        throw new Error('Failed to get upload URLs: Empty response');
      }

      if (!Array.isArray(response)) {
        throw new Error('Failed to get upload URLs: Response is not an array');
      }

      if (response.length !== attachments.length) {
        throw new Error(
          `Failed to get upload URLs: Got ${response.length} URLs for ${attachments.length} files`,
        );
      }

      const uploadPromises = attachments.map((file, index) => {
        const presignedData = response[index];
        return uploadFileWithPresignedUrl(file, presignedData.uploadUrl)
          .then((result) => {
            return result;
          })
          .catch((error) => {
            throw error;
          });
      });
      try {
        const uploadResults = [];
        for (let i = 0; i < attachments.length; i++) {
          const file = attachments[i];
          const presignedData = response[i];
          try {
            await uploadFileWithPresignedUrl(file, presignedData.uploadUrl);
            uploadResults.push({
              fileUrl: presignedData.accessUrl,
              filename: file.filename,
              mimeType: file.type,
            });
          } catch (uploadError) {
            showToast({
              message: 'Upload Issue',
              description: `Failed to upload ${file.filename}: ${
                uploadError && typeof uploadError === 'object' && 'message' in uploadError
                  ? (uploadError as { message: string }).message
                  : String(uploadError)
              }`,
              type: 'error',
            });
          }
        }
        return uploadResults;
      } catch (allError) {
        throw new Error(
          `Upload process failed: ${
            allError && typeof allError === 'object' && 'message' in allError
              ? (allError as { message: string }).message
              : String(allError)
          }`,
        );
      }
      return response.map((item, index) => {
        const result = {
          fileUrl: item.accessUrl,
          filename: attachments[index].filename,
          mimeType: attachments[index].type,
        };
        return result;
      });
    } catch (error) {
      if (error instanceof Error) {
      } else {
      }
      throw error;
    }
  };

  const onSubmit = async (data: QuestionFormData) => {
    try {
      setIsLoading(true);
      dispatch(
        updateFormData({
          title: data.title,
          description: data.description,
          communityId: 'dda5ebc8-fe88-46ab-a847-139558c3a73b',
          topics: topicsSelection,
        }),
      );

      const currentState = {
        question: {
          formData: {
            ...questionFormData,
            title: data.title,
            description: data.description,
            topics: topicsSelection,
          },
        },
        search: {
          multipleSelections: { topic: questionFormData.topics },
          selections: {
            department: questionFormData.department,
            equipmentCategory: questionFormData.equipmentCategory,
            equipmentManufacturer: questionFormData.equipmentManufacturer,
            equipmentModel: questionFormData.equipmentModel,
          },
        },
      };

      const validation = validateAskQuestionForm(currentState);

      if (!validation.isValid) {
        showToast({
          type: 'error',
          message: 'Validation Error',
          description: validation.message,
        });
        return;
      }

      let uploadedFiles: QuestionMediaCreateItemI[] = [];
      if (attachments.length > 0) {
        try {
          const uploadedAttachments = await uploadAttachments(attachments);

          uploadedFiles = uploadedAttachments.map((attachment) => {
            const fileExt = attachment.filename.split('.').pop()?.toLowerCase() || 'unknown';
            return {
              fileUrl: attachment.fileUrl,
              fileExtension: fileExt,
            };
          });

          dispatch(setFiles(uploadedFiles));
        } catch (uploadError) {
          throw new Error(
            `File upload failed: ${uploadError instanceof Error ? uploadError.message : 'Unknown error'}`,
          );
        }
      } else {
      }

      const result = await dispatch(createQuestion());

      if (createQuestion.fulfilled.match(result)) {
        showToast({
          message: 'Success',
          description: 'Question created successfully',
          type: 'success',
        });
        await dispatch(resetAllQuestionStates());
        onSuccess();
        resetForm();
      } else {
        throw new Error(result.payload as string);
      }
    } catch (error) {
      if (error instanceof Error) {
        showToast({
          message: 'Error',
          description: error.message,
          type: 'error',
        });
      }
      showToast({
        message: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create question',
        type: 'error',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    reset();
    setAttachments([]);
  };

  const focusTitleInput = () => {
    titleInputRef.current?.focus();
  };

  const isFormValid = isValid && watchedValues.title.trim().length > 0;

  return {
    control,
    errors,
    isSubmitting: isLoading,
    isLoading,
    attachments,
    titleInputRef,
    descriptionInputRef,
    handleSubmit: handleSubmit(onSubmit),
    handleAttachments,
    handleDeleteAttachment,
    focusTitleInput,
    isFormValid,
  };
};

export default useCreateQuestionForm;
