/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { FetchProfileResultI } from '@/src/networks/profile/types';
import { AboutProfileI } from '../ProfileContent/types';

export interface ProfileHeaderProps {
  data: FetchProfileResultI;
  isUserProfile: boolean;
  aboutProfile: AboutProfileI | null;
  setFollowing: React.Dispatch<React.SetStateAction<boolean>>;
}
