/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ObjUnknownI } from '@/src/types/common/data';
import { GenerateURLI } from './types';

export const queryParamsToStr = (data: ObjUnknownI): string => {
  const filteredEntries = Object.entries(data).filter(([_, value]) => value !== undefined);
  if (filteredEntries.length === 0) {
    return '';
  }
  return (
    '?' +
    filteredEntries
      .map(([key, valueP]) => {
        const value = Array.isArray(valueP)
          ? (valueP as string[]).join(',').slice(0, -1)
          : String(valueP);
        return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
      })
      .join('&')
  );
};

export const generateUrl = (args: GenerateURLI): string => {
  let url = `${args.baseUrl}${args.path}`;

  if (args?.routeId !== undefined) {
    url += `/${args.routeId}`;
  }

  if (args?.query && Object.keys(args.query).length) {
    const queryString = queryParamsToStr(args.query);
    if (queryString) {
      url += queryString;
    }
  }

  return url;
};
