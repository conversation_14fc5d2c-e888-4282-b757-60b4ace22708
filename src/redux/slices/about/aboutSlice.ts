import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import { AboutState } from './types';

const initialState: AboutState = {
  description: null,
  educationCount: 0,
  statutoryCount: 0,
  valueAddedCount: 0,
  identityCount: 0,
  visaCount: 0,
  maritimeSkillsCount: 0,
  mutualCount: 0,
  otherSkillsCount: 0,
  profileEducations: [],
  profileStatutoryCertifications: [],
  profileIdentityDocuments: [],
  profileMaritimeSkills: [],
};

const aboutSlice = createSlice({
  name: 'about',
  initialState,
  reducers: {
    setAboutData: (state, action) => {
      const {
        educationCount,
        statutoryCertCount,
        valueAddedCertCount,
        identityCount,
        visaCount,
        maritimeSkillsTotal,
        otherSkillsTotal,
        mutualCount,
      } = action.payload.meta;

      Object.assign(state, {
        educationCount,
        statutoryCount: statutoryCertCount,
        valueAddedCount: valueAddedCertCount,
        identityCount,
        visaCount,
        mutualCount,
        maritimeSkillsCount: maritimeSkillsTotal,
        otherSkillsCount: otherSkillsTotal,
        profileEducations: action.payload.institutions,
        profileStatutoryCertifications: action.payload.statutoryCerts,
        profileIdentityDocuments: action.payload.identities,
        profileMaritimeSkills: action.payload.maritimeSkills,
        description: action.payload.description,
      });
    },

    updateDescription: (state, action: PayloadAction<string>) => {
      state.description = action.payload;
    },
  },
});

export const { setAboutData, updateDescription } = aboutSlice.actions;

export default aboutSlice.reducer;
