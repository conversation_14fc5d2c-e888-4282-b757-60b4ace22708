import type { ViewStyle } from 'react-native';

export type AttachmentI = {
  uri?: string;
  type?: string;
  filename?: string;
  fileUrl: string;
  fileExtension: string;
};

export type AnswerInputPropsI = {
  onSubmit: (text: string, files: AttachmentI[]) => void;
  style?: ViewStyle;
};

export type AttachmentPreviewPropsI = {
  attachments: any[];
  onRemove: (index: number) => void;
};

export type MediaI = {
  uri: string;
  type: string;
  filename: string;
};
