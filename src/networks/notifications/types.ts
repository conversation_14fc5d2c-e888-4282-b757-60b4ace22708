export type NotificationType =
  | 'LIKE'
  | 'COMMENT'
  | 'REPLY'
  | 'FOLLOWER'
  | 'REQUEST_RECEIVED'
  | 'REQUEST_ACCEPTED';

export interface NotificationFetchManyItemI {
  actorProfileId: string;
  postId?: string;
}

export type NotificationFetchManyI = NotificationFetchManyItemI[];

export interface ProfileForNotificationI {
  id: string;
  avatar: string | null;
  name: string;
}

export interface PostForNotificationI {
  id: string;
  caption: string;
  image?: string;
}

export interface NotificationFetchManyResultI {
  profiles: ProfileForNotificationI[] | null;
  posts: PostForNotificationI[] | null;
}

export interface NotificationData {
  actorProfileId: string;
  postId?: string;
  commentId?: string;
  parentCommentId?: string;
}

export interface NotificationFetchManyQueryI {
  id: string;
  type: NotificationType;
  data: NotificationData;
  isRead: boolean;
  createdAt: string;
}

export type NotificationFetchManyResult = {
  notifications: NotificationFetchManyQueryI[];
  nextCursor: string | null;
};

export interface NotificationUpdateReadI {
  profileId: string;
  type: 'UPDATE_READ';
  ids: string[];
}

export interface NotificationUpdateReadResult {
  success: boolean;
  updatedCount: number;
}
