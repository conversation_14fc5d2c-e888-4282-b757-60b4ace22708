/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
export const VARIANTS = {
  success: {
    container: 'bg-[#448600]',
    label: 'text-white',
    spinnerColor: '#FFFFFF',
  },
  error: {
    container: 'bg-[#DC2626]',
    label: 'text-white',
    spinnerColor: '#FFFFFF',
  },
  info: {
    container: 'bg-[#2563EB]',
    label: 'text-white',
    spinnerColor: '#FFFFFF',
  },
};
