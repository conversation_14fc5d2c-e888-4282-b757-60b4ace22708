import { Dispatch, SetStateAction } from 'react';
import { StackNavigationProp } from '@react-navigation/stack';
import { UseFormReturn } from 'react-hook-form';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { IdTypeI } from '@/src/types/common/data';
import { HomeStackParamListI } from '@/src/navigation/types';
import { ShipCreateEditPayloadI } from '@/src/screens/EditShipItem/components/EditShipItem/types';

export interface EditEquipmentItemPropsI {
  onBack: () => void;
  profileId: string;
  experienceId?: string;
  shipId: string;
  equipmentId: string;
  preFilledData: ShipCreateEditPayloadI[];
  refetch: () => void;
}

export interface UseEditEquipmentItemI {
  methods: UseFormReturn<EquipmentDetailsFormDataI>;
  isSubmitting: boolean;
  onSubmit: (data: EquipmentDetailsFormDataI) => Promise<void>;
  navigation: StackNavigationProp<HomeStackParamListI>;
  handleBack?: () => void;
  localFuelTypes: SearchResultI[];
  setLocalFuelTypes: Dispatch<SetStateAction<SearchResultI[]>>;
  clearFields: () => void;
  isSubmitted: boolean;
  setIsSubmitted: Dispatch<SetStateAction<boolean>>;
  loading: boolean;
  hasChanges: boolean;
}

export type EquipmentDetailsFormDataI = {
  category: SearchResultI & { hasFuelType: boolean };
  manufacturerName: string;
  model: string;
  power: string;
  fuelType: SearchResultI[];
  additionalDetails?: string;
};

export type EquipmentPayloadI = {
  id?: string;
  opr?: 'CREATE' | 'UPDATE' | 'DELETE' | 'NESTED_OPR';
  equipmentCategory?: {
    id: string;
    dataType: string;
  };
  manufacturerName?: string;
  model?: string;
  powerCapacity?: number;
  details?: string;
  fuelTypes?: (
    | { opr: string; fuelType: { id: string; dataType: string } }
    | { id: string; opr: string }
  )[];
};

export type EquipmentCreateUpdatePayloadI = {
  opr: 'UPDATE' | 'CREATE' | 'DELETE' | 'NESTED_OPR';
  entity?: SearchResultI;
  id?: string;
  designations: {
    designation?: SearchResultI;
    fromDate?: string;
    id?: string;
    opr?: string;
    ships?: {
      opr?: 'UPDATE' | 'CREATE' | 'DELETE' | 'NESTED_OPR';
      ship?: {
        imo: string;
        dataType: string;
      };
      name?: string;
      sizeGt?: number;
      powerKw?: number;
      fromDate?: string;
      toDate?: string | null;
      details?: string;
      department?: IdTypeI;
      subVesselType?: IdTypeI;
      dwt?: number;
      id?: string;
      skills?: {
        opr: string;
        id: string;
        dataType: 'raw' | 'master';
      }[];
      equipmentCategories: {
        id?: string;
        opr?: 'CREATE' | 'UPDATE' | 'DELETE' | 'NESTED_OPR';
        equipmentCategory?: {
          id: string;
          dataType: string;
        };
        manufacturerName?: string;
        model?: string;
        powerCapacity?: number;
        details?: string;
        fuelTypes?: (
          | { opr: string; fuelType: { id: string; dataType: string } }
          | { id: string; opr: string }
        )[];
      }[];
    };
    toDate?: string;
  }[];
};
