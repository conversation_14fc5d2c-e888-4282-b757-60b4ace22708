/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { apiCall } from '@/src/services/api';
import {
  BlockProfileParamsI,
  BlockProfileResponseI,
  FetchBlockedProfilesParamsI,
  FetchBlockedProfilesResponseI,
  UnblockProfileParamsI,
} from './types';

export const blockProfileAPI = async (
  payload: BlockProfileParamsI,
): Promise<BlockProfileResponseI> => {
  const result = await apiCall<BlockProfileParamsI, BlockProfileResponseI>(
    '/backend/api/v1/network/block',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};

export const unblockProfileAPI = async (payload: UnblockProfileParamsI): Promise<unknown> => {
  const result = await apiCall<UnblockProfileParamsI, unknown>(
    '/backend/api/v1/network/unblock',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );

  return result;
};

export const fetchBlockedProfilesAPI = async ({
  page = 0,
  pageSize = 10,
}: FetchBlockedProfilesParamsI = {}): Promise<FetchBlockedProfilesResponseI> => {
  const query = { page, pageSize } as FetchBlockedProfilesParamsI;

  const result = await apiCall<FetchBlockedProfilesParamsI, FetchBlockedProfilesResponseI>(
    '/backend/api/v1/network/blocked-profiles',
    'GET',
    {
      isAuth: true,
      query,
    },
  );

  return result;
};
