import { apiCall } from '@/src/services/api';
import type {
  ForumAnswerDetailI,
  ForumAnswerCreateOnePayloadI,
  ForumAnswerCreateOneResponseI,
  ForumAnswerDeleteOnePayloadI,
  ForumAnswersResponseI,
  AnswerUpdateStatusPayloadI,
} from './types';

export const updateForumAnswerStatusAPI = async (
  payload: AnswerUpdateStatusPayloadI,
): Promise<void> => {
  apiCall<AnswerUpdateStatusPayloadI, void>('/backend/api/v1/forum/answer-status', 'PATCH', {
    isAuth: true,
    payload,
  });
};

export const fetchForumAnswersAPI = async (
  questionId: string,
  cursorId?: number | null,
  pageSize: number = 10,
) => {
  return apiCall<
    { questionId: string; cursorId?: number; pageSize: number },
    ForumAnswersResponseI
  >('/backend/api/v1/forum/answers', 'GET', {
    isAuth: true,
    query: { questionId, cursorId, pageSize },
  });
};

export const fetchForumAnswerDetailAPI = async (id: string) => {
  return apiCall<unknown, ForumAnswerDetailI>('/backend/api/v1/forum/answer/text', 'GET', {
    isAuth: true,
    query: { id },
  });
};

export const createForumAnswerAPI = async (
  payload: ForumAnswerCreateOnePayloadI,
): Promise<ForumAnswerCreateOneResponseI> => {
  return apiCall<ForumAnswerCreateOnePayloadI, ForumAnswerCreateOneResponseI>(
    '/backend/api/v1/forum/answer',
    'POST',
    {
      isAuth: true,
      payload,
    },
  );
};

export const deleteForumAnswerAPI = async (
  payload: ForumAnswerDeleteOnePayloadI,
): Promise<void> => {
  return apiCall<ForumAnswerDeleteOnePayloadI, void>(
    `/backend/api/v1/forum/answer/${payload.answerId}`,
    'DELETE',
    {
      isAuth: true,
    },
  );
};
