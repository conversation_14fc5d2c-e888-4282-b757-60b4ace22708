import { ReactNode } from 'react';

export type VariantConfig = {
  container: string;
  containerStyle?: Record<string, any>;
  disabledContainer?: string;
  disabledContainerStyle?: Record<string, any>;
  label: string | Record<string, any>;
  spinnerColor: string;
};

export type VariantsType = Record<string, VariantConfig>;

export type ButtonVariant =
  | 'primary'
  | 'secondary'
  | 'tertiary'
  | 'outline'
  | 'primaryOutline'
  | 'oauth';

export interface ButtonProps {
  onPress: () => void;
  variant?: ButtonVariant;
  label: string;
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  labelClassName?: string;
  prefixIcon?: ReactNode;
  suffixIcon?: ReactNode;
}
