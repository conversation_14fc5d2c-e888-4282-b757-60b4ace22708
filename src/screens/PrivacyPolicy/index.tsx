import { View, Text, ScrollView, ActivityIndicator, StyleSheet } from 'react-native';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Markdown from 'react-native-markdown-display';
import BackButton from '@/src/components/BackButton';
import Button from '@/src/components/Button';
import SafeArea from '@/src/components/SafeArea';
import { AppStackParamListI, AuthStackParamListI } from '@/src/navigation/types';
import { usePrivacyPolicy } from './useHook';

const PrivacyPolicyScreen = () => {
  const navigation = useNavigation<NativeStackNavigationProp<AppStackParamListI>>();
  const { policyContent, loading } = usePrivacyPolicy();

  const handleBack = () => {
    navigation.goBack();
  };

  const handleViewTerms = () => {
    navigation.navigate('Terms');
  };

  if (loading) {
    return (
      <SafeArea>
        <View className="flex-1 justify-center items-center bg-white">
          <ActivityIndicator size="small" color="#448600" />
        </View>
      </SafeArea>
    );
  }

  const cleanContent = policyContent
    ?.replace(/\\\n/g, '\n')
    .replace(/\\\"/g, '"')
    .replace(/\\\'/g, "'");

  const markdownStyles = StyleSheet.create({
    body: {
      fontSize: 16,
      lineHeight: 24,
      color: '#374151',
    },
    heading1: {
      fontSize: 24,
      fontWeight: 'bold',
      color: '#111827',
      marginBottom: 16,
      marginTop: 24,
    },
    heading2: {
      fontSize: 20,
      fontWeight: 'bold',
      color: '#111827',
      marginBottom: 12,
      marginTop: 20,
    },
    heading3: {
      fontSize: 18,
      fontWeight: '600',
      color: '#111827',
      marginBottom: 8,
      marginTop: 16,
    },
    paragraph: {
      marginBottom: 12,
      lineHeight: 22,
    },
    list_item: {
      marginBottom: 8,
    },
    bullet_list: {
      marginBottom: 16,
    },
    ordered_list: {
      marginBottom: 16,
    },
    strong: {
      fontWeight: 'bold',
    },
    em: {
      fontStyle: 'italic',
    },
    link: {
      color: '#004687',
      textDecorationLine: 'underline',
    },
  });

  return (
    <SafeArea>
      <View className="flex-1 bg-white">
        <View className="px-4 py-2">
          <BackButton onBack={handleBack} label="" />
        </View>

        <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
          <View className="px-6 pb-8">
            <Text className="text-3xl font-bold text-gray-900 mb-6">Privacy Policy</Text>

            {cleanContent && <Markdown style={markdownStyles}>{cleanContent}</Markdown>}

            <Button
              label="View Terms of Service"
              onPress={handleViewTerms}
              variant="secondary"
              className="mt-6 mb-4"
            />
          </View>
        </ScrollView>
      </View>
    </SafeArea>
  );
};

export default PrivacyPolicyScreen;
