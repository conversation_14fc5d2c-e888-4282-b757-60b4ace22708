import { Platform, PermissionsAndroid } from 'react-native';
import { getApp } from '@react-native-firebase/app';
import {
  getMessaging,
  requestPermission,
  AuthorizationStatus,
} from '@react-native-firebase/messaging';
import FirebaseService from '@/src/services/firebase';

export const requestNotificationPermission = async (): Promise<boolean> => {
  try {
    await FirebaseService.getInstance();

    if (Platform.OS === 'ios') {
      const app = getApp();
      const messaging = getMessaging(app);

      const authStatus = await requestPermission(messaging);
      return (
        authStatus === AuthorizationStatus.AUTHORIZED ||
        authStatus === AuthorizationStatus.PROVISIONAL
      );
    } else {
      if (Number(Platform.Version) >= 33) {
        const hasPermission = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
        );
        if (hasPermission) return true;
        const permissionStatus = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
        );
        return permissionStatus === PermissionsAndroid.RESULTS.GRANTED;
      }

      return true;
    }
  } catch (error) {
    return false;
  }
};

export const requestPermissions = async (): Promise<boolean> => {
  return await requestNotificationPermission();
};
