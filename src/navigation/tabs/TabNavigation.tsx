import { useRef, useEffect } from 'react';
import { Pressable, View, Animated } from 'react-native';
import {
  createBottomTabNavigator,
  BottomTabBarProps,
  BottomTabBarButtonProps,
} from '@react-navigation/bottom-tabs';
import { getFocusedRouteNameFromRoute } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import UserAvatar from '@/src/components/UserAvatar';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import CreateStackNavigator from '@/src/navigation/stacks/CreateStack';
import HomeStackNavigator from '@/src/navigation/stacks/HomeStack';
import NotificationStackNavigator from '@/src/navigation/stacks/NotificationStack';
import ProfileStackNavigator from '@/src/navigation/stacks/ProfileStack';
import AnalysisIcon from '@/src/assets/svgs/AnalysisTab';
import HomeIcon from '@/src/assets/svgs/HomeTab';
import LearnAndCollabIcon from '@/src/assets/svgs/LearnCollabTab';
import NotificationIcon from '@/src/assets/svgs/NotificationTab';
import LearnCollabStackNavigator from '../stacks/LearnCollabStack';
import type { HomeScreenActionsRef } from '../types';

const Tab = createBottomTabNavigator();

const TabButton = (props: BottomTabBarButtonProps) => {
  const scale = useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    Animated.spring(scale, { toValue: 0.95, useNativeDriver: true }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scale, { toValue: 1, useNativeDriver: true }).start();
  };

  return (
    <Pressable
      onPress={props.onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      style={props.style}
      accessibilityRole={props.accessibilityRole}
    >
      <Animated.View style={{ transform: [{ scale }] }}>{props.children}</Animated.View>
    </Pressable>
  );
};

const CustomTabBar = ({ state, descriptors, navigation }: BottomTabBarProps) => {
  const translateY = useRef(new Animated.Value(0)).current;

  const currentRoute = state.routes[state.index];
  const currentOptions = descriptors[currentRoute.key]?.options;
  const shouldHide =
    currentOptions?.tabBarStyle &&
    typeof currentOptions.tabBarStyle === 'object' &&
    'display' in currentOptions.tabBarStyle &&
    currentOptions.tabBarStyle.display === 'none';

  useEffect(() => {
    Animated.timing(translateY, {
      toValue: shouldHide ? 100 : 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  }, [shouldHide, translateY]);

  return (
    <Animated.View
      style={{
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        backgroundColor: '#ffffff',
        height: 80,
        flexDirection: 'row',
        paddingHorizontal: 20,
        paddingBottom: 25,
        paddingTop: 15,
        borderTopWidth: 1,
        borderTopColor: '#f0f0f0',
        transform: [{ translateY }],
        borderRadius: 20,
      }}
    >
      {state.routes.map((route, index) => {
        const { options } = descriptors[route.key];
        const isFocused = state.index === index;

        const onPress = () => {
          const event = navigation.emit({
            type: 'tabPress',
            target: route.key,
            canPreventDefault: true,
          });

          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name, route.params);
          }
        };

        return (
          <View key={route.key} style={{ flex: 1, alignItems: 'center' }}>
            <TabButton
              onPress={onPress}
              style={{ alignItems: 'center', justifyContent: 'center' }}
              accessibilityRole="button"
            >
              {options.tabBarIcon?.({ focused: isFocused, color: '', size: 0 })}
            </TabButton>
          </View>
        );
      })}
    </Animated.View>
  );
};

const TabIcon = ({
  IconComponent,
  focused,
}: {
  IconComponent: React.ComponentType<{ fill: string }>;
  focused: boolean;
}) => (
  <View
    style={{
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 20,
      backgroundColor: focused ? '#f0f8f0' : 'transparent',
      overflow: 'hidden',
    }}
  >
    <IconComponent fill={focused ? '#006400' : '#888888'} />
  </View>
);

const CreateButton = ({ focused }: { focused: boolean }) => (
  <View style={{ alignItems: 'center', justifyContent: 'center', marginTop: -10 }}>
    <View
      style={{
        width: 50,
        height: 50,
        borderRadius: 25,
        backgroundColor: focused ? '#006400' : '#f0f8f0',
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 2,
        borderColor: '#006400',
      }}
    >
      <AnalysisIcon fill={focused ? '#ffffff' : '#006400'} />
    </View>
  </View>
);

const ProfileIcon = ({ focused }: { focused: boolean }) => {
  const currentUser = useSelector(selectCurrentUser);

  return (
    <View
      style={{
        borderRadius: 20,
        borderWidth: focused ? 2 : 0,
        borderColor: '#006400',
        padding: 2,
        overflow: 'hidden',
      }}
    >
      <UserAvatar
        avatarUri={currentUser?.avatar}
        name={currentUser?.fullName}
        width={32}
        height={32}
      />
    </View>
  );
};

const hiddenScreens = {
  home: [
    'Chat',
    'Chats',
    'AIChat',
    'GlobalSearch',
    'Comment',
    'OtherUserProfile',
    'PortProfile',
    'ShipProfile',
    'EditDocumentList',
    'EditCertificationList',
    'EditEducationList',
    'EditSkillsList',
    'EditExperienceList',
    'Connection',
    'Likes',
    'EditShipItem',
    'PortsVisited',
  ],
  create: ['CreateContent'],
  learnCollab: ['ForumAnswers', 'CreateQuestion', 'ForumComments', 'SearchScreen'],
  profile: [
    'BlockedUserProfiles',
    'Terms',
    'Privacy',
    'Comment',
    'Connection',
    'Chat',
    'Chats',
    'EditCargoItem',
    'EditCertificationItem',
    'EditDocumentItem',
    'EditDocumentList',
    'EditCertificationList',
    'EditEducationList',
    'EditSkillsList',
    'EditExperienceList',
    'EditEducationItem',
    'EditEquipmentItem',
    'EditExperienceItem',
    'EditUserProfile',
    'Likes',
    'OtherUserProfile',
    'PortsVisited',
    'SearchScreen',
    'EditDetail',
    'UserProfile',
    'EditShipItem',
    'ShipProfile',
    'UserSettings',
  ],
};

const getTabOptions = (route: any, screens: string[]) => {
  const routeName = getFocusedRouteNameFromRoute(route) ?? 'Home';
  return screens.includes(routeName)
    ? { tabBarStyle: { display: 'none' as const } }
    : { borderRadius: 20 };
};

const BottomTabNavigator = () => {
  const homeScreenActionsRef = useRef<HomeScreenActionsRef['current']>({});

  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarShowLabel: false,
        tabBarButton: TabButton,
      }}
      tabBar={(props) => <CustomTabBar {...props} />}
    >
      <Tab.Screen
        name="HomeStack"
        options={({ route }) => ({
          tabBarIcon: ({ focused }: { focused: boolean }) => (
            <TabIcon IconComponent={HomeIcon} focused={focused} />
          ),
          ...getTabOptions(route, hiddenScreens.home),
        })}
        listeners={{
          tabPress: () => {
            homeScreenActionsRef.current.scrollToTop?.();
            homeScreenActionsRef.current.handleRefresh?.();
          },
        }}
      >
        {() => <HomeStackNavigator homeScreenActionsRef={homeScreenActionsRef} />}
      </Tab.Screen>

      <Tab.Screen
        name="LearnCollabStack"
        component={LearnCollabStackNavigator}
        options={({ route }) => ({
          tabBarIcon: ({ focused }: { focused: boolean }) => (
            <TabIcon IconComponent={LearnAndCollabIcon} focused={focused} />
          ),
          ...getTabOptions(route, hiddenScreens.learnCollab),
        })}
        listeners={({ navigation }) => ({
          tabPress: (e) => {
            e.preventDefault();
            navigation.navigate('LearnCollabStack', {
              screen: 'LearnCollab',
              params: {},
            });
          },
        })}
      />

      <Tab.Screen
        name="CreateStack"
        component={CreateStackNavigator}
        options={({ route }) => ({
          tabBarIcon: ({ focused }: { focused: boolean }) => <CreateButton focused={focused} />,
          ...getTabOptions(route, hiddenScreens.create),
        })}
        listeners={({ navigation }) => ({
          tabPress: (e) => {
            e.preventDefault();
            navigation.navigate('CreateStack', {
              screen: 'CreateContent',
              params: {
                type: 'USER_POST',
                editing: undefined,
                portUnLocode: undefined,
                postId: undefined,
              },
            });
          },
        })}
      />

      <Tab.Screen
        name="NotificationStack"
        component={NotificationStackNavigator}
        options={({ route }) => ({
          tabBarIcon: ({ focused }: { focused: boolean }) => (
            <TabIcon IconComponent={NotificationIcon} focused={focused} />
          ),
          ...getTabOptions(route, []),
        })}
      />

      <Tab.Screen
        name="ProfileStack"
        component={ProfileStackNavigator}
        options={({ route }) => ({
          tabBarIcon: ({ focused }: { focused: boolean }) => <ProfileIcon focused={focused} />,
          ...getTabOptions(route, hiddenScreens.profile),
        })}
        listeners={({ navigation }) => ({
          tabPress: (e) => {
            e.preventDefault();
            navigation.reset({
              index: 0,
              routes: [
                {
                  name: 'ProfileStack',
                  params: {
                    screen: 'UserProfile',
                    params: { fromTabPress: true, profileId: undefined },
                  },
                },
              ],
            });
          },
        })}
      />
    </Tab.Navigator>
  );
};

export default BottomTabNavigator;
