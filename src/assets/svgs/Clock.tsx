/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const Clock: React.FC<FilledIconPropsI> = ({
  width = 2.976,
  height = 3.1,
  fill = '#000',
  color,
  disabled,
  accessibilityLabel = 'Clock',
  ...props
}) => {
  const fillColor = color || fill;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 24 25"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        d="M12 4.47a8 8 0 108 8 8.01 8.01 0 00-8-8zm0 15.06a7.06 7.06 0 117.059-7.06A7.066 7.066 0 0112 19.53zm4.863-7.06a.47.47 0 01-.47.471H12a.47.47 0 01-.47-.47V8.078a.47.47 0 01.94 0V12h3.922a.47.47 0 01.47.47z"
        fill={fillColor}
      />
    </Svg>
  );
};

export default Clock;
