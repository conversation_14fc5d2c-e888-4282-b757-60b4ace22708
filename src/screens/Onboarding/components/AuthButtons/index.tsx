/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { View } from 'react-native';
import Button from '@/src/components/Button';
import { AuthButtonsProps } from './types';

const AuthButtons = ({ onCreateAccount, onSignIn }: AuthButtonsProps) => {
  return (
    <View className="px-6 py-8 gap-4">
      <Button
        label="Create an account"
        variant="primary"
        onPress={onCreateAccount}
        className="font-medium "
      />
      <Button className="font-medium" label="Sign In" variant="secondary" onPress={onSignIn} />
    </View>
  );
};

export default AuthButtons;
