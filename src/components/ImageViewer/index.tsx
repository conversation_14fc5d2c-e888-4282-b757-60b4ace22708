import { Text, TouchableOpacity, View } from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Modal from 'react-native-modal';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  runOnJS,
  clamp,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { formatSocialTime } from '@/src/utilities/datetime';
import Close from '@/src/assets/svgs/Close';
import type { PostMediaI } from '@/src/networks/content/types';
import Carousel from '../Carousel';
import UserAvatar from '../UserAvatar';
import type { ImageViewerModalProps } from './types';

const ZoomableImage = ({ item, index }: { item: PostMediaI; index: number }) => {
  const scale = useSharedValue(1);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
      { scale: scale.value },
    ],
  }));

  const resetTransform = () => {
    scale.value = withSpring(1);
    translateX.value = withSpring(0);
    translateY.value = withSpring(0);
  };

  const gesture = (() => {
    let savedScale = 1;
    let savedTranslateX = 0;
    let savedTranslateY = 0;

    const pinchGesture = Gesture.Pinch()
      .onStart(() => {
        savedScale = scale.value;
        savedTranslateX = translateX.value;
        savedTranslateY = translateY.value;
      })
      .onUpdate((e) => {
        const newScale = clamp(savedScale * e.scale, 0.5, 4);
        scale.value = newScale;
      })
      .onEnd(() => {
        if (scale.value < 1) {
          runOnJS(resetTransform)();
        } else if (scale.value > 3) {
          scale.value = withSpring(3);
        }
      });

    const panGesture = Gesture.Pan()
      .onStart(() => {
        savedTranslateX = translateX.value;
        savedTranslateY = translateY.value;
      })
      .onUpdate((e) => {
        if (scale.value > 1.1) {
          translateX.value = savedTranslateX + e.translationX;
          translateY.value = savedTranslateY + e.translationY;
        }
      })
      .onEnd(() => {
        if (scale.value <= 1.1) {
          translateX.value = withSpring(0);
          translateY.value = withSpring(0);
        }
      })
      .enabled(true)
      .minDistance(scale.value > 1.1 ? 5 : 999)
      .activeOffsetX(scale.value > 1.1 ? [-5, 5] : [-999, 999])
      .activeOffsetY(scale.value > 1.1 ? [-5, 5] : [-999, 999]);

    const doubleTapGesture = Gesture.Tap()
      .numberOfTaps(2)
      .onEnd(() => {
        if (scale.value > 1.1) {
          runOnJS(resetTransform)();
        } else {
          scale.value = withSpring(2.5);
        }
      });

    return Gesture.Race(doubleTapGesture, Gesture.Simultaneous(pinchGesture, panGesture));
  })();

  return (
    <View className="w-full h-full">
      <View className="w-full justify-center items-center flex-1">
        <GestureDetector gesture={gesture}>
          <Animated.View className="w-full h-full justify-center items-center">
            <Animated.Image
              source={{ uri: item.fileUrl }}
              className="w-full h-full"
              resizeMode="contain"
              style={animatedStyle}
            />
          </Animated.View>
        </GestureDetector>
      </View>
    </View>
  );
};

const ImageViewer = ({ isVisible, onClose, post, initialIndex = 0 }: ImageViewerModalProps) => {
  const timeAgo = formatSocialTime(post.createdAt);
  const insets = useSafeAreaInsets();
  const hasSingleMedia = post.Media?.length === 1;

  return (
    <View
      className="absolute pointer-events-none"
      style={{ top: insets.top, bottom: insets.bottom }}
    >
      <Modal
        isVisible={isVisible}
        onBackdropPress={onClose}
        onBackButtonPress={onClose}
        style={{ margin: 0 }}
        animationIn="fadeIn"
        animationOut="fadeOut"
        backdropOpacity={1}
        backdropColor="black"
        animationInTiming={250}
        animationOutTiming={250}
        backdropTransitionInTiming={250}
        backdropTransitionOutTiming={1}
        statusBarTranslucent
        useNativeDriver={false}
        useNativeDriverForBackdrop={false}
        hideModalContentWhileAnimating={false}
        avoidKeyboard
      >
        <View className="flex-1 bg-black">
          <View className="h-full w-full" style={{ paddingTop: insets.top }}>
            <View className="flex-row items-center justify-between px-4 py-2">
              <View className="flex-row items-center flex-1">
                <UserAvatar
                  avatarUri={post.Profile.avatar}
                  name={post.Profile.name}
                  width={40}
                  height={40}
                  className="mr-3"
                />
                <View className="flex-1 ml-3">
                  <Text className="text-white font-bold text-base">{post.Profile.name}</Text>
                  <Text className="text-gray-300 text-xs" numberOfLines={1}>
                    {post.Profile.designation?.name}{' '}
                    {post.Profile.entity?.name ? `at ${post.Profile.entity.name}` : ''}
                  </Text>
                  <Text className="text-gray-400 text-xs">{timeAgo}</Text>
                </View>
              </View>
              <TouchableOpacity onPress={onClose} className="p-2">
                <Close stroke="white" width={2} height={2} />
              </TouchableOpacity>
            </View>
            <View className="flex-1 justify-center items-center">
              {hasSingleMedia ? (
                <ZoomableImage item={post.Media[0]} index={0} />
              ) : (
                <Carousel
                  showArrows={true}
                  showDots={false}
                  showSlideNumbers={true}
                  activeColor="#FFFFFF"
                  inactiveColor="#FFFFFF50"
                  arrowClassName="bg-white/70 p-3 rounded-full"
                  dotClassName="h-3 w-3"
                  autoPlay={false}
                  initialIndex={initialIndex}
                >
                  {post.Media?.map((media, index) => (
                    <ZoomableImage key={index} item={media} index={index} />
                  ))}
                </Carousel>
              )}
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default ImageViewer;
