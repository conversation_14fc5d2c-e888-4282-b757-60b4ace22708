import type { ReactNode } from 'react';

export type MessageHandler = (type: string, data: any, rawMessage: any) => void;

export interface SocketContextValue {
  isConnected: boolean;
  isConnecting: boolean;
  socket: WebSocket | null;
  sendMessage: (type: string, data: any) => Promise<void>;
  disconnect: (permanent?: boolean) => void;
  subscribeToMessages: (messageTypes: string[], handler: MessageHandler) => () => void;
  connectSocket: (profileId: string) => Promise<void>;
}

export interface SocketContextProviderProps {
  children: ReactNode;
}
