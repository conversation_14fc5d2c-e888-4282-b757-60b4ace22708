/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import Toast from 'react-native-toast-message';
import { ShowToastOptions } from './types';

export const showToast = ({
  message,
  description = '',
  type = 'info',
  visibilityTime = 2000,
}: ShowToastOptions): void => {
  Toast.show({
    type,
    text1: message,
    text2: description,
    position: 'bottom',
    visibilityTime,
    autoHide: true,
    bottomOffset: 40,
    swipeable: false,
  });
};
