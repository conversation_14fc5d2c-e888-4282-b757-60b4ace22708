/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Circle, Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const Certification: React.FC<FilledIconPropsI> = ({
  width = 5.456,
  height = 5.58,
  fill = '#448600',
  color,
  disabled,
  accessibilityLabel = 'Certification',
  ...props
}) => {
  const fillColor = color || fill;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 44 45"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Circle cx="22" cy="22.4707" r="21.5" fill="#F2F9E5" stroke="#DDEFC8" />
      <Path
        d="M30.25 19.4707C30.2508 18.0804 29.9003 16.7124 29.2309 15.4939C28.5616 14.2753 27.5951 13.2456 26.4213 12.5006C25.2475 11.7555 23.9044 11.3191 22.5168 11.232C21.1293 11.1449 19.7422 11.4099 18.4844 12.0024C17.2266 12.5948 16.139 13.4955 15.3225 14.6208C14.506 15.7462 13.9872 17.0595 13.8141 18.439C13.6411 19.8186 13.8195 21.2194 14.3328 22.5115C14.846 23.8036 15.6775 24.945 16.75 25.8298V32.9707C16.7499 33.0986 16.7825 33.2244 16.8448 33.3362C16.907 33.4479 16.9968 33.5419 17.1056 33.6092C17.2144 33.6764 17.3386 33.7148 17.4663 33.7205C17.5941 33.7262 17.7212 33.6992 17.8356 33.642L22 31.5645L26.1653 33.6467C26.2697 33.6966 26.3842 33.722 26.5 33.7207C26.6989 33.7207 26.8897 33.6417 27.0303 33.5011C27.171 33.3604 27.25 33.1696 27.25 32.9707V25.8298C28.1884 25.0569 28.944 24.0859 29.4627 22.9865C29.9814 21.887 30.2503 20.6864 30.25 19.4707ZM15.25 19.4707C15.25 18.1357 15.6459 16.8307 16.3876 15.7206C17.1293 14.6106 18.1835 13.7454 19.4169 13.2345C20.6503 12.7236 22.0075 12.59 23.3169 12.8504C24.6262 13.1109 25.829 13.7537 26.773 14.6977C27.717 15.6418 28.3599 16.8445 28.6203 18.1539C28.8808 19.4632 28.7471 20.8204 28.2362 22.0538C27.7253 23.2872 26.8601 24.3414 25.7501 25.0831C24.6401 25.8248 23.335 26.2207 22 26.2207C20.2104 26.2187 18.4947 25.5069 17.2292 24.2415C15.9638 22.9761 15.252 21.2603 15.25 19.4707ZM25.75 31.7576L22.3347 30.0504C22.2305 29.9983 22.1156 29.9711 21.9991 29.9711C21.8825 29.9711 21.7676 29.9983 21.6634 30.0504L18.25 31.7576V26.8179C19.411 27.4113 20.6962 27.7207 22 27.7207C23.3038 27.7207 24.589 27.4113 25.75 26.8179V31.7576ZM22 24.7207C23.0384 24.7207 24.0534 24.4128 24.9167 23.8359C25.7801 23.2591 26.453 22.4391 26.8504 21.4798C27.2477 20.5205 27.3517 19.4649 27.1491 18.4465C26.9466 17.4281 26.4465 16.4926 25.7123 15.7584C24.9781 15.0242 24.0426 14.5242 23.0242 14.3216C22.0058 14.119 20.9502 14.223 19.9909 14.6204C19.0316 15.0177 18.2117 15.6906 17.6348 16.554C17.0579 17.4173 16.75 18.4324 16.75 19.4707C16.7515 20.8626 17.3051 22.1971 18.2893 23.1814C19.2736 24.1656 20.6081 24.7192 22 24.7207Z"
        fill={fillColor}
      />
    </Svg>
  );
};

export default Certification;
