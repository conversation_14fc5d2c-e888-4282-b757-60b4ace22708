import { IdNameI } from '@/src/types/common/data';

export type AboutState = {
  educationCount: number;
  statutoryCount: number;
  valueAddedCount: number;
  identityCount: number;
  visaCount: number;
  mutualCount: number;
  maritimeSkillsCount: number;
  otherSkillsCount: number;
  profileEducations: EducationI[];
  profileStatutoryCertifications: ProfileStatutoryI[];
  profileIdentityDocuments: ProfileIdentityI[];
  profileMaritimeSkills: SearchResultTypesI[];
  description: string | null;
};

export type EducationI = {
  id: string;
  fromDate: string;
  toDate: string;
  degree: SearchResultTypesI;
  entity: SearchResultTypesI;
  createdAt?: string;
};

export type SearchResultTypesI = {
  id: string;
  name: string;
  dataType: 'raw' | 'master';
};

export type ProfileStatutoryI = {
  id: string;
  name?: string;
  fromDate: string;
  untilDate: string;
  entity: SearchResultTypesI;
};

type ProfileIdentityI = {
  id: string;
  type: string;
  countryName: string;
  fromDate: string;
  untilDate: string;
};

export type ValueAddedI = {
  id: string;
  entity: IdNameI;
  certificateCourse: IdNameI;
  fromDate: string;
  untilDate: string;
};

export type VisaI = {
  country: {
    name: string;
  };
  expiryStatus: string;
  fromDate: string;
  untilDate: string;
  id: string;
  fileUrl: string;
  name: string;
};
