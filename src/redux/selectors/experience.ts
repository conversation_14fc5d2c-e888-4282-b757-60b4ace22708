import { createSelector } from '@reduxjs/toolkit';
import { ExperienceStateI } from '../slices/experience/types';
import { selectAllSelections } from './search';

export const selectExperienceState = (state: { experience: ExperienceStateI }): ExperienceStateI =>
  state.experience;

export const selectExperiences = createSelector(
  [selectExperienceState],
  (experienceState) => experienceState.experiences || [],
);

export const selectAllDesignations = createSelector([selectAllSelections], (selections) => {
  return Object.entries(selections)
    .filter(([key]) => key.startsWith('designation-'))
    .map(([key, value]) => {
      const indexKey = key.split('-')[1];
      return {
        ...value,
        indexKey,
      };
    });
});
