import type { GlobalSearchCategory } from '@/src/utilities/search/types';

export interface RecentSearchesProps {
  setSearchData: (text: string) => void;
  setShowRecent: (flag: boolean) => void;
  setLoading: (flag: boolean) => void;
  setActiveTab: (tab: GlobalSearchCategory) => void;
  setLastSearchQuery: (query: string) => void;
  performSearch: (query: string, category: GlobalSearchCategory, page: number) => Promise<void>;
  category: string;
}

export interface RecentSearchItem {
  category: string;
  searchText: string;
}
