/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState, useEffect } from 'react';
import { showToast } from '@/src/utilities/toast';
import { fetchBlockedProfilesAPI, unblockProfileAPI } from '@/src/networks/connect/blocking';
import { ProfileExternalI } from '@/src/networks/connect/types';
import { BlockedUserI, UseBlockedProfilesReturn } from './types';

export const useBlockedProfiles = (): UseBlockedProfilesReturn => {
  const [blockedUsers, setBlockedUsers] = useState<BlockedUserI[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [isUnblocking, setIsUnblocking] = useState<Record<string, boolean>>({});
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [error, setError] = useState<Error | null>(null);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const pageSize = 10;

  const fetchProfiles = async (currentPage: number, shouldReset = false) => {
    try {
      const response = await fetchBlockedProfilesAPI({
        page: currentPage,
        pageSize,
      });

      const formattedData: BlockedUserI[] = response.data.map((profile: ProfileExternalI) => ({
        Profile: profile,
      }));

      setTotalCount(response.total);
      setHasMore(formattedData.length === pageSize);

      if (shouldReset) {
        setBlockedUsers(formattedData);
      } else {
        setBlockedUsers((prev) => [...prev, ...formattedData]);
      }
    } catch (error) {
      if (shouldReset) {
        triggerErrorBoundary(
          new Error(
            `Failed to load blocked profiles: ${error instanceof Error ? error.message : 'Unknown error'}`,
          ),
        );
      } else {
        showToast({
          type: 'error',
          message: 'Error',
          description: 'Failed to load blocked profiles',
        });
      }
    }
  };

  const loadInitialData = async () => {
    setLoading(true);
    await fetchProfiles(0, true);
    setLoading(false);
  };

  useEffect(() => {
    loadInitialData();
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    setPage(0);
    await fetchProfiles(0, true);
    setRefreshing(false);
  };

  const handleLoadMore = async () => {
    if (!hasMore || loading || refreshing) return;

    const nextPage = page + 1;
    setPage(nextPage);
    await fetchProfiles(nextPage);
  };

  const handleUnblock = async (profileId: string) => {
    try {
      setIsUnblocking((prev) => ({ ...prev, [profileId]: true }));

      await unblockProfileAPI({ toUnblockId: profileId });

      setBlockedUsers((prev) => prev.filter((user) => user.Profile.id !== profileId));
      setTotalCount((prev) => Math.max(0, prev - 1));

      showToast({
        type: 'success',
        message: 'User Unblocked',
        description: 'User has been unblocked successfully',
      });
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Error',
        description: 'Failed to unblock user',
      });
    } finally {
      setIsUnblocking((prev) => ({ ...prev, [profileId]: false }));
    }
  };

  return {
    blockedUsers,
    loading,
    refreshing,
    isUnblocking,
    totalCount,
    handleRefresh,
    handleLoadMore,
    handleUnblock,
  };
};
