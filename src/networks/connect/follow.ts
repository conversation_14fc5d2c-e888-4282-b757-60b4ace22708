/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { apiCall } from '@/src/services/api';
import { FetchFollowersParamsI, FetchFollowersResponseI } from './types';

export const followOrUnfollowAPI = (followeeProfileId: string, isFollowing: boolean) => {
  const action = isFollowing ? 'unfollow' : 'follow';
  const method = isFollowing ? 'DELETE' : 'POST';

  const result = apiCall(`/backend/api/v1/network/${action}`, method, {
    isAuth: true,
    ...(method === 'POST' ? { payload: { followeeProfileId } } : { query: { followeeProfileId } }),
  });

  return result;
};

export const fetchFollowersAPI = async ({
  profileId,
  cursorId,
  pageSize = 10,
}: FetchFollowersParamsI): Promise<FetchFollowersResponseI> => {
  const params = { profileId, pageSize } as FetchFollowersParamsI;
  params.cursorId = cursorId ?? null;

  const result = await apiCall<FetchFollowersParamsI, FetchFollowersResponseI>(
    '/backend/api/v1/network/followers',
    'GET',
    {
      isAuth: true,
      query: params,
    },
  );

  return result;
};

export const fetchFollowingAPI = async ({
  profileId,
  cursorId,
  pageSize = 10,
}: FetchFollowersParamsI): Promise<FetchFollowersResponseI> => {
  const params: FetchFollowersParamsI = { profileId, pageSize };
  params.cursorId = cursorId ?? null;

  const result = await apiCall<FetchFollowersParamsI, FetchFollowersResponseI>(
    '/backend/api/v1/network/followings',
    'GET',
    {
      isAuth: true,
      query: params,
    },
  );

  return result;
};
