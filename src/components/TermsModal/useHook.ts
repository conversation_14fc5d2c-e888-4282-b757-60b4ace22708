import { useEffect, useState } from 'react';
import { showToast } from '@/src/utilities/toast';
import { fetchPrivacyPolicyAPI } from '@/src/networks/profile/privacyPolicy';

export const useTermsModal = (isVisible: boolean) => {
  const [termsContent, setTermsContent] = useState('');
  const [loading, setLoading] = useState(false);

  const fetchTermsOfUse = async () => {
    try {
      setLoading(true);
      const result = await fetchPrivacyPolicyAPI({
        type: 'TERMS_OF_USE',
      });

      if (result) {
        setTermsContent(result.content);
      }
    } catch (error) {
      showToast({
        message: 'Error loading terms of service',
        type: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTermsOfUse();
  }, [isVisible]);

  return {
    termsContent,
    loading,
    isVisible,
  };
};
