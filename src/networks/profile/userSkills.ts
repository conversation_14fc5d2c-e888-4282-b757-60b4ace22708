import { apiCall } from '@/src/services/api';
import { UserSkillsResponse } from './types';

export const fetchUserSkills = async (
  profileId: string,
  category: string,
): Promise<UserSkillsResponse> => {
  const result = await apiCall<string, UserSkillsResponse>(
    '/backend/api/v1/career/profile-skills',
    'GET',
    {
      isAuth: true,
      query: {
        profileId,
        category,
        pageSize: 3,
      },
    },
  );

  return result;
};
