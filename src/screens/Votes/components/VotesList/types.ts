/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { RouteProp } from '@react-navigation/native';
import { ListItem } from '@/src/components/UsersList/types';
import { HomeStackParamListI } from '@/src/navigation/types';
import { ReactionExternalClientI } from '@/src/networks/content/types';

export type VotesListPropsI = {
  onUserPress: (item: ListItem) => void;
};

export type UseVotesListResultI = {
  reactions: {
    reactions: ReactionExternalClientI[];
    totalCount: number;
  };
  loading: boolean;
  refreshing: boolean;
  handleRefresh: () => void;
  handleLoadMore: () => void;
};

export type VotesRouteI = RouteProp<HomeStackParamListI, 'Votes'>;
