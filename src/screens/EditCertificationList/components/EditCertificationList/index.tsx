import { SetStateAction, useState } from 'react';
import { Pressable, Text, View } from 'react-native';
import BackButton from '@/src/components/BackButton';
import Tabs from '@/src/components/Tabs';
import AddItem from '@/src/assets/svgs/AddItem';
import { EditStatutory } from '../EditStatutoryCertifications';
import { EditValueAdded } from '../EditValueAddedCertifications';
import { EditCertificationListPropsI, EditCertificationTabsI } from './types';
import { useEditCertificationList } from './useHook';

export const EditCertificationList = ({
  onBack,
  profileId,
  editable,
  tab,
}: EditCertificationListPropsI) => {
  const { onAddCertification } = useEditCertificationList(profileId);

  const tabs = [
    {
      id: 'statutory',
      label: 'Statutory',
    },
    {
      id: 'value-added',
      label: 'Value Added',
    },
  ];

  const tabScreens: EditCertificationTabsI = {
    statutory: <EditStatutory profileId={profileId} editable={editable} />,
    'value-added': <EditValueAdded profileId={profileId} editable={editable} />,
  };

  const [activeTab, setActiveTab] = useState<keyof EditCertificationTabsI>(tab);

  console.log(activeTab);

  return (
    <View className="flex-1 px-4">
      <View className="flex-row items-center justify-between">
        <BackButton
          onBack={onBack}
          label={editable ? 'Edit Certifications' : 'View Certifications'}
        />
        <View>
          {editable && (
            <Pressable onPress={() => onAddCertification(activeTab)}>
              <AddItem />
            </Pressable>
          )}
        </View>
      </View>

      <>
        <Tabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab as React.Dispatch<SetStateAction<string>>}
        />
        {tabScreens[activeTab]}
      </>
    </View>
  );
};

export default EditCertificationList;
