import type React from 'react';
import { View, Text, Dimensions, Pressable } from 'react-native';
import Modal from 'react-native-modal';
import type { RateLimitModalProps, ModalFeature } from './types';

const { width } = Dimensions.get('window');

const features: ModalFeature[] = [
  {
    icon: '⚓',
    text: 'Your AI navigation quota has been reached',
  },
  {
    icon: '🌊',
    text: 'Fresh queries sail in at midnight',
  },
  {
    icon: '🧭',
    text: 'Chart your course manually for now',
  },
];

const RateLimitModal: React.FC<RateLimitModalProps> = ({ visible, onClose }) => {
  return (
    <Modal
      isVisible={visible}
      onBackdropPress={onClose}
      onBackButtonPress={onClose}
      animationIn="fadeIn"
      animationOut="fadeOut"
      backdropOpacity={0.75}
      animationInTiming={250}
      animationOutTiming={250}
      backdropTransitionInTiming={250}
      backdropTransitionOutTiming={1}
      statusBarTranslucent
      useNativeDriverForBackdrop
      hideModalContentWhileAnimating={false}
      avoidKeyboard
    >
      <View className="flex-1 justify-center items-center px-5">
        <View
          className="w-full rounded-lg overflow-hidden"
          style={{
            width: width * 0.85,
            maxWidth: 350,
          }}
        >
          <View style={{ backgroundColor: '#047857' }}>
            <View className="items-center pt-6 pb-4">
              <View
                className="w-16 h-16 rounded-full justify-center items-center mb-5"
                style={{
                  backgroundColor: 'rgba(16, 185, 129, 0.3)',
                  borderWidth: 2,
                  borderColor: 'rgba(16, 185, 129, 0.5)',
                }}
              >
                <Text className="text-3xl">🚢</Text>
              </View>

              <Text
                className="text-2xl font-bold text-center mb-2"
                style={{
                  color: '#ffffff',
                }}
              >
                Daily Voyage Complete!
              </Text>

              <Text
                className="text-sm text-center mb-6 px-4"
                style={{
                  color: 'rgba(167, 243, 208, 0.9)',
                }}
              >
                You've navigated through all 3 AI queries of this hour
              </Text>

              <View className="w-full px-6 mb-6">
                {features.map((feature, index) => (
                  <View key={index} className="flex-row items-center mb-3">
                    <Text className="text-lg mr-3">{feature.icon}</Text>
                    <Text
                      className="flex-1 text-sm"
                      style={{
                        color: 'rgba(167, 243, 208, 0.9)',
                      }}
                    >
                      {feature.text}
                    </Text>
                  </View>
                ))}
              </View>

              <Pressable className="w-full" onPress={onClose}>
                <View
                  className="py-3 mx-5 rounded-xl items-center"
                  style={{
                    backgroundColor: '#10b981',
                  }}
                >
                  <Text
                    className="text-base font-medium"
                    style={{
                      color: '#ffffff',
                    }}
                  >
                    Sail After Hour
                  </Text>
                </View>
              </Pressable>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default RateLimitModal;
