/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { View } from 'react-native';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from './types';

const Company: React.FC<OutlinedIconPropsI> = ({
  width = 2,
  height = 2,
  color = '#4B5563',
  secondaryColor,
  containerStyle,
  strokeWidth = 1.5,
  accessibilityLabel = 'Company icon',
  active = false,
  disabled = false,
  fill,
  stroke,
  ...props
}) => {
  const actualColor = disabled ? '#9CA3AF' : stroke || color;
  const actualStrokeWidth = active ? strokeWidth + 0.5 : strokeWidth;

  return (
    <View style={containerStyle} accessibilityLabel={accessibilityLabel}>
      <Svg
        width={RFPercentage(width)}
        height={RFPercentage(height)}
        viewBox="0 0 24 24"
        fill="none"
        {...props}
      >
        <Path
          d="M19 21V5C19 3.9 18.1 3 17 3H7C5.9 3 5 3.9 5 5V21"
          stroke={actualColor}
          strokeWidth={actualStrokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <Path
          d="M3 21H21"
          stroke={actualColor}
          strokeWidth={actualStrokeWidth}
          strokeLinecap="round"
        />
        <Path
          d="M9 7H10"
          stroke={actualColor}
          strokeWidth={actualStrokeWidth}
          strokeLinecap="round"
        />
        <Path
          d="M14 7H15"
          stroke={actualColor}
          strokeWidth={actualStrokeWidth}
          strokeLinecap="round"
        />
        <Path
          d="M9 11H10"
          stroke={actualColor}
          strokeWidth={actualStrokeWidth}
          strokeLinecap="round"
        />
        <Path
          d="M14 11H15"
          stroke={actualColor}
          strokeWidth={actualStrokeWidth}
          strokeLinecap="round"
        />
        <Path
          d="M9 15H10"
          stroke={actualColor}
          strokeWidth={actualStrokeWidth}
          strokeLinecap="round"
        />
        <Path
          d="M14 15H15"
          stroke={actualColor}
          strokeWidth={actualStrokeWidth}
          strokeLinecap="round"
        />
      </Svg>
    </View>
  );
};

export default Company;
