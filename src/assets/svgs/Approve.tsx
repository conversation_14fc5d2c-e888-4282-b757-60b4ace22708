/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const Approve: React.FC<FilledIconPropsI> = ({
  width = 2.1,
  height = 2,
  fill = '#16A34A',
  color,
  disabled,
  accessibilityLabel = 'Approve',
  ...props
}) => {
  const fillColor = color || fill;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 17 16"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.93 8A6.429 6.429 0 112.071 8 6.429 6.429 0 0114.93 8zm-3.838-1.948a.482.482 0 010 .682L7.877 9.948a.482.482 0 01-.681 0L5.91 8.662a.482.482 0 11.682-.681l.945.944L8.973 7.49l1.437-1.437a.482.482 0 01.682 0z"
        fill={fillColor}
      />
    </Svg>
  );
};

export default Approve;
