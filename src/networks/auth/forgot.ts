import { apiCall } from '@/src/services/api';
import {
  SendOTPForPasswordResetBodyI,
  VerifyOTPForPasswordResetBodyI,
  ResetPasswordBodyI,
} from './types';

export const sendOTPForPasswordResetAPI = async (payload: SendOTPForPasswordResetBodyI) => {
  const result = await apiCall<SendOTPForPasswordResetBodyI, undefined>(
    '/backend/api/v1/auth/forgot-password/send-otp',
    'POST',
    {
      isAuth: false,
      payload,
    },
  );

  return result;
};

export const verifyOTPForPasswordResetAPI = async (payload: VerifyOTPForPasswordResetBodyI) => {
  const result = await apiCall<VerifyOTPForPasswordResetBodyI, undefined>(
    '/backend/api/v1/auth/forgot-password/verify-otp',
    'POST',
    {
      isAuth: false,
      payload,
    },
  );

  return result;
};

export const resetPasswordAPI = async (payload: ResetPasswordBodyI) => {
  const result = await apiCall<ResetPasswordBodyI, { success: boolean; message: string }>(
    '/backend/api/v1/auth/forgot-password/reset-password',
    'POST',
    {
      isAuth: false,
      payload,
    },
  );

  return result;
};
