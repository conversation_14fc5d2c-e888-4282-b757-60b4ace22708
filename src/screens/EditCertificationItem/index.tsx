import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import SafeArea from '@/src/components/SafeArea';
import { ProfileStackParamsListI } from '@/src/navigation/types';
import EditCertificationItem from './components/EditCertificationItem';

type RouteProps = RouteProp<ProfileStackParamsListI, 'EditCertificationItem'>;

const EditCertificationItemScreen = () => {
  const route = useRoute<RouteProps>();
  const { profileId, certificationId, type } = route.params || {};
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();

  console.log(type);

  return (
    <SafeArea>
      <EditCertificationItem
        onBack={navigation.goBack}
        profileId={profileId!}
        certificationId={certificationId}
        type={type!}
      />
    </SafeArea>
  );
};

export default EditCertificationItemScreen;
