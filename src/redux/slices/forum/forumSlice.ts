import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { AttachmentI } from '@/src/screens/ForumAnswers/AnswerInput/types';
import {
  fetchForumAnswersAPI,
  fetchForumAnswerDetailAPI,
  createForumAnswerAPI,
  deleteForumAnswerAPI,
  updateForumAnswerStatusAPI,
} from '@/src/networks/answer/answer';
import type {
  ForumAnswerCreateOnePayloadI,
  ForumAnswerCreateOneResponseI,
  ForumAnswerDeleteOnePayloadI,
  AnswerUpdateStatusPayloadI,
} from '@/src/networks/answer/types';
import {
  fetchForumAnswerVotesAPI,
  createForumAnswerVoteAPI,
} from '@/src/networks/answerVote/answerVote';
import type {
  ForumAnswerVoteFetchManyParamsI,
  ForumAnswerVoteFetchManyResponseI,
  ForumAnswerVoteCreateOnePayloadI,
  ForumAnswerVoteCreateOneResponseI,
  ProfileExternalI,
} from '@/src/networks/answerVote/types';
import {
  addForumAnswerCommentAPI,
  deleteForumAnswerCommentAPI,
  fetchForumAnswerCommentsAPI,
  fetchForumAnswerCommentRepliesAPI,
} from '@/src/networks/forum/answerComment';
import {
  addForumCommentAPI,
  deleteForumCommentAPI,
  fetchForumCommentsAPI,
  fetchForumCommentRepliesAPI,
} from '@/src/networks/forum/comment';
import {
  QuestionCommentCreateI,
  QuestionCommentCreateResponseI,
  GetQuestionCommentsPayloadI,
  GetQuestionCommentRepliesPayloadI,
  GetQuestionCommentsResponseI,
  AnswerCommentCreateI,
  AnswerCommentCreateResponseI,
  GetAnswerCommentsPayloadI,
  GetAnswerCommentsResponseI,
  GetAnswerCommentRepliesPayloadI,
} from '@/src/networks/forum/types';
import { fetchForumQuestionForClientAPI } from '@/src/networks/question/question';
import type { ForumQuestionDetailWithAnswersI } from '@/src/networks/question/types';
import type { ForumState } from './types';

export const fetchForumQuestionDetail = createAsyncThunk<
  ForumQuestionDetailWithAnswersI,
  { questionId: string },
  { rejectValue: string }
>('forum/fetchForumQuestionDetail', async ({ questionId }, { rejectWithValue }) => {
  try {
    return await fetchForumQuestionForClientAPI(questionId);
  } catch (error) {
    if (error instanceof Error) {
      return rejectWithValue(error.message);
    }
    return rejectWithValue('Failed to fetch forum question');
  }
});

export const fetchAnswersForQuestion = createAsyncThunk(
  'answer/fetchAnswersForQuestion',
  async (
    {
      questionId,
      cursorId,
      pageSize,
    }: { questionId: string; cursorId?: number | null; pageSize?: number },
    { rejectWithValue },
  ) => {
    try {
      return await fetchForumAnswersAPI(questionId, cursorId, pageSize ?? 10);
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('Failed to fetch answers');
    }
  },
);

export const fetchAnswerDetail = createAsyncThunk(
  'answer/fetchAnswerDetail',
  async (id: string, { rejectWithValue }) => {
    try {
      return await fetchForumAnswerDetailAPI(id);
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('Failed to fetch answer detail');
    }
  },
);

export const createAnswer = createAsyncThunk<
  ForumAnswerCreateOneResponseI & { tempId: string },
  ForumAnswerCreateOnePayloadI & { tempId: string },
  { rejectValue: string; state: { forum: ForumState; user: any } }
>('answer/createAnswer', async (args, { dispatch, getState, rejectWithValue }) => {
  const { tempId, ...payload } = args;
  const state = getState();
  const currentUser = state.user;
  const previewAnswer: any = {
    id: tempId,
    cursorId: 0,
    text: args.text,
    upvoteCount: 0,
    downvoteCount: 0,
    commentCount: 0,
    status: '',
    isEdited: false,
    createdAt: new Date().toISOString(),
    profile: {
      id: currentUser?.profileId || 'temp',
      name: currentUser?.fullName || 'You',
      avatar: currentUser?.avatar || null,
    },
    userVote: null,
    media: args.files
      ? args.files.map((file) => ({
          fileUrl: file.fileUrl,
          fileExtension: file.fileExtension,
        }))
      : [],
  };
  dispatch(addAnswerOptimistic(previewAnswer));
  try {
    const response = await createForumAnswerAPI(payload);
    return { ...response, tempId };
  } catch (error: any) {
    dispatch(removeAnswerOptimistic({ answerId: tempId }));
    return rejectWithValue(error?.message || 'Failed to create answer');
  }
});

export const fetchAnswerVotes = createAsyncThunk<
  ForumAnswerVoteFetchManyResponseI,
  ForumAnswerVoteFetchManyParamsI,
  { rejectValue: string }
>('answerVote/fetchMany', async (params, { rejectWithValue }) => {
  try {
    return await fetchForumAnswerVotesAPI(params);
  } catch (error: any) {
    return rejectWithValue(error?.message || 'Failed to fetch answer votes');
  }
});

export const createAnswerVote = createAsyncThunk<
  ForumAnswerVoteCreateOneResponseI,
  ForumAnswerVoteCreateOnePayloadI,
  { rejectValue: string }
>('answerVote/createOne', async (payload, { rejectWithValue }) => {
  try {
    return await createForumAnswerVoteAPI(payload);
  } catch (error: any) {
    return rejectWithValue(error?.message || 'Failed to vote on answer');
  }
});

export const createForumComment = createAsyncThunk(
  'forum/createComment',
  async (payload: QuestionCommentCreateI, { dispatch, rejectWithValue, getState }) => {
    const { questionId, text, parentCommentId } = payload;
    const state = getState() as { forum: ForumState; user: any };
    const currentUser = state.user;
    const tempId = `temp_${Date.now()}`;
    const profile = {
      id: currentUser.profileId,
      name: currentUser.fullName,
      avatar: currentUser.avatar,
      entity: null,
      designation: null,
    };

    dispatch(
      addCommentOptimistic({
        questionId,
        comment: {
          id: tempId,
          text,
          parentCommentId,
          profile,
          createdAt: new Date().toISOString(),
          replies: [],
          replyCount: 0,
          isAnonymous: false,
          questionId,
          cursorId: 0,
          updatedAt: new Date().toISOString(),
          Profile: currentUser.profile,
        },
      }),
    );

    try {
      const response = await addForumCommentAPI(payload);
      if (payload.parentCommentId) {
        dispatch(
          incrementCommentCount({
            questionId: payload.questionId,
            answerId: payload.parentCommentId,
          }),
        );
      } else {
        dispatch(incrementCommentCount({ questionId: payload.questionId }));
      }
      return { ...response, tempId };
    } catch (error: any) {
      dispatch(removeCommentOptimistic({ questionId, commentId: tempId, parentCommentId }));
      return rejectWithValue(error?.message || 'Failed to create comment');
    }
  },
);

export const createForumAnswerComment = createAsyncThunk(
  'forum/createAnswerComment',
  async (payload: AnswerCommentCreateI, { dispatch, rejectWithValue, getState }) => {
    const { answerId, text, parentCommentId } = payload;
    const state = getState() as { forum: ForumState; user: any };
    const currentUser = state.user;
    const tempId = `temp_${Date.now()}`;
    const profile = {
      id: currentUser.profileId,
      name: currentUser.fullName,
      avatar: currentUser.avatar,
      entity: null,
      designation: null,
    };
    dispatch(
      addAnswerCommentOptimistic({
        answerId,
        comment: {
          id: tempId,
          text,
          parentCommentId,
          profile,
          createdAt: new Date().toISOString(),
          replies: [],
          replyCount: 0,
          isAnonymous: false,
          answerId,
          cursorId: 0,
          updatedAt: new Date().toISOString(),
          Profile: currentUser.profile,
        },
      }),
    );

    try {
      const response = await addForumAnswerCommentAPI(payload);
      dispatch(incrementCommentCount({ answerId: payload.answerId, isAnswer: true }));
      return { ...response, tempId };
    } catch (error: any) {
      dispatch(removeAnswerCommentOptimistic({ answerId, commentId: tempId, parentCommentId }));
      return rejectWithValue(error?.message || 'Failed to create comment');
    }
  },
);

export const deleteForumComment = createAsyncThunk(
  'forum/deleteComment',
  async (
    {
      commentId,
      questionId,
      parentCommentId,
    }: { commentId: string; questionId: string; parentCommentId?: string },
    { dispatch, rejectWithValue },
  ) => {
    dispatch(removeCommentOptimistic({ questionId, commentId, parentCommentId }));
    try {
      await deleteForumCommentAPI(commentId);
      if (parentCommentId) {
        dispatch(decrementCommentCount({ questionId, answerId: parentCommentId }));
      } else {
        dispatch(decrementCommentCount({ questionId }));
      }
    } catch (error: any) {
      return rejectWithValue(error?.message || 'Failed to delete comment');
    }
    if (parentCommentId) {
      dispatch(fetchCommentsForQuestion({ questionId, cursorId: null, pageSize: 10 }));
    }
  },
);

export const deleteForumAnswerComment = createAsyncThunk(
  'forum/deleteAnswerComment',
  async (
    {
      commentId,
      answerId,
      parentCommentId,
    }: { commentId: string; answerId: string; parentCommentId?: string },
    { dispatch, rejectWithValue },
  ) => {
    dispatch(removeAnswerCommentOptimistic({ answerId, commentId, parentCommentId }));
    try {
      await deleteForumAnswerCommentAPI(commentId);
      if (parentCommentId) {
        dispatch(decrementCommentCount({ answerId: parentCommentId, isAnswer: true }));
      } else {
        dispatch(decrementCommentCount({ answerId, isAnswer: true }));
      }
    } catch (error: any) {
      return rejectWithValue(error?.message || 'Failed to delete comment');
    }
    if (parentCommentId) {
      dispatch(fetchCommentsForAnswer({ answerId, cursorId: null, pageSize: 10 }));
    }
  },
);

export const fetchCommentsForQuestion = createAsyncThunk<
  GetQuestionCommentsResponseI,
  GetQuestionCommentsPayloadI,
  { rejectValue: string }
>('forum/fetchCommentsForQuestion', async (payload, { rejectWithValue }) => {
  try {
    return await fetchForumCommentsAPI(payload);
  } catch (error: any) {
    return rejectWithValue(error?.message || 'Failed to fetch comments');
  }
});

export const fetchCommentReplies = createAsyncThunk<
  GetQuestionCommentsResponseI,
  GetQuestionCommentRepliesPayloadI,
  { rejectValue: string }
>('forum/fetchCommentReplies', async (payload, { rejectWithValue }) => {
  try {
    return await fetchForumCommentRepliesAPI(payload);
  } catch (error: any) {
    return rejectWithValue(error?.message || 'Failed to fetch replies');
  }
});

export const fetchCommentsForAnswer = createAsyncThunk<
  GetAnswerCommentsResponseI,
  GetAnswerCommentsPayloadI,
  { rejectValue: string }
>('forum/fetchCommentsForAnswer', async (payload, { rejectWithValue }) => {
  try {
    return await fetchForumAnswerCommentsAPI(payload);
  } catch (error: any) {
    return rejectWithValue(error?.message || 'Failed to fetch comments');
  }
});

export const fetchAnswerCommentReplies = createAsyncThunk<
  GetAnswerCommentsResponseI,
  GetAnswerCommentRepliesPayloadI,
  { rejectValue: string }
>('forum/fetchAnswerCommentReplies', async (payload, { rejectWithValue }) => {
  try {
    return await fetchForumAnswerCommentRepliesAPI(payload);
  } catch (error: any) {
    return rejectWithValue(error?.message || 'Failed to fetch replies');
  }
});

export const deleteAnswer = createAsyncThunk<
  void,
  ForumAnswerDeleteOnePayloadI,
  { rejectValue: string }
>('answer/deleteAnswer', async (payload, { rejectWithValue }) => {
  try {
    await deleteForumAnswerAPI(payload);
    return;
  } catch (error: any) {
    return rejectWithValue(error?.message || 'Failed to delete answer');
  }
});

export const updateAnswerStatus = createAsyncThunk<
  void,
  AnswerUpdateStatusPayloadI,
  { rejectValue: string }
>('answer/updateStatus', async (payload, { rejectWithValue, dispatch }) => {
  try {
    await updateForumAnswerStatusAPI(payload);
    dispatch(updateAnswerStatusOptimistic(payload));
  } catch (error: any) {
    return rejectWithValue(error?.message || 'Failed to update answer status');
  }
});

const initialState: ForumState = {
  question: null,
  loading: false,
  error: null,
  answers: [],
  total: 0,
  nextCursorId: null,
  canUpdateStatus: false,
  answerLoading: false,
  answerError: null,
  answerDetail: null,
  answerVotes: [] as ProfileExternalI[],
  answerVotesTotal: 0,
  answerVotesNextCursorId: null,
  answerVoteLoading: false,
  answerVoteError: null,
  comments: {},
  commentReplies: {},
  answerComments: {},
  answerCommentReplies: {},
};

const forumSlice = createSlice({
  name: 'forum',
  initialState,
  reducers: {
    resetForumState: () => initialState,
    incrementCommentCount: (
      state,
      action: { payload: { questionId?: string; answerId?: string; isAnswer?: boolean } },
    ) => {
      if (action.payload.isAnswer) {
        const answer = state.answers.find((a) => a.id === action.payload.answerId);
        if (answer) {
          answer.commentCount = (answer.commentCount || 0) + 1;
        }
      } else if (
        action.payload.questionId &&
        state.question &&
        state.question.id === action.payload.questionId
      ) {
        state.question.commentCount = (state.question.commentCount || 0) + 1;
      }
    },
    decrementCommentCount: (
      state,
      action: { payload: { questionId?: string; answerId?: string; isAnswer?: boolean } },
    ) => {
      if (action.payload.isAnswer) {
        const answer = state.answers.find((a) => a.id === action.payload.answerId);
        if (answer) {
          answer.commentCount = (answer.commentCount || 0) - 1;
        }
      } else if (
        action.payload.questionId &&
        state.question &&
        state.question.id === action.payload.questionId
      ) {
        state.question.commentCount = (state.question.commentCount || 0) - 1;
      }
    },
    addCommentOptimistic: (state, action) => {
      const { questionId, comment } = action.payload;
      if (!state.comments[questionId]) {
        state.comments[questionId] = { comments: [], total: 0, nextCursorId: null };
      }
      if (comment.parentCommentId) {
        if (!state.commentReplies[comment.parentCommentId]) {
          state.commentReplies[comment.parentCommentId] = {
            comments: [],
            total: 0,
            nextCursorId: null,
          };
        }
        state.commentReplies[comment.parentCommentId].comments.unshift(comment);
        state.commentReplies[comment.parentCommentId].total++;

        const parentComment = state.comments[questionId]?.comments.find(
          (c) => c.id === comment.parentCommentId,
        );
        if (parentComment) {
          parentComment.replyCount = (parentComment.replyCount || 0) + 1;
        }
      } else {
        state.comments[questionId].comments.unshift(comment);
      }
      state.comments[questionId].total++;
    },
    removeCommentOptimistic: (state, action) => {
      const { questionId, commentId, parentCommentId } = action.payload;
      if (parentCommentId) {
        if (state.commentReplies[parentCommentId]) {
          state.commentReplies[parentCommentId].comments = state.commentReplies[
            parentCommentId
          ].comments.filter((reply) => reply.id !== commentId);
          state.commentReplies[parentCommentId].total--;
        }

        const parentComment = state.comments[questionId]?.comments.find(
          (c) => c.id === parentCommentId,
        );
        if (parentComment) {
          parentComment.replyCount = (parentComment.replyCount || 0) - 1;
        }
      } else {
        state.comments[questionId].comments = state.comments[questionId].comments.filter(
          (c) => c.id !== commentId,
        );
      }
      if (state.comments[questionId]) {
        state.comments[questionId].total--;
      }
    },
    addAnswerCommentOptimistic: (state, action) => {
      const { answerId, comment } = action.payload;
      if (!state.answerComments[answerId]) {
        state.answerComments[answerId] = { comments: [], total: 0, nextCursorId: null };
      }
      if (comment.parentCommentId) {
        if (!state.answerCommentReplies[comment.parentCommentId]) {
          state.answerCommentReplies[comment.parentCommentId] = {
            comments: [],
            total: 0,
            nextCursorId: null,
          };
        }
        state.answerCommentReplies[comment.parentCommentId].comments.unshift(comment);
        state.answerCommentReplies[comment.parentCommentId].total++;

        const parentComment = state.answerComments[answerId]?.comments.find(
          (c) => c.id === comment.parentCommentId,
        );
        if (parentComment) {
          parentComment.replyCount = (parentComment.replyCount || 0) + 1;
        }
      } else {
        state.answerComments[answerId].comments.unshift(comment);
      }
      state.answerComments[answerId].total++;
    },
    removeAnswerCommentOptimistic: (state, action) => {
      const { answerId, commentId, parentCommentId } = action.payload;
      if (parentCommentId) {
        if (state.answerCommentReplies[parentCommentId]) {
          state.answerCommentReplies[parentCommentId].comments = state.answerCommentReplies[
            parentCommentId
          ].comments.filter((reply) => reply.id !== commentId);
          state.answerCommentReplies[parentCommentId].total--;
        }

        const parentComment = state.answerComments[answerId]?.comments.find(
          (c) => c.id === parentCommentId,
        );
        if (parentComment) {
          parentComment.replyCount = (parentComment.replyCount || 0) - 1;
        }
      } else {
        state.answerComments[answerId].comments = state.answerComments[answerId].comments.filter(
          (c) => c.id !== commentId,
        );
      }
      if (state.answerComments[answerId]) {
        state.answerComments[answerId].total--;
      }
    },

    addAnswerOptimistic: (state, action) => {
      state.answers.unshift(action.payload);
    },
    addAnswerVoteOptimistic: (state, action) => {
      const { answerId, type } = action.payload;
      const answer = state.answers.find((a) => a.id === answerId);
      if (answer) {
        if (type === 'UPVOTE') {
          answer.upvoteCount = (answer.upvoteCount || 0) + 1;
          if (answer.userVote === 'DOWNVOTE') {
            answer.downvoteCount = Math.max(0, (answer.downvoteCount || 0) - 1);
          }
          answer.userVote = 'UPVOTE';
        } else {
          answer.downvoteCount = (answer.downvoteCount || 0) + 1;
          if (answer.userVote === 'UPVOTE') {
            answer.upvoteCount = Math.max(0, (answer.upvoteCount || 0) - 1);
          }
          answer.userVote = 'DOWNVOTE';
        }
      }
    },
    removeAnswerVoteOptimistic: (state, action) => {
      const { answerId, type } = action.payload;
      const answer = state.answers.find((a) => a.id === answerId);
      if (answer) {
        if (type === 'UPVOTE') {
          answer.upvoteCount = Math.max(0, (answer.upvoteCount || 0) - 1);
        } else {
          answer.downvoteCount = Math.max(0, (answer.downvoteCount || 0) - 1);
        }
        answer.userVote = null;
      }
    },
    switchAnswerVoteOptimistic: (state, action) => {
      const { answerId, fromType, toType } = action.payload;
      const answer = state.answers.find((a) => a.id === answerId);
      if (answer) {
        if (fromType === 'UPVOTE') {
          answer.upvoteCount = Math.max(0, (answer.upvoteCount || 0) - 1);
        } else {
          answer.downvoteCount = Math.max(0, (answer.downvoteCount || 0) - 1);
        }
        if (toType === 'UPVOTE') {
          answer.upvoteCount = (answer.upvoteCount || 0) + 1;
        } else {
          answer.downvoteCount = (answer.downvoteCount || 0) + 1;
        }
        answer.userVote = toType;
      }
    },
    removeAnswerOptimistic: (state, action) => {
      const { answerId } = action.payload;
      state.answers = state.answers.filter((answer) => answer.id !== answerId);
      if (state.question && state.question.answerCount > 0) {
        state.question.answerCount -= 1;
      }
    },
    updateAnswerStatusOptimistic: (state, action: { payload: AnswerUpdateStatusPayloadI }) => {
      const { id, status } = action.payload;
      const answer = state.answers.find((a) => a.id === id);
      if (answer) {
        (answer as any).status = status;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchForumQuestionDetail.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchForumQuestionDetail.fulfilled, (state, action) => {
        state.loading = false;
        state.question = action.payload;
      })
      .addCase(fetchForumQuestionDetail.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch forum question';
      })
      .addCase(fetchAnswersForQuestion.pending, (state) => {
        state.answerLoading = true;
        state.answerError = null;
      })
      .addCase(fetchAnswersForQuestion.fulfilled, (state, action) => {
        state.answerLoading = false;
        const mappedAnswers = action.payload.data.map((answer: any) => ({
          ...answer,
          userVote: answer.vote,
        }));
        if (action.meta.arg.cursorId) {
          state.answers = [...state.answers, ...mappedAnswers];
        } else {
          state.answers = mappedAnswers;
        }
        state.total = action.payload.total;
        state.nextCursorId = action.payload.nextCursorId;
        state.canUpdateStatus = action.payload.data.some(
          (answer) => answer.canUpdateStatus === true,
        );
      })
      .addCase(fetchAnswersForQuestion.rejected, (state, action) => {
        state.answerLoading = false;
        state.answerError = action.payload as string;
      })
      .addCase(fetchAnswerDetail.pending, (state) => {
        state.answerLoading = true;
        state.answerError = null;
      })
      .addCase(fetchAnswerDetail.fulfilled, (state, action) => {
        state.answerLoading = false;
        state.answerDetail = action.payload;
      })
      .addCase(fetchAnswerDetail.rejected, (state, action) => {
        state.answerLoading = false;
        state.answerError = action.payload as string;
      })
      .addCase(createAnswer.pending, (state) => {
        state.answerLoading = true;
        state.answerError = null;
      })
      .addCase(createAnswer.fulfilled, (state, action) => {
        state.answerLoading = false;
        const { tempId, id, cursorId } = action.payload;
        const tempAnswer = state.answers.find((answer) => answer.id === tempId);
        if (tempAnswer) {
          tempAnswer.id = id;
          tempAnswer.cursorId = cursorId;
        }
      })
      .addCase(createAnswer.rejected, (state, action) => {
        state.answerLoading = false;
        state.answerError = action.payload as string;
      })
      .addCase(fetchAnswerVotes.pending, (state) => {
        state.answerVoteLoading = true;
        state.answerVoteError = null;
      })
      .addCase(fetchAnswerVotes.fulfilled, (state, action) => {
        state.answerVoteLoading = false;
        state.answerVotes = action.payload.data;
        state.answerVotesTotal = action.payload.total;
        state.answerVotesNextCursorId = action.payload.nextCursorId;
      })
      .addCase(fetchAnswerVotes.rejected, (state, action) => {
        state.answerVoteLoading = false;
        state.answerVoteError = action.payload as string;
      })
      .addCase(createAnswerVote.pending, (state) => {
        state.answerVoteLoading = true;
        state.answerVoteError = null;
      })
      .addCase(createAnswerVote.fulfilled, (state) => {
        state.answerVoteLoading = false;
      })
      .addCase(createAnswerVote.rejected, (state, action) => {
        state.answerVoteLoading = false;
        state.answerVoteError = action.payload as string;
      })
      .addCase(fetchCommentsForQuestion.fulfilled, (state, action) => {
        const { questionId } = action.meta.arg;
        const { data, total, nextCursorId } = action.payload;

        if (state.comments[questionId] && action.meta.arg.cursorId) {
          state.comments[questionId].comments.push(...data);
        } else {
          state.comments[questionId] = {
            comments: data,
            total,
            nextCursorId,
          };
        }

        data.forEach((comment) => {
          if (comment.replies && comment.replies.length > 0) {
            state.commentReplies[comment.id] = {
              comments: comment.replies,
              total: comment.replyCount || comment.replies.length,
              nextCursorId: null,
            };
          }
        });
      })

      .addCase(fetchCommentReplies.fulfilled, (state, action) => {
        const { parentCommentId } = action.meta.arg;
        const { data, total, nextCursorId } = action.payload;

        state.commentReplies[parentCommentId] = {
          comments: data,
          total,
          nextCursorId,
        };
      })
      .addCase(createForumComment.fulfilled, (state, action) => {
        const { tempId, id, cursorId } = action.payload;
        const { questionId, parentCommentId } = action.meta.arg;

        if (parentCommentId) {
          const tempReply = state.commentReplies[parentCommentId]?.comments.find(
            (c) => c.id === tempId,
          );
          if (tempReply) {
            tempReply.id = id;
            tempReply.cursorId = cursorId;
          }
        } else {
          const tempComment = state.comments[questionId]?.comments.find((c) => c.id === tempId);
          const tempReply = state.commentReplies[tempId]?.comments.find((r) => r.id === tempId);
          if (tempReply) {
            tempReply.id = id;
            tempReply.cursorId = cursorId;
          }
          if (tempComment) {
            tempComment.id = id;
            tempComment.cursorId = cursorId;
          }
        }
      })
      .addCase(fetchCommentsForAnswer.fulfilled, (state, action) => {
        const { answerId } = action.meta.arg;
        const { data, total, nextCursorId } = action.payload;

        if (state.answerComments[answerId] && action.meta.arg.cursorId) {
          state.answerComments[answerId].comments.push(...data);
        } else {
          state.answerComments[answerId] = {
            comments: data,
            total,
            nextCursorId,
          };
        }

        data.forEach((comment) => {
          if (comment.replies && comment.replies.length > 0) {
            state.answerCommentReplies[comment.id] = {
              comments: comment.replies,
              total: comment.replyCount || comment.replies.length,
              nextCursorId: null,
            };
          }
        });
      })
      .addCase(fetchAnswerCommentReplies.fulfilled, (state, action) => {
        const { parentCommentId } = action.meta.arg;
        const { data, total, nextCursorId } = action.payload;

        state.answerCommentReplies[parentCommentId] = {
          comments: data,
          total,
          nextCursorId,
        };
      })
      .addCase(createForumAnswerComment.fulfilled, (state, action) => {
        const { tempId, id, cursorId } = action.payload;
        const { answerId, parentCommentId } = action.meta.arg;

        if (parentCommentId) {
          const tempReply = state.answerCommentReplies[parentCommentId]?.comments.find(
            (c) => c.id === tempId,
          );
          if (tempReply) {
            tempReply.id = id;
            tempReply.cursorId = cursorId;
          }
        } else {
          const tempComment = state.answerComments[answerId]?.comments.find((c) => c.id === tempId);
          const tempReply = state.answerCommentReplies[tempId]?.comments.find(
            (r) => r.id === tempId,
          );
          if (tempReply) {
            tempReply.id = id;
            tempReply.cursorId = cursorId;
          }
          if (tempComment) {
            tempComment.id = id;
            tempComment.cursorId = cursorId;
          }
        }
      })
      .addCase(deleteAnswer.pending, (state) => {
        state.answerLoading = true;
        state.answerError = null;
      })
      .addCase(deleteAnswer.fulfilled, (state) => {
        state.answerLoading = false;
      })
      .addCase(deleteAnswer.rejected, (state, action) => {
        state.answerLoading = false;
        state.answerError = action.payload as string;
      })
      .addCase(updateAnswerStatus.pending, (state) => {
        state.answerLoading = true;
        state.answerError = null;
      })
      .addCase(updateAnswerStatus.fulfilled, (state) => {
        state.answerLoading = false;
      })
      .addCase(updateAnswerStatus.rejected, (state, action) => {
        state.answerLoading = false;
        state.answerError = action.payload as string;
      });
  },
});

export const {
  incrementCommentCount,
  decrementCommentCount,
  addCommentOptimistic,
  removeCommentOptimistic,
  addAnswerCommentOptimistic,
  removeAnswerCommentOptimistic,
} = forumSlice.actions;
export const {
  resetForumState,
  addAnswerOptimistic,
  addAnswerVoteOptimistic,
  removeAnswerVoteOptimistic,
  switchAnswerVoteOptimistic,
  removeAnswerOptimistic,
  updateAnswerStatusOptimistic,
} = forumSlice.actions;
export default forumSlice.reducer;
