import { SearchResultI } from '@/src/redux/slices/entitysearch/types';

/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
export type ShipDetailsI = {
  id: string;
  name: string;
  type: string;
  period: string;
  subVesselType: SearchResultI;
  fromDate: string;
  toDate: string;
  ship: {
    imo: string;
    name: string;
    dataType: string;
  };
};
