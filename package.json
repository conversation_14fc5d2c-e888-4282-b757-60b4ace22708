{"name": "navicater", "version": "0.0.4", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "lint:fix": "eslint --fix .", "prettier": "prettier --check .", "prettier:fix": "prettier --write .", "fmt": "npm run prettier:fix", "start": "react-native start", "clean": "rimraf android/app/build && rimraf ios/build", "clean:metro": "rimraf $TMPDIR/metro-* || rimraf %TEMP%\\metro-*", "clean:all": "npm run clean && npm run clean:metro && watchman watch-del-all && rimraf node_modules && npm install", "reset": "react-native start --reset-cache", "build:android": "cd android && ./gradlew assembleRelease", "build:android:win": "cd android && gradlew.bat assembleRelease", "build:ios": "cd ios && xcodebuild -workspace navicater.xcworkspace -scheme navicater -configuration Release", "postinstall": "patch-package", "pod-install": "cd ios && pod install", "pod-update": "cd ios && pod update", "doctor": "react-native doctor", "debug": "react-native start --verbose"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}, "dependencies": {"@babel/plugin-transform-export-namespace-from": "^7.27.1", "@hookform/resolvers": "^5.0.1", "@invertase/react-native-apple-authentication": "^2.4.1", "@notifee/react-native": "^9.1.8", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-clipboard/clipboard": "^1.16.2", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-documents/picker": "^10.1.3", "@react-native-documents/viewer": "^1.0.1", "@react-native-firebase/app": "^22.2.0", "@react-native-firebase/messaging": "^22.2.0", "@react-native-google-signin/google-signin": "^13.2.0", "@react-native/gradle-plugin": "^0.78.2", "@react-navigation/bottom-tabs": "^7.3.1", "@react-navigation/elements": "^2.2.6", "@react-navigation/native": "^7.0.15", "@react-navigation/native-stack": "^7.2.1", "@react-navigation/stack": "^7.1.2", "@reduxjs/toolkit": "^2.6.1", "@sentry/react-native": "^6.13.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "babel-plugin-transform-remove-console": "^6.9.4", "jwt-decode": "^4.0.0", "lottie-react-native": "^7.2.2", "markdown-it": "^14.1.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-hook-form": "^7.54.2", "react-native": "^0.78.0", "react-native-audio-recorder-player": "^3.6.12", "react-native-bouncy-checkbox": "^4.1.2", "react-native-compressor": "^1.11.0", "react-native-config": "^1.5.5", "react-native-encrypted-storage": "^4.0.3", "react-native-gesture-handler": "^2.24.0", "react-native-image-crop-picker": "^0.42.0", "react-native-linear-gradient": "^2.8.3", "react-native-markdown-display": "^7.0.2", "react-native-modal": "^14.0.0-rc.1", "react-native-reanimated": "^3.17.3", "react-native-responsive-fontsize": "^0.5.1", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.9.2", "react-native-sse": "^1.2.1", "react-native-svg": "^15.11.2", "react-native-toast-message": "^2.2.1", "react-native-url-polyfill": "^2.0.0", "react-native-video": "^6.14.1", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.0.2", "zod": "^3.24.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native-community/eslint-config": "^3.2.0", "@react-native/babel-preset": "0.78.0", "@react-native/eslint-config": "0.78.0", "@react-native/metro-config": "0.78.0", "@react-native/typescript-config": "^0.78.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/jest": "^29.5.14", "@types/react": "^19.0.0", "@types/react-native": "^0.72.8", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.57.1", "eslint-plugin-header": "^3.1.1", "eslint-plugin-prettier": "^5.2.3", "husky": "^9.1.7", "lint-staged": "^15.4.3", "patch-package": "^8.0.0", "prettier": "^3.5.3", "react-native-svg-transformer": "^1.5.0", "rimraf": "^5.0.5", "tailwindcss": "^3.4.17", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}