import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const Microphone: React.FC<FilledIconPropsI> = ({
  width = 16,
  height = 22,
  color = 'black',
  fill,
  containerStyle,
  accessibilityLabel = 'Microphone',
  ...props
}) => {
  const fillColor = fill || color;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 16 22"
      fill="none"
      style={containerStyle}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        d="M8 15C9.19309 14.9988 10.337 14.5243 11.1806 13.6806C12.0243 12.837 12.4988 11.6931 12.5 10.5V4.5C12.5 3.30653 12.0259 2.16193 11.182 1.31802C10.3381 0.474106 9.19347 0 8 0C6.80653 0 5.66193 0.474106 4.81802 1.31802C3.97411 2.16193 3.5 3.30653 3.5 4.5V10.5C3.50124 11.6931 3.97575 12.837 4.81939 13.6806C5.66303 14.5243 6.80691 14.9988 8 15ZM5 4.5C5 3.70435 5.31607 2.94129 5.87868 2.37868C6.44129 1.81607 7.20435 1.5 8 1.5C8.79565 1.5 9.55871 1.81607 10.1213 2.37868C10.6839 2.94129 11 3.70435 11 4.5V10.5C11 11.2956 10.6839 12.0587 10.1213 12.6213C9.55871 13.1839 8.79565 13.5 8 13.5C7.20435 13.5 6.44129 13.1839 5.87868 12.6213C5.31607 12.0587 5 11.2956 5 10.5V4.5ZM8.75 17.9625V21C8.75 21.1989 8.67098 21.3897 8.53033 21.5303C8.38968 21.671 8.19891 21.75 8 21.75C7.80109 21.75 7.61032 21.671 7.46967 21.5303C7.32902 21.3897 7.25 21.1989 7.25 21V17.9625C5.40091 17.7743 3.68728 16.9072 2.44048 15.5288C1.19368 14.1504 0.502284 12.3586 0.5 10.5C0.5 10.3011 0.579018 10.1103 0.71967 9.96967C0.860322 9.82902 1.05109 9.75 1.25 9.75C1.44891 9.75 1.63968 9.82902 1.78033 9.96967C1.92098 10.1103 2 10.3011 2 10.5C2 12.0913 2.63214 13.6174 3.75736 14.7426C4.88258 15.8679 6.4087 16.5 8 16.5C9.5913 16.5 11.1174 15.8679 12.2426 14.7426C13.3679 13.6174 14 12.0913 14 10.5C14 10.3011 14.079 10.1103 14.2197 9.96967C14.3603 9.82902 14.5511 9.75 14.75 9.75C14.9489 9.75 15.1397 9.82902 15.2803 9.96967C15.421 10.1103 15.5 10.3011 15.5 10.5C15.4977 12.3586 14.8063 14.1504 13.5595 15.5288C12.3127 16.9072 10.5991 17.7743 8.75 17.9625Z"
        fill={fillColor}
      />
    </Svg>
  );
};

export default Microphone;
