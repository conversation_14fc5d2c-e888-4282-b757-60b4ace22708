export interface LocationCoordinates {
  latitude?: string | null;
  longitude?: string | null;
}

export interface PortCapacity {
  noOfTerminals?: number | null;
  noOfBerths?: number | null;
  maxDraught?: number | null;
  maxDeadweight?: number | null;
  maxLength?: number | null;
  maxAirDraught?: number | null;
}

export interface CityResult {
  name: string;
}

export interface CountryResult {
  name: string;
}

export interface TimezoneResult {
  timezone: string;
  utcOffset: number;
  dstOffset: number;
}

export interface PortProfileData extends LocationCoordinates, PortCapacity {
  unLocode?: string;
  name?: string;
  imageUrl?: string | null;
  city?: CityResult | null;
  country?: CountryResult | null;
  dataType: 'master' | 'raw';
  isVisited: boolean;
  timezone?: TimezoneResult | null;
}

export interface PortAboutProps {
  portData: PortProfileData | null;
}
