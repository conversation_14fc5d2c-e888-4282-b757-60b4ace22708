import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { selectAboutProfileDocumentsVisaCount } from '@/src/redux/selectors/about';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import { fetchVisaDocumentationsAPI } from '@/src/networks/profile/visaDocumentations';
import { ProfileVisaDocumentationI } from './types';

export const useProfileVisa = (profileId: string) => {
  const [documentations, seDocumentations] = useState<ProfileVisaDocumentationI[]>([]);
  const count = useSelector(selectAboutProfileDocumentsVisaCount);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchDocumentations = async () => {
      try {
        setLoading(true);
        const response = await fetchVisaDocumentationsAPI(profileId);
        seDocumentations(response);
      } catch (error) {
        handleError(error, {
          handle4xxError: () => {
            showToast({
              message: 'Failed to Fetch Documents',
              type: 'error',
            });
          },
          handle5xxError: () => {
            showToast({
              message: 'Server Error',
              description: 'Please try again later',
              type: 'error',
            });
          },
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDocumentations();
  }, [profileId]);

  return {
    documentations,
    count,
    loading,
  };
};
