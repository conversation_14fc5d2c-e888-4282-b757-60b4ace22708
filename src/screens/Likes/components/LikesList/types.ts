/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { ListItem } from '@/src/components/UsersList/types';
import { ReactionExternalClientI } from '@/src/networks/content/types';

export interface LikesListProps {
  postId: string;
  onUserPress?: (item: ListItem) => void;
}

export interface UseLikesListResult {
  reactions: ReactionExternalClientI;
  loading: boolean;
  refreshing: boolean;
  handleRefresh: () => Promise<void>;
  handleLoadMore: () => Promise<void>;
}
