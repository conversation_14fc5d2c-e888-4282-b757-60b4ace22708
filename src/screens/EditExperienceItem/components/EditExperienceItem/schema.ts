import { z } from 'zod';

export const EntitySchema = z.object({
  id: z.string().optional(),
  name: z.string(),
  dataType: z.string().optional(),
});

const getTodayString = () => new Date().toISOString().split('T')[0];

const DesignationSchema = z
  .object({
    id: z.string().optional(),
    designation: z.any().optional(),
    name: z.string().optional(),
    experienceDesignationId: z.string().optional(),
    fromDate: z.string().min(1, 'From Date is required'),
    toDate: z.string().optional(),
    isPresent: z.boolean().optional(),
    ships: z.array(z.any()),
  })
  .superRefine((data, ctx) => {
    if (!data.isPresent) {
      if (!data.toDate || data.toDate.trim() === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'To Date is required when "Present" is not checked',
          path: ['toDate'],
        });
        return;
      }

      if (data.fromDate && data.toDate) {
        const fromDate = new Date(data.fromDate);
        const toDate = new Date(data.toDate);
        if (fromDate > toDate) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'To date must be later than or equal to From date',
            path: ['toDate'],
          });
        }
      }

      if (data.toDate) {
        const toDate = new Date(data.toDate);
        const today = new Date(getTodayString());
        if (toDate > today) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'To date cannot be in the future',
            path: ['toDate'],
          });
        }
      }
    }
  });

export const editExperienceSchema = z.object({
  company: EntitySchema.optional(),
  designations: z.array(DesignationSchema),
});

export type ExperienceFormDataI = z.infer<typeof editExperienceSchema>;
