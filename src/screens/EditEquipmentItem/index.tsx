import { KeyboardAvoidingView, Platform } from 'react-native';
import { type RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import SafeArea from '@/src/components/SafeArea';
import type { ProfileStackParamsListI } from '@/src/navigation/types';
import EditEquipmentItem from './components/EditEquipmentItem';

type RouteProps = RouteProp<ProfileStackParamsListI, 'EditEquipmentItem'>;

const EditEquipmentItemScreen = () => {
  const route = useRoute<RouteProps>();
  const { profileId, experienceId, shipId, equipmentId, data, refetch, isClientMode, shipTempId } =
    route.params || {};
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();

  return (
    <SafeArea>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <EditEquipmentItem
          onBack={navigation.goBack}
          profileId={profileId}
          experienceId={experienceId}
          shipId={shipId!}
          equipmentId={equipmentId!}
          preFilledData={data}
          refetch={refetch}
          isClientMode={isClientMode}
          shipTempId={shipTempId}
        />
      </KeyboardAvoidingView>
    </SafeArea>
  );
};

export default EditEquipmentItemScreen;
