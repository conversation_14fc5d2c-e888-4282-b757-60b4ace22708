/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
export type CompanyExperienceI = {
  company: string;
  duration: string;
  positions: PositionI[];
};

export type PositionI = {
  title: string;
  period: string;
  ships: ShipI[];
};

export type ShipI = {
  name: string;
  type: string;
  period: string;
};
