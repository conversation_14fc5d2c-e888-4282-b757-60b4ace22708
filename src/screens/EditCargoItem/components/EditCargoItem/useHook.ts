import { useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import {
  addCargoShipExperience,
  addClientCargo,
  updateClientCargo,
} from '@/src/redux/slices/experience/experienceSlice';
import { ClientCargoI } from '@/src/redux/slices/experience/types';
import { AppDispatch, RootState } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import { HomeStackParamListI } from '@/src/navigation/types';
import { ShipCreateEditPayloadI } from '@/src/screens/EditShipItem/components/EditShipItem/types';
import { fetchSingleShipCargo } from '@/src/networks/experienceShip.ts/ship';
import { generatePayloadCargo } from '../utils';
import { CargoCreateUpdatePayloadI, CargoDetailsFormDataI, UseEditCargoItemI } from './types';

export const useEditCargoItem = (
  cargoId?: string,
  preFilledData?: ShipCreateEditPayloadI[],
  refetch?: () => void,
  isClientMode: boolean = false,
  onClientSave?: (cargo: any) => void,
  shipTempId?: string,
): UseEditCargoItemI & {
  handleClientSave: (data: CargoDetailsFormDataI) => void;
  isSaving: boolean;
} => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const navigation = useNavigation<StackNavigationProp<HomeStackParamListI>>();
  const dispatch = useDispatch<AppDispatch>();
  const clientCargos = useSelector((state: RootState) => state.experience.clientCargos);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) throw error;

  const methods = useForm<CargoDetailsFormDataI>({
    mode: 'onChange',
    defaultValues: {
      name: '',
      description: '',
      fromDate: '',
      toDate: '',
    },
  });

  const hasChanges = methods.formState.isDirty;

  useEffect(() => {
    if (!cargoId) return;

    if (isClientMode) {
      const clientCargo = clientCargos.find((cargo) => cargo.tempId === cargoId);
      if (clientCargo) {
        const cargoData: CargoDetailsFormDataI = {
          name: clientCargo.name,
          description: clientCargo.description,
          fromDate: clientCargo.fromDate,
          toDate: clientCargo.toDate,
        };
        methods.reset(cargoData);
      }
    } else {
      const fetchCargo = async () => {
        try {
          setLoading(true);
          const response = await fetchSingleShipCargo(cargoId);

          const fetchedCargo: CargoDetailsFormDataI = {
            name: response.name,
            description: response.description,
            fromDate: response.fromDate,
            toDate: response.toDate,
          };

          methods.reset(fetchedCargo);
        } catch (error) {
          triggerErrorBoundary(
            new Error(
              `Failed to load cargo details: ${error instanceof Error ? error.message : 'Unknown error'}`,
            ),
          );
        } finally {
          setLoading(false);
        }
      };

      fetchCargo();
    }
  }, [cargoId, isClientMode, clientCargos, methods]);

  const onSubmit = async (data: CargoDetailsFormDataI) => {
    if (!preFilledData) return;

    const payload: CargoCreateUpdatePayloadI = generatePayloadCargo(data, preFilledData, cargoId);

    try {
      setIsSubmitting(true);
      setIsSaving(true);
      await dispatch(addCargoShipExperience({ payload })).unwrap();

      showToast({
        message: 'Success',
        description: 'Added new Cargo successfully',
        type: 'success',
      });

      methods.reset(methods.getValues());
      setTimeout(() => {
        navigation.goBack();
      }, 100);
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Failed to Save Cargo',
            description: 'Unable to save Cargo',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsSubmitting(false);
      setIsSaving(false);
      if (refetch) {
        refetch();
      }
    }
  };

  const handleClientSave = (data: CargoDetailsFormDataI) => {
    setIsSaving(true);

    const cargoData: ClientCargoI = {
      tempId: cargoId || `cargo_${Date.now()}_${Math.random()}`,
      shipTempId: shipTempId || '',
      name: data.name,
      description: data.description,
      fromDate: data.fromDate,
      toDate: data.toDate,
    };

    if (cargoId) {
      dispatch(updateClientCargo({ tempId: cargoId, cargo: cargoData }));
    } else {
      dispatch(addClientCargo(cargoData));
    }

    if (onClientSave) {
      onClientSave(cargoData);
    }

    showToast({
      message: 'Success',
      description: cargoId ? 'Cargo updated successfully' : 'Cargo added successfully',
      type: 'success',
    });

    methods.reset(methods.getValues());
    setTimeout(() => {
      setIsSaving(false);
      navigation.goBack();
    }, 100);
  };

  return {
    methods,
    isSubmitting,
    onSubmit,
    navigation,
    loading,
    hasChanges,
    handleClientSave,
    isSaving,
  };
};
