import { FlatList, Text, View } from 'react-native';
import CommunityItem from '@/src/screens/MyCommunities/components/CommunityItem';
import { CommunityI } from '@/src/screens/MyCommunities/components/CommunityList/types';

const sampleCommunities: CommunityI[] = [
  {
    id: '1',
    name: 'Community Name',
    memberCount: 123456,
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Morbi eleifend',
    avatar:
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
  },
  {
    id: '2',
    name: 'Community Name',
    memberCount: 123456,
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Morbi eleifend',
    avatar:
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
  },
  {
    id: '3',
    name: 'Community Name',
    memberCount: 123456,
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Morbi eleifend',
    avatar:
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
  },
  {
    id: '4',
    name: 'Community Name',
    memberCount: 123456,
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Morbi eleifend',
    avatar:
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
  },
];

const renderCommunityItem = ({ item }: { item: CommunityI }) => (
  <CommunityItem
    community={item}
    // onPress={() => onCommunityPress?.(item)}
  />
);

const RecommendedCommunities = () => {
  return (
    <View className="px-4">
      <Text className="text-base font-medium leading-5">Recommended communities for you </Text>

      <FlatList
        data={sampleCommunities}
        renderItem={renderCommunityItem}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        className="flex-1"
      />
    </View>
  );
};

export default RecommendedCommunities;
