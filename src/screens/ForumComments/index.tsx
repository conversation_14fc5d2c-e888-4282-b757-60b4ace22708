/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import CommentInput from './CommentInput';
import CommentsList from './CommentsList';
import { ForumCommentTypeI } from './types';

const ForumCommentsScreen = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const route = useRoute<RouteProp<LearnCollabStackParamsListI, 'ForumComments'>>();
  const { postId, type } = route.params;

  const [replyPreview, setReplyPreview] = useState<ForumCommentTypeI | null>(null);
  const commentsListRef = useRef<{
    refresh: () => void;
  }>(null);

  const handleReply = (comment: ForumCommentTypeI) => {
    setReplyPreview(comment);
  };

  return (
    <SafeArea>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <KeyboardAvoidingView
          className="flex-1"
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 10 : 20}
        >
          <View className="flex-row items-center px-4">
            <BackButton onBack={() => navigation.goBack()} label="" />
            <Text className="text-xl font-medium">Comments</Text>
          </View>

          <CommentsList ref={commentsListRef} postId={postId} type={type} onReply={handleReply} />
          <View>
            <CommentInput
              postId={postId}
              type={type}
              parentCommentId={replyPreview?.id}
              replyPreview={replyPreview}
              setReplyPreview={setReplyPreview}
            />
          </View>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </SafeArea>
  );
};

export default ForumCommentsScreen;
