import { Dispatch, SetStateAction } from 'react';

export interface UseEditSkillsListI {
  maritimeSkills: SkillI[];
  otherSkills: SkillI[];
  isSubmitting: boolean;
  handleSubmit: () => void;
  setMaritimeSkills: Dispatch<SetStateAction<SkillI[]>>;
  setOtherSkills: Dispatch<SetStateAction<SkillI[]>>;
  loading: boolean;
  loadingMore: boolean;
  loadMoreMaritime: () => void;
  loadMoreOther: () => void;
  hasMoreMaritime: boolean;
  hasMoreOther: boolean;
  activeTab: string;
  setActiveTab: Dispatch<SetStateAction<string>>;
  hasChanges: boolean;
}

export interface EditSkillsListPropsI {
  onBack: () => void;
  profileId: string;
  category: string;
}

export interface SkillI {
  id: string;
  name: string;
  dataType: string;
  category: string;
}
