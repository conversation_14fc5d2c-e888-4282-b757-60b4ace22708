/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { Image, getFileSize } from 'react-native-compressor';

export const compressToTargetSize = async (uri: string, targetSizeKB: number): Promise<string> => {
  const initialSize = await getFileSize(uri);
  const targetSizeBytes = targetSizeKB * 1024;

  if (Number(initialSize) <= targetSizeBytes) {
    return uri;
  }

  let quality = 0.9;
  let compressedUri = uri;
  let currentSize = initialSize;
  const minQuality = 0.1;

  while (Number(currentSize) > targetSizeBytes && quality > minQuality) {
    const sizeRatio = Number(currentSize) / targetSizeBytes;
    const qualityReduction = Math.min(0.1, Math.max(0.05, (0.1 * (sizeRatio - 1)) / 3));
    quality = Math.max(minQuality, quality - qualityReduction);
    try {
      compressedUri = await Image.compress(uri, {
        quality,
        maxWidth: 1000,
        maxHeight: 1000,
        output: 'jpg',
      });

      currentSize = await getFileSize(compressedUri);
    } catch (_error) {
      return compressedUri;
    }
  }

  return compressedUri;
};
