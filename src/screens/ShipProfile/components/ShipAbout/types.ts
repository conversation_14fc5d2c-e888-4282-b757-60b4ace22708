export interface ShipProfileData {
  imo: string;
  mmsi: number;
  callSign: string;
  name: string;
  imageUrl: string;
  yearBuilt: number;
  country: {
    name: string;
  };
  mainVesselType: {
    name: string;
  };
  subVesselTypes: Array<{
    name: string;
  }>;
  dataType: string;
  shipNames: Array<{
    name: string;
    fromDate: string | null;
    toDate: string | null;
  }>;
}

export interface ShipAboutProps {
  shipData: ShipProfileData | null;
}
