/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const Comment: React.FC<FilledIconPropsI> = ({
  width = 2.108,
  height = 1.86,
  fill = '#000',
  color,
  disabled,
  accessibilityLabel = 'Comment',
  ...props
}) => {
  const fillColor = color || fill;
  const opacity = disabled ? 0.5 : 1;

  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 17 15"
      fill="none"
      opacity={opacity}
      accessibilityLabel={accessibilityLabel}
      {...props}
    >
      <Path
        d="M12.7668 12.0676C13.9747 11.5851 15.0409 10.8017 15.865 9.791C16.5979 8.91831 17 7.81291 17 6.67064C17 5.52838 16.5979 4.42298 15.865 3.55029C15.0384 2.54541 13.9727 1.76687 12.7668 1.28699C11.4132 0.730742 9.9623 0.453444 8.50015 0.471535C7.03687 0.458721 5.58595 0.741686 4.23353 1.30363C3.02764 1.78351 1.96192 2.56205 1.13534 3.56693C0.40809 4.4436 0.00671415 5.54733 0 6.68895C0.00466367 7.66186 0.303677 8.61031 0.857297 9.40824C1.46794 10.2923 2.27042 11.0253 3.20411 11.5517C3.12136 11.7481 3.03861 11.9245 2.95585 12.0876C2.86836 12.251 2.76762 12.4068 2.65464 12.5536C2.53548 12.7033 2.44611 12.8232 2.38322 12.9064C2.32033 12.9896 2.2111 13.1194 2.0787 13.2658C1.9463 13.4123 1.84038 13.5188 1.80066 13.5687C1.80066 13.5687 1.80065 13.5687 1.751 13.622C1.70135 13.6752 1.70135 13.6885 1.69804 13.6819C1.69473 13.6752 1.67818 13.6985 1.64839 13.7451L1.60536 13.8117L1.57557 13.8716C1.56441 13.8948 1.55661 13.9194 1.5524 13.9448C1.55062 13.9703 1.55062 13.9959 1.5524 14.0214C1.54359 14.0528 1.54246 14.0859 1.54909 14.1179C1.56254 14.2176 1.61203 14.3088 1.68811 14.3742C1.75677 14.4351 1.84482 14.4693 1.93637 14.4707H1.96947C2.31916 14.424 2.66611 14.3585 3.00881 14.2743C4.21242 13.9678 5.34998 13.4421 6.36518 12.7233C7.06672 12.8488 7.77768 12.9134 8.49022 12.9163C9.95817 12.9273 11.4131 12.6386 12.7668 12.0676Z"
        fill={fillColor}
      />
    </Svg>
  );
};

export default Comment;
