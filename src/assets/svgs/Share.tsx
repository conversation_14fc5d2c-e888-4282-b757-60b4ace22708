/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { FilledIconPropsI } from './types';

const Share: React.FC<FilledIconPropsI> = ({ width = 1.488, height = 1.86, color, ...props }) => {
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 12 15"
      fill="none"
      {...props}
    >
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.27271 2.80403C7.27271 1.51537 8.33097 0.470703 9.63635 0.470703C10.9417 0.470703 12 1.51537 12 2.80403C12 4.0927 10.9417 5.13737 9.63635 5.13737C8.9772 5.13737 8.38151 4.87087 7.95325 4.44213L4.6812 6.65135C4.71141 6.80045 4.72729 6.95445 4.72729 7.11174C4.72729 7.42324 4.66518 7.72102 4.55265 7.99318L8.14045 10.3308C8.54767 10.002 9.06868 9.80401 9.63635 9.80401C10.9417 9.80401 12 10.8487 12 12.1374C12 13.426 10.9417 14.4707 9.63635 14.4707C8.33097 14.4707 7.27271 13.426 7.27271 12.1374C7.27271 11.7999 7.34555 11.4786 7.47649 11.1885L3.9177 8.86979C3.50256 9.22756 2.95878 9.44505 2.36363 9.44505C1.05824 9.44505 0 8.40037 0 7.11174C0 5.82306 1.05824 4.7784 2.36363 4.7784C3.11428 4.7784 3.78249 5.12378 4.21514 5.66133L7.38628 3.5202C7.31252 3.29422 7.27271 3.05342 7.27271 2.80403Z"
        fill={color || '#525252'}
      />
    </Svg>
  );
};

export default Share;
