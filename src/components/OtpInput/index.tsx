import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import {
  NativeSyntheticEvent,
  TextInput as RNTextInput,
  Text,
  TextInputKeyPressEventData,
  View,
  Platform,
} from 'react-native';
import { twMerge } from 'tailwind-merge';
import TextView from '../TextView';
import { OTPInputHandle, OTPInputPropsI } from './types';

const OTPInput = forwardRef<OTPInputHandle, OTPInputPropsI>(
  ({ title, subtitle, value = '', onChange, length = 6, autoFocus = true }, ref) => {
    const inputRefs = useRef<Array<React.MutableRefObject<RNTextInput | null>>>(
      Array.from({ length }, () => ({ current: null })),
    );
    const [isInvalid, setIsInvalid] = useState(false);

    const safeValue = value || '';

    useImperativeHandle(ref, () => ({
      setInvalidOTP: () => setIsInvalid(true),
      clear: () => {
        onChange('');
        setIsInvalid(false);
        inputRefs.current[0].current?.focus();
      },
    }));

    const distributeOTP = (otp: string, startIndex = 0) => {
      const digits = otp.replace(/\D/g, '').slice(0, length - startIndex);
      const currentValues = safeValue.split('');

      for (let i = 0; i < digits.length && startIndex + i < length; i++) {
        currentValues[startIndex + i] = digits[i];
      }

      const newValue = currentValues.join('');
      onChange(newValue);

      const nextFocusIndex = Math.min(startIndex + digits.length, length - 1);
      if (nextFocusIndex < length && digits.length > 0) {
        setTimeout(() => {
          inputRefs.current[nextFocusIndex].current?.focus();
        }, 10);
      }

      setIsInvalid(false);
    };

    const handleChangeText = (text: string, index: number) => {
      if (text.length > 1) {
        distributeOTP(text, index);
        return;
      }

      if (text.length === 1 && /^\d$/.test(text)) {
        const newValue = safeValue.split('');
        newValue[index] = text;
        const updatedValue = newValue.join('');
        onChange(updatedValue);

        if (index < length - 1) {
          inputRefs.current[index + 1].current?.focus();
        }
        setIsInvalid(false);
      } else if (text.length === 0) {
        const newValue = safeValue.split('');
        newValue[index] = '';
        onChange(newValue.join(''));
        setIsInvalid(false);
      }
    };

    const handleKeyPress = (e: NativeSyntheticEvent<TextInputKeyPressEventData>, index: number) => {
      if (e.nativeEvent.key === 'Backspace') {
        const currentValue = safeValue[index];

        if (!currentValue && index > 0) {
          const newValue = safeValue.split('');
          newValue[index - 1] = '';
          onChange(newValue.join(''));
          inputRefs.current[index - 1].current?.focus();
        } else if (currentValue) {
          const newValue = safeValue.split('');
          newValue[index] = '';
          onChange(newValue.join(''));
        }
        setIsInvalid(false);
      }
    };

    const handleFocus = () => {
      setIsInvalid(false);
    };

    return (
      <View className="my-8">
        <TextView title={title} subtitle={subtitle} titleClassName="font-bold" />
        <View className="space-y-8 mt-4">
          <View className="flex-row justify-between">
            {Array.from({ length }).map((_, index) => (
              <View key={index} className="w-14">
                <RNTextInput
                  ref={(el) => {
                    inputRefs.current[index].current = el;
                  }}
                  value={safeValue[index] ?? ''}
                  onChangeText={(text) => handleChangeText(text, index)}
                  onKeyPress={(e) => handleKeyPress(e, index)}
                  onFocus={handleFocus}
                  keyboardType="numeric"
                  maxLength={Platform.OS === 'android' ? length : 1}
                  placeholderTextColor="#A3A3A3"
                  style={{
                    color: '#000000',
                  }}
                  className={twMerge(
                    'w-full px-4 py-4 rounded-xl bg-white border border-[#D4D4D4]',
                    'text-center text-xl font-medium',
                    Platform.select({
                      ios: 'leading-6',
                      android: 'leading-5',
                    }),
                    isInvalid && 'border-red-500 bg-red-100',
                    safeValue[index] && 'border-green-800',
                  )}
                  autoFocus={autoFocus && index === 0}
                  selectTextOnFocus={true}
                  textContentType="oneTimeCode"
                />
              </View>
            ))}
          </View>
          {isInvalid && (
            <Text className="mt-2 text-red-500 text-center">
              Invalid OTP! Please check and try again.
            </Text>
          )}
        </View>
      </View>
    );
  },
);

OTPInput.displayName = 'OTPInput';

export default OTPInput;
