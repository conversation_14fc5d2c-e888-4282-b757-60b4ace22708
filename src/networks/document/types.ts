import { IdTypeI } from '@/src/types/common/data';

export interface AddDocumentBodyI {
  documentNo: string;
  fileUrl: string;
  type: 'PASSPORT' | 'CDC' | 'SID';
  countryIso2: string;
  fromDate: Date;
  untilDate: Date;
}

export interface AddDocumentResultI {
  id: string;
  fileUrl: string;
  expiryStatus: 'expired' | 'expires_soon' | 'valid';
}

export interface AddCertificationBodyI {
  institute: IdTypeI;
  certificateCourse: IdTypeI;
  fileUrl?: string;
  fromDate: Date;
  untilDate: Date;
  skills: IdTypeI;
}

export interface AddCertificationResultI {
  id: string;
  fileUrl: string | null;
}
