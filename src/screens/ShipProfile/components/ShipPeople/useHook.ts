import React from 'react';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { sendConnectionRequestAPI } from '@/src/networks/connect/connection';
import { deleteConnectionAPI } from '@/src/networks/connect/connection';
import { respondReceivedRequestAPI } from '@/src/networks/connect/request';
import { fetchShipPeople } from '@/src/networks/ship/people';
import type { FetchPeopleResult } from '@/src/networks/ship/types';
import type {
  FetchPeopleParams,
  UseShipPeopleParams,
  UseShipPeopleReturnI,
  RequestStatusTypeE,
} from './types';

const useShipPeople = ({
  imo,
  dataType,
  pageSize = 10,
  onError,
}: UseShipPeopleParams): UseShipPeopleReturnI => {
  const currentUser = useSelector(selectCurrentUser);

  const [people, setPeople] = React.useState<{ data: FetchPeopleResult[]; total: number }>({
    total: 0,
    data: [],
  });

  const [loading, setLoading] = React.useState<boolean>(true);
  const [loadingMore, setLoadingMore] = React.useState<boolean>(false);
  const [refreshing, setRefreshing] = React.useState<boolean>(false);
  const [hasMore, setHasMore] = React.useState<boolean>(false);
  const [page, setPage] = React.useState<number>(0);
  const [connectionStates, setConnectionStates] = React.useState<
    Record<string, 'idle' | 'loading' | 'success'>
  >({});

  const fetchPeople = async (pageNumber: number, isRefresh = false): Promise<void> => {
    try {
      if (isRefresh) {
        setRefreshing(true);
        setPage(0);
      } else if (pageNumber > 0) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }

      const params: FetchPeopleParams = {
        imo,
        dataType,
        page: pageNumber,
        pageSize,
      };

      const result = await fetchShipPeople({ ...params });

      if (isRefresh || pageNumber === 0) {
        setPeople(result);
      } else {
        setPeople((prevPeople) => ({
          total: result.total,
          data: [...prevPeople.data, ...result.data],
        }));
      }

      setPage(pageNumber);
      setHasMore(result.data.length === pageSize && result.data.length > 0);
    } catch (err) {
      const errorMessage = `Failed to fetch visitors: ${err instanceof Error ? err.message : 'Unknown error'}`;
      if (onError) {
        onError(new Error(errorMessage));
      }
    } finally {
      setLoading(false);
      setLoadingMore(false);
      setRefreshing(false);
    }
  };

  React.useEffect(() => {
    if (imo && dataType) {
      fetchPeople(0);
    }
  }, [imo, dataType]);

  const handleRefresh = (): void => {
    fetchPeople(0, true);
  };

  const handleLoadMore = (): void => {
    if (!loadingMore && !loading && hasMore) {
      fetchPeople(page + 1);
    }
  };

  const handleConnectRequest = async (profileId: string): Promise<void> => {
    try {
      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'loading',
      }));

      await sendConnectionRequestAPI({
        receiverProfileId: profileId,
        requestedStatus: 'PENDING',
      });

      setPeople((prevPeople) => ({
        ...prevPeople,
        data: prevPeople.data.map((person) =>
          person.id === profileId
            ? {
                ...person,
                requestStatus: 'PENDING' as RequestStatusTypeE,
              }
            : person,
        ),
      }));

      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'success',
      }));
    } catch (error) {
      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'idle',
      }));
      const errorMessage = `Failed to send connection request: ${error instanceof Error ? error.message : 'Unknown error'}`;
      if (onError) {
        onError(new Error(errorMessage));
      }
    }
  };

  const handleAcceptRequest = async (profileId: string): Promise<void> => {
    try {
      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'loading',
      }));

      await respondReceivedRequestAPI({
        senderProfileId: profileId,
        requestedStatus: 'ACCEPTED',
      });

      setPeople((prevPeople) => ({
        ...prevPeople,
        data: prevPeople.data.map((person) =>
          person.id === profileId
            ? {
                ...person,
                isConnected: true,
                requestStatus: 'ACCEPTED' as RequestStatusTypeE,
              }
            : person,
        ),
      }));

      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'success',
      }));
    } catch (error) {
      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'idle',
      }));
      const errorMessage = `Failed to accept connection request: ${error instanceof Error ? error.message : 'Unknown error'}`;
      if (onError) {
        onError(new Error(errorMessage));
      }
    }
  };

  const handleRejectRequest = async (profileId: string): Promise<void> => {
    try {
      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'loading',
      }));

      await respondReceivedRequestAPI({
        senderProfileId: profileId,
        requestedStatus: 'REJECTED',
      });

      setPeople((prevPeople) => ({
        ...prevPeople,
        data: prevPeople.data.map((person) =>
          person.id === profileId
            ? {
                ...person,
                requestStatus: 'REJECTED' as RequestStatusTypeE,
              }
            : person,
        ),
      }));

      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'success',
      }));
    } catch (error) {
      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'idle',
      }));
      const errorMessage = `Failed to reject connection request: ${error instanceof Error ? error.message : 'Unknown error'}`;
      if (onError) {
        onError(new Error(errorMessage));
      }
    }
  };

  const handleRevokeRequest = async (profileId: string): Promise<void> => {
    try {
      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'loading',
      }));

      await sendConnectionRequestAPI({
        receiverProfileId: profileId,
        requestedStatus: 'REVOKED',
      });

      setPeople((prevPeople) => ({
        ...prevPeople,
        data: prevPeople.data.map((person) =>
          person.id === profileId
            ? {
                ...person,
                requestStatus: 'REVOKED' as RequestStatusTypeE,
                isConnected: false,
              }
            : person,
        ),
      }));

      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'success',
      }));
    } catch (error) {
      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'idle',
      }));
      const errorMessage = `Failed to revoke connection request: ${error instanceof Error ? error.message : 'Unknown error'}`;
      if (onError) {
        onError(new Error(errorMessage));
      }
    }
  };

  const handleDisconnectRequest = async (profileId: string): Promise<void> => {
    try {
      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'loading',
      }));

      await deleteConnectionAPI({ profileId: profileId });

      setPeople((prevPeople) => ({
        ...prevPeople,
        data: prevPeople.data.map((person) =>
          person.id === profileId
            ? {
                ...person,
                requestStatus: 'DISCONNECTED' as RequestStatusTypeE,
                isConnected: false,
              }
            : person,
        ),
      }));

      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'success',
      }));
    } catch (error) {
      setConnectionStates((prevStates) => ({
        ...prevStates,
        [profileId]: 'idle',
      }));
      const errorMessage = `Failed to disconnect: ${error instanceof Error ? error.message : 'Unknown error'}`;
      if (onError) {
        onError(new Error(errorMessage));
      }
    }
  };

  return {
    people,
    loading,
    loadingMore,
    hasMore,
    refreshing,
    connectionStates,
    handleRefresh,
    handleLoadMore,
    handleConnectRequest,
    handleAcceptRequest,
    handleRejectRequest,
    handleRevokeRequest,
    handleDisconnectRequest,
  };
};

export default useShipPeople;
