import { useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { isRejectedWithValue } from '@reduxjs/toolkit';
import { useDispatch, useSelector } from 'react-redux';
import { selectCommunity, selectCommunityLoading } from '@/src/redux/selectors/community';
import { selectProfileId } from '@/src/redux/selectors/user';
import {
  createCommunityAsync,
  setCommunityAvatar,
  setIsLoading,
  setMembers,
  setModerators,
} from '@/src/redux/slices/community/communitySlice';
import { AppDispatch } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import { uploadFileWithPresignedUrl } from '@/src/utilities/upload/upload';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import { fetchConnectionsAPI } from '@/src/networks/connect/connection';
import { FetchFollowersDataI } from '@/src/networks/connect/types';
import { AuthorProfileI } from '@/src/networks/content/types';
import { fetchPresignedUrlAPI } from '@/src/networks/storage/presignedUrl';

const usePeopleList = (type: 'create' | 'add', searchQuery: string) => {
  const profileId = useSelector(selectProfileId);
  const [people, setPeople] = useState<FetchFollowersDataI[]>([]);
  const [hasMore, setHasMore] = useState(false);
  const [nextCursorId, setNextCursorId] = useState<number | null>(null);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const loading = useSelector(selectCommunityLoading);
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const dispatch = useDispatch<AppDispatch>();
  const communityState = useSelector(selectCommunity);

  const fetchData = async (cursorId?: number | null, isLoadMore = false) => {
    try {
      if (isLoadMore) {
        setIsLoadingMore(true);
      }

      const response = await fetchConnectionsAPI({
        profileId,
        cursorId: cursorId || null,
        pageSize: 10,
        name: searchQuery,
      });

      if (isLoadMore) {
        setPeople((prev) => [...prev, ...response.data]);
      } else {
        setPeople(response.data);
      }

      setHasMore(Boolean(response.hasMore));
      setNextCursorId(response.nextCursorId || null);
    } catch (error) {
      console.error('Error fetching people:', error);
    } finally {
      if (isLoadMore) {
        setIsLoadingMore(false);
      }
    }
  };

  const loadMore = () => {
    if (hasMore && !isLoadingMore && nextCursorId) {
      fetchData(nextCursorId, true);
    }
  };

  useEffect(() => {
    try {
      fetchData(null, false);
    } catch (err) {
      showToast({
        type: 'error',
        message: 'Failed to fetch people',
      });
    }
  }, [searchQuery]);

  const uploadAvatarAndGetUrl = async (): Promise<string | null> => {
    const avatar = communityState.avatar;

    if (!avatar?.image) {
      return null;
    }

    if (avatar.url) {
      return avatar.url;
    }

    if (avatar.image && !avatar.url) {
      try {
        dispatch(setIsLoading(true));
        let extension = 'jpeg';

        if (avatar.image.mime) {
          extension = avatar.image.mime.split('/')[1];
        } else if (avatar.image.path) {
          const pathParts = avatar.image.path.split('.');
          extension = pathParts.length > 1 ? pathParts[pathParts.length - 1].toLowerCase() : 'jpeg';
        }

        const response = await fetchPresignedUrlAPI([extension], 'AVATAR');

        if (Array.isArray(response) && response.length > 0) {
          const presignedData = response[0];

          const fileToUpload = {
            uri: avatar.image.path,
            type: avatar.image.mime || `image/${extension}`,
            filename: avatar.image.filename || `community-avatar.${extension}`,
          };

          await uploadFileWithPresignedUrl(fileToUpload, presignedData.uploadUrl);
          return presignedData.accessUrl;
        }
      } catch (error) {
        console.error('Error uploading avatar:', error);
        showToast({
          type: 'error',
          message: 'Error uploading avatar.',
          description: 'Please try again',
        });
        dispatch(setIsLoading(false));
        throw error;
      }
    }

    return null;
  };

  const handleSubmit = async () => {
    if (type === 'create') {
      try {
        const avatarUrl = await uploadAvatarAndGetUrl();
        if (avatarUrl) {
          dispatch(setCommunityAvatar(avatarUrl));
        }
        const result = await dispatch(createCommunityAsync({}));
        if (isRejectedWithValue(result)) {
          showToast({
            type: 'error',
            message: 'Failed to create community',
          });
        } else {
          showToast({
            type: 'success',
            message: 'Community created successfully',
          });
          navigation.navigate('MyCommunities');
        }
      } catch (error) {
        showToast({
          type: 'error',
          message: 'Failed to create community',
        });
      }
    } else if (type === 'add') {
      navigation.navigate('CreateCommunity');
      return;
    }
  };

  const updateCommunityState = (isModeratorTitle: boolean, users: AuthorProfileI[]): void => {
    if (isModeratorTitle) {
      dispatch(setModerators(users));
    } else {
      dispatch(setMembers(users));
    }
  };

  return {
    handleSubmit,
    people,
    loading,
    isLoadingMore,
    loadMore,
    updateCommunityState,
  };
};

export default usePeopleList;
