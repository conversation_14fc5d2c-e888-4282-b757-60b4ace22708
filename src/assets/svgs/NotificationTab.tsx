/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React from 'react';
import { RFPercentage } from 'react-native-responsive-fontsize';
import Svg, { Path } from 'react-native-svg';
import { OutlinedIconPropsI } from './types';

const NotificationIcon: React.FC<OutlinedIconPropsI> = ({
  width = 2.356,
  height = 2.604,
  fill = 'black',
  stroke = 'white',
  ...props
}) => {
  return (
    <Svg
      width={RFPercentage(width)}
      height={RFPercentage(height)}
      viewBox="0 0 19 21"
      fill="none"
      {...props}
    >
      <Path
        d="M18.7541 15.4629L18.7542 15.4629C18.8913 15.6981 18.964 15.9653 18.965 16.2375C18.966 16.5097 18.8952 16.7774 18.7597 17.0135C18.6243 17.2497 18.429 17.4459 18.1935 17.5826C17.958 17.7192 17.6907 17.7913 17.4185 17.7917H13.3793H13.3508L13.345 17.8196C13.164 18.7057 12.6825 19.5019 11.9819 20.0738C11.2814 20.6456 10.4048 20.958 9.50049 20.958C8.59618 20.958 7.71962 20.6456 7.01908 20.0738C6.31853 19.5019 5.837 18.7057 5.65594 17.8196L5.65022 17.7917H5.62165H1.58249C1.31017 17.7915 1.04273 17.7195 0.807122 17.5829C0.571515 17.4464 0.376078 17.2501 0.240519 17.014C0.104961 16.7778 0.0340743 16.51 0.0350091 16.2377C0.035944 15.9654 0.108667 15.6982 0.245844 15.4629L0.245878 15.4629C0.799263 14.5097 1.61746 11.8267 1.61746 8.32502C1.61746 6.23431 2.44799 4.22923 3.92635 2.75087C5.4047 1.27251 7.40978 0.441982 9.50049 0.441982C11.5912 0.441982 13.5963 1.27251 15.0746 2.75087C16.553 4.22923 17.3835 6.23431 17.3835 8.32502C17.3835 11.8257 18.2007 14.5097 18.7541 15.4629ZM7.26167 17.7917H7.21217L7.22867 17.8383C7.39478 18.3082 7.70243 18.715 8.10927 19.0027C8.51612 19.2905 9.00215 19.4451 9.50048 19.4453C9.99884 19.4451 10.4849 19.2905 10.8917 19.0027C11.2986 18.715 11.6062 18.3082 11.7723 17.8383L11.7888 17.7917H11.7393H7.26167ZM1.5522 16.2255L1.52162 16.278H1.58246H17.4185H17.4794L17.4488 16.2254C16.6889 14.9192 15.8699 11.8894 15.8699 8.32502C15.8699 6.63574 15.1989 5.01565 14.0044 3.82115C12.8099 2.62665 11.1898 1.95559 9.50049 1.95559C7.81122 1.95559 6.19113 2.62665 4.99663 3.82115C3.80213 5.01565 3.13107 6.63574 3.13107 8.32502C3.13107 11.8924 2.31016 14.9222 1.5522 16.2255Z"
        fill={fill}
        stroke={stroke}
        strokeWidth={0.07}
      />
    </Svg>
  );
};

export default NotificationIcon;
