import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useSelector } from 'react-redux';
import SafeArea from '@/src/components/SafeArea';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { ProfileStackParamsListI } from '@/src/navigation/types';
import EditEducationItem from './components/EditEducationItem';

type RouteProps = RouteProp<ProfileStackParamsListI, 'EditEducationItem'>;

const EditEducationItemScreen = () => {
  const route = useRoute<RouteProps>();
  const { educationId } = route.params || {};
  const { profileId } = useSelector(selectCurrentUser);
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();

  return (
    <SafeArea>
      <EditEducationItem
        onBack={navigation.goBack}
        profileId={profileId!}
        educationId={educationId}
      />
    </SafeArea>
  );
};

export default EditEducationItemScreen;
