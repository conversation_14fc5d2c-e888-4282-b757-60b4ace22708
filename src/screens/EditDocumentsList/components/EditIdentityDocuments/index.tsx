import { ActivityIndicator, FlatList, Pressable, Text, View } from 'react-native';
import CustomModal from '@/src/components/Modal';
import NotFound from '@/src/components/NotFound';
import EditPencil from '@/src/assets/svgs/EditPencil';
import DeleteIcon from '@/src/assets/svgs/TrashBin';
import type { IdentityI } from '../EditDocumentationList/types';
import { findValidity } from '../utils/findValidity';
import { useEditIdentityDocument } from './useHook';

export const EditIdentityDocuments = ({
  profileId,
  editable,
}: {
  profileId: string;
  editable: boolean;
}) => {
  const {
    onEditDocument,
    onDeleteDocument,
    isVisible,
    setIsVisible,
    setDeleteDocumentId,
    isDeleting,
    loading,
    loadingMore,
    handleLoadMore,
    documents,
  } = useEditIdentityDocument(profileId);

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#448600" />
      </View>
    );
  }

  const renderItem = ({ item, index }: { item: IdentityI; index: number }) => {
    const isLast = index === documents.length - 1;
    const validity = findValidity(item.untilDate, !item.type);
    const isExpired = Array.isArray(validity) && validity.length === 0;
    const textColor = isExpired ? 'text-gray-400' : 'text-black';

    let expiryText = 'Expired';
    if (Array.isArray(validity) && validity.length > 0) {
      if (validity[0] === 'Unlimited') {
        expiryText = 'Unlimited validity';
      } else {
        const [value, unit] = validity as [number, 'Y' | 'M' | 'D'];
        if (unit === 'Y') {
          expiryText = `${value} year(s)`;
        } else if (unit === 'M') {
          expiryText = `${value} month(s)`;
        } else {
          expiryText = `${value} day(s)`;
        }
      }
    }

    return (
      <View className={`py-4 ${isLast ? '' : 'border-b border-[#E5E5E5]'}`}>
        <Text className={`text-base font-medium ${textColor}`}>{item.type}</Text>
        <View className="flex-row justify-between mt-2 items-center">
          <Text className={`text-sm mt-1 ${textColor}`}>
            {item.country.name} | {expiryText}
          </Text>
          {editable && (
            <View className="flex-row gap-1 items-center">
              <Pressable onPress={() => onEditDocument(item.id)} className="p-2">
                <EditPencil width={2.3} height={2.3} />
              </Pressable>
              <Pressable
                onPress={() => {
                  setDeleteDocumentId(item.id);
                  setIsVisible(true);
                }}
                className="p-2"
              >
                <DeleteIcon width={2.3} height={2.3} />
              </Pressable>
            </View>
          )}
        </View>
      </View>
    );
  };

  const renderFooter = () =>
    loadingMore ? (
      <View className="py-4 items-center">
        <ActivityIndicator size="small" color="#448600" />
      </View>
    ) : null;

  return documents.length === 0 ? (
    <NotFound />
  ) : (
    <>
      <FlatList
        data={documents}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.3}
        ListFooterComponent={renderFooter}
        contentContainerStyle={{
          flexGrow: 1,
          backgroundColor: 'white',
          paddingVertical: 20,
        }}
        showsVerticalScrollIndicator={false}
      />
      <CustomModal
        isVisible={isVisible}
        onCancel={() => setIsVisible(false)}
        title="Are you sure you want to delete this document?"
        confirmText="Delete"
        confirmButtonVariant="danger"
        onConfirm={onDeleteDocument}
        isConfirming={isDeleting}
      />
    </>
  );
};
