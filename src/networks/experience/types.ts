import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { OprTypeE } from '@/src/redux/slices/experience/types';

export type PortVisitQueryParams = {
  profileId: string;
  page?: number;
  pageSize?: number;
};

export type TotalDataI<T> = {
  data: T[];
  total: number;
};

export type PortVisitCountry = {
  iso2: string | null;
  name: string | null;
};

export type PortVisitorFetchResultI = {
  unLocode: string | null;
  name: string;
  country: PortVisitCountry;
};

export type ExperienceFetchParamsI = {
  profileId: string;
};

export type ExperienceFetchReponseI = {
  data: ExperienceFetchForClientResultI[];
  total: number;
  portVisits: { data: PortI[]; total: number };
};

type PortI = {
  unLocode: string;
  name: string;
  country: CountryI;
};

type CountryI = {
  iso2: string;
  name: string;
};

type ExperienceFetchForClientResultI = {
  id: string;
  entity: {
    dataType?: 'raw' | 'master' | undefined;
    id: string;
    name: string;
    logo?: string;
    website?: string;
    location?: string;
  };
  years: number;
  months: number;
  dataType?: 'raw' | 'master' | undefined;
  designations?: ExperienceDesignationI[];
};

type ExperienceDesignationI = {
  dataType?: 'raw' | 'master' | undefined;
  experienceDesignationId?: string;
  fromDate?: string;
  toDate?: string;
  designation: SearchResultI;
  id?: string;
  name: string;
  from?: string;
  to?: string;
};

export type DeleteExperienceQueryI = {
  id: string;
  opr: OprTypeE;
}[];
