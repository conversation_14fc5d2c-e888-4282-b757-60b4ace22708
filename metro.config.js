const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');
const { withNativeWind } = require('nativewind/metro');
const { wrapWithReanimatedMetroConfig } = require('react-native-reanimated/metro-config');
const { withSentryConfig } = require('@sentry/react-native/metro');
const path = require('path');

const config = (() => {
  const defaultConfig = getDefaultConfig(__dirname);
  const { assetExts, sourceExts } = defaultConfig.resolver;

  return mergeConfig(defaultConfig, {
    transformer: {
      babelTransformerPath: require.resolve('react-native-svg-transformer'),
      experimentalImportSupport: false,
      inlineRequires: true,
    },
    resolver: {
      assetExts: assetExts.filter((ext) => ext !== 'svg'),
      sourceExts: [...sourceExts, 'svg'],
      alias: {
        '@': path.resolve(__dirname, './'),
      },
      platforms: ['ios', 'android', 'native', 'web'],
    },
    watchFolders: [path.resolve(__dirname, './')],
    maxWorkers: 2,
    resetCache: process.env.RESET_CACHE === 'true',
  });
})();

module.exports = withSentryConfig(
  wrapWithReanimatedMetroConfig(withNativeWind(config, { input: './styles/global.css' })),
);
