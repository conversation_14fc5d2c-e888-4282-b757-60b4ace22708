/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState, useEffect } from 'react';
import { useRoute } from '@react-navigation/native';
import { UseVotesListResultI, VotesRouteI } from './types';

const useVotesList = (): UseVotesListResultI => {
  const [loading, setLoading] = useState(true);
  const [reactions, setReactions] = useState<{ reactions: any[]; totalCount: number }>({
    reactions: [],
    totalCount: 0,
  });
  const route = useRoute<VotesRouteI>();
  const type = route.params?.type;

  useEffect(() => {
    const timer = setTimeout(() => {
      const dummyData = {
        reactions: [
          {
            Profile: {
              id: '1',
              name: type === 'upvote' ? 'Upvoter One' : 'Downvoter One',
              avatar: 'https://i.pravatar.cc/150?u=a042581f4e29026024d',
              designation: { id: '1', name: 'Captain' },
              entity: { id: '1', name: '<PERSON><PERSON>' },
            },
          },
          {
            Profile: {
              id: '2',
              name: type === 'upvote' ? 'Upvoter Two' : 'Downvoter Two',
              avatar: 'https://i.pravatar.cc/150?u=a042581f4e29026704d',
              designation: { id: '2', name: 'Chief Engineer' },
              entity: { id: '2', name: 'MSC' },
            },
          },
        ],
        totalCount: 2,
      };
      setReactions(dummyData);
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [type]);

  return {
    reactions,
    loading,
    refreshing: false,
    handleRefresh: () => {},
    handleLoadMore: () => {},
  };
};

export default useVotesList;
