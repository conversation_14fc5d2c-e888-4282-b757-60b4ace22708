/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TouchableWithoutFeedback,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import SafeArea from '@/src/components/SafeArea';
import { AppStackParamListI } from '@/src/navigation/types';
import CreateAccountForm from './components/CreateAccountForm';

type CreateAccountScreenNavigationPropI = StackNavigationProp<AppStackParamListI>;

const CreateAccountScreen = () => {
  const navigation = useNavigation<CreateAccountScreenNavigationPropI>();

  const handleSignin = () => {
    navigation.navigate('UserLogin');
  };

  return (
    <SafeArea>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          style={{ flex: 1 }}
        >
          <ScrollView contentContainerStyle={{ flexGrow: 1 }} keyboardShouldPersistTaps="handled">
            <CreateAccountForm onSignIn={handleSignin} />
          </ScrollView>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </SafeArea>
  );
};

export default CreateAccountScreen;
